<script>
	import {
		mapActions,
		mapMutations
	} from 'vuex'
	import {
		getConfig
	} from '@/api/app'
	import {
		strToParams
	} from '@/utils/tools'
	import {
		bindSuperior
	} from '@/api/user'
	import {INVITE_CODE} from '@/config/cachekey'
	import Cache from '@/utils/cache'
	export default {
		onLaunch: function(options) {
			// 获取配置
			this.getConfigFun()
			//this.getUser()
			this.getSystemInfo()
			this.updateApp();//小程序版本更新检查
			this.checkAppUpdate();
			// Initialize i18n with the stored language preference
			const storedLanguage = Cache.get('LANGUAGE');
			if (storedLanguage) {
				this.$i18n.locale = storedLanguage;
			} else {
				// Set English as default language
				this.$i18n.locale = 'en';
				Cache.set('LANGUAGE', 'en');
			}
		},
		onShow: function(options) {
			console.log(options)
			this.bindCode(options)
		},
		onHide: function() {

		},
		methods: {
			...mapActions(['getSystemInfo', 'getUser', 'wxShare', 'initLocationFunc']),
			...mapMutations(['setConfig']),

			checkAppUpdate(){
				// #ifdef APP-PLUS
				const that = this; // 保存 this 引用
				plus.runtime.getProperty(plus.runtime.appid, function(widgetInfo) {
					uni.request({
						url: 'https://api.aichatgpt.net.cn/api/setting/update',
						data: {
							version: widgetInfo.version,
							name: widgetInfo.name
						},
						success: (result) => {
							var data = result.data;
							if (data.update && data.wgtUrl) {
								// 添加用户确认提示
								uni.showModal({
									title: that.$t('appUpdate.newVersion'),
									content: data.note || that.$t('appUpdate.updatePrompt'),
									success: (res) => {
										if (res.confirm) {
											// 用户点击确认，开始下载更新
											// 使用Toast显示下载开始
											uni.showToast({
												title: that.$t('appUpdate.downloading') + ' 0%',
												icon: 'none',
												duration: 2000,
												mask: true
											});

											const downloadTask = uni.downloadFile({
												url: data.wgtUrl,
												success: (downloadResult) => {
													// 下载完成，隐藏可能存在的提示
													uni.hideToast();

													if (downloadResult.statusCode === 200) {
														// 安装前再次确认
														uni.showModal({
															title: that.$t('appUpdate.downloadComplete'),
															content: that.$t('appUpdate.installPrompt'),
															success: (res) => {
																if (res.confirm) {
																	plus.runtime.install(downloadResult.tempFilePath, {
																		force: false
																	}, function() {
																		console.log('install success...');
																		plus.runtime.restart();
																	}, function(error) {
																		console.error('install fail...', error);
																		uni.showToast({
																			title: that.$t('appUpdate.installFailed'),
																			icon: 'none'
																		});
																	});
																}
															}
														});
													}
												},
												fail: (error) => {
													uni.hideLoading();
													console.error('download fail...', error);
													uni.showToast({
														title: that.$t('appUpdate.downloadFailed'),
														icon: 'none'
													});
												}
											});

											// 监听下载进度
											let lastToastTime = Date.now();
											downloadTask.onProgressUpdate((res) => {
												// 每10%更新一次，或者每2秒至少更新一次
												const now = Date.now();
												if (res.progress % 10 === 0 || now - lastToastTime > 2000) {
													lastToastTime = now;
													// 使用Toast显示进度，不会闪烁
													uni.showToast({
														title: that.$t('appUpdate.downloading') + ' ' + res.progress + '%',
														icon: 'none',
														duration: 2000,
														mask: true
													});
												}
											});
										} else {
											// 用户点击取消，不进行更新
											console.log('用户取消更新');
										}
									}
								});
							}
						}
					});
				});
				// #endif
			},
			async getConfigFun() {
				const {
					code,
					data
				} = await getConfig()
				if (code == 1) {
					this.setConfig(data)
					if(data.is_open_nearby) {
						// 获取定位
						this.initLocationFunc()
					}
					//setTabbar()
					// 防止第一次调用时拿不到数据
					this.wxShare()
				}

			},
			async bindCode(options) {
				if(!options.query) return
				let invite_code = options.query.invite_code || strToParams(decodeURIComponent(options.query.scene))
					.invite_code
				if (invite_code) {
					const {
						data,
						code
					} = await bindSuperior({
						code: invite_code
					})
					if (code == -1) {
						Cache.set(INVITE_CODE, invite_code)
					}
				}
			},
			//小程序版本更新检查
			updateApp(){
				const updateManager = uni.getUpdateManager();
				const that = this; // 保存 this 引用
				updateManager.onCheckForUpdate(function (res) {
				    // 请求完新版本信息的回调
				    //console.log("是否有最新版本，",res.hasUpdate);
				});
				updateManager.onUpdateReady(function (res) {
				    uni.showModal({
						title: that.$t('appUpdate.updateTip'),
						content: that.$t('appUpdate.restartPrompt'),
						success(res) {
						    if (res.confirm) {
								// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
								updateManager.applyUpdate();
						    }
						}
				    });
				});
				updateManager.onUpdateFailed(function (res) {
				    // 新的版本下载失败
				});
			},
		}
	}
</script>
<style lang="scss">
	@import 'styles/common.scss';
	@import "components/uview-ui/index.scss";
	@import url('@/plugin/emoji-awesome/css/apple.css');
	/*每个页面公共css */
</style>
