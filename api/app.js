import request from '@/utils/request'
import wechath5 from '@/utils/wechath5'
import {client} from '@/utils/tools'

//小程序授权登录
export function authLogin(data) {
	return request.post('account/authLogin', data);
}
//小程序静默登录
export function silentLogin(data) {
	return request.post('account/silentLogin', data);
}

// app登录
export function opLogin(data) {
	return request.post('account/uinAppLogin', {...data, client});
}

// 拉取用户数据
export function pullUser(data) {
	return request.post('user/getPullUserInfo',{...data})
}


//预支付接口
export function prepay(data) {
	return request.post('pay/unifiedpay', {...data,});
}

//小程序订阅
export function getMnpNotice(data) {
	return request.get("subscribe/lists", {
		params: data
	});
}

//账号登录
export function accountLogin(data) {
    return request.post("account/login", {...data, client})
}



// 登录
export function wechatLogin(data) {
	return request.post('account/oalogin', data)
}

// 向微信请求code的链接
export function getCodeUrl() {
	return request.get('account/codeurl', {
		params:{
			url: encodeURIComponent(location.href)
		}
	});
}



//微信sdk配置
export function getJsconfig() {
	return request.get('wechat/jsconfig', {

		params: {
			url: encodeURIComponent(wechath5.signLink())
		}
	});
}

// 忘记密码
export function forgetPwd(data) {
    return request.post('login_password/forget', {...data, client})
}

// 设置交易密码
export function setPayPassword(data) {
    return request.post('user/updatePaypassword', {...data, client})
}

export function updatePayPassword(data) {
    return request.post('user/setPayPassword', {...data, client})
}


// 发送短信
export function sendSms(data) {
    return request.post('sms/send', {...data, client})
}

// 获取图形验证码
export function getCaptcha() {
    return request.post('Account/getCaptcha')
}

// Html5 注册账号
export function register(data) {
    return request.post('account/register', {...data, client})
}

// 获取服务协议
export function getServerProto() {
    return request.get("policy/service")
}

// 获取隐私政策
export function getPrivatePolicy() {
    return request.get("policy/privacy")
}

// 获取售后保障
export function getAfterSaleGuar() {
    return request.get("policy/afterSale")
}

//客服
export function getService() {
    return request.get("setting/getPlatformCustomerService")
}


//客服配置
export function getChatConfig(params) {
    return request.get("index/chatConfig", { params })
}


// 足迹气泡
export function getBubbleLists() {
    return request.get("footprint/lists")
}



// 验证码登录
export function smsCodeLogin(data) {
    return request.post('account/smsLogin', {...data, client})
}
export function getConfig() {
    return request.get("index/config")
}


// 注册赠送优惠券
export function getRegisterCoupon() {
    return request.get('coupon/registerSendCoupon')
}

// 获取支付配置
export function getPayway(params) {
    return request.get('order/getPayWay', { params })
}

// 小程序码
export function apiMnpQrCode(params) {
    return request.get('share/getMnQrcode', { params })
}

// 绑定银行卡验证码
export function paycardbind(params){
	return request.post('user/paycardbind', { params })
}

// 确认绑定银行卡
export function paycardbindconfirm(params){
	return request.post('user/paycardbindconfirm', { params })
}

//银行卡列表
 export function paycardList(params){
	 return request.post('user/paycardList', { params })
 }

 // 支付宝授权
 export function authorization(params){
	return request.get('user/authorization', { params })
 }