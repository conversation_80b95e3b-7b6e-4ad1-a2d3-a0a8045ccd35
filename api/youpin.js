import request from '../utils/request'
import {client} from '@/utils/tools'

//推客系统
export function showcaseList(params){
	return request.post('user/showcaseList', params)
}

//我的团队
export function myTeam(params){
	return request.post('/user/myTeam', params)
}

//展开下级团队
export function expandChild(params){
	return request.post('/user/expandChild', params)
}

//绑定橱窗
export function bindShowcase(params){
	return request.post('/user/bindShowcase', params)
}

//销售排行榜
export function xiaofeiyejidata(params){
	return request.post('user/xiaofeiyejidata', params)
}

//绑定上级
export function distribution(params){
	return request.post('distribution/code', params)
}

// 橱窗详情
export function navigation_detail(data) {
    return request.get('help/navigation_detail', {
        params: { id: data.id }
    })
}

// 确认绑定银行卡
export function bankadd(params){
	return request.post('bank/add', { params })
}
//银行卡列表
 export function banklists(params){
	 return request.get('bank/lists', {
	     params: { params }
	 })
 }
 // 删除银行卡
 export function bankdel(params){
 	return request.post('bank/del', params)
 }

//个人中心签到
export function signRelease(params){
 	return request.post('user/signRelease', params)
 }

//积分兑换购物券
export function ExchangeCoin(params){
 	return request.post('user/ExchangeCoin', params)
 }
