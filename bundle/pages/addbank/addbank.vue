<template>
	<view>
		<view class="container">
			<u-form :model="form" ref="uForm" :rules="rules" labelWidth='200rpx'>
				<u-form-item label="绑卡机构" leftIcon='list' class='item' @click.native='choosePayNameShow = true'>
					<u-input v-model="form.payName" placeholder="请选择绑卡机构" disabled disabledColor="white"
						suffix-icon="arrow-right" />
				</u-form-item>
				<u-form-item v-if="form.payName == '汇聚快捷'" label="卡片类型" leftIcon='list-dot' class='item' @click.native="choosePayTypeShow = true">
					<u-input v-model="form.payType" placeholder="请选择绑卡机构" disabled disabledColor="white"
						suffix-icon="arrow-right" />
				</u-form-item>
				<u-form-item label="姓名" prop="name" leftIcon='man-add-fill' class='item'>
					<u-input v-model="form.name" placeholder="请输入实名" suffix-icon="arrow-right" />
				</u-form-item>
				<u-form-item label="身份证号" prop="idCard" leftIcon='minus-square-fill' class='item'>
					<u-input v-model="form.idCard" type="idcard" placeholder="请输入身份证号码"
						suffix-icon="arrow-right" />
				</u-form-item>
				<u-form-item label="银行名称" prop="bankName" labelWidth='200rpx' leftIcon='home-fill' class='item'>
					<u-input v-model="form.bankName" placeholder="请输入银行名及支行名"
						suffix-icon="arrow-right" />
				</u-form-item>
				<u-form-item label="银行卡号" prop="bankId" labelWidth='200rpx' leftIcon='coupon-fill' class='item'>
					<u-input v-model="form.bankId" type="number" placeholder="请输入银行卡号"
						suffix-icon="arrow-right" />
				</u-form-item>
				<u-form-item label="预留手机" prop="phone" labelWidth='200rpx' leftIcon='phone-fill' class='item'>
					<u-input v-model="form.phone" type="number" placeholder="请输入银行预留手机号"
						suffix-icon="arrow-right" />
				</u-form-item>
				<u-form-item v-if="form.payType == '信用卡'" label="卡有效期" prop="validTimeEnd" labelWidth='200rpx'
					leftIcon='calendar-fill' class='item'>
					<u-input v-model="form.validTimeEnd" placeholder="请输入信用卡有效期(年/月)"
						suffix-icon="arrow-right" />
				</u-form-item>
				<u-form-item v-if="form.payType == '信用卡'" label="卡校验码" prop="cvv" labelWidth='200rpx' leftIcon='scan'
					class='item'>
					<u-input v-model="form.cvv" placeholder="请输入信用卡背面校验码后三位" suffix-icon="arrow-right" />
				</u-form-item>
				<u-form-item label="验证码" labelWidth='200rpx' leftIcon='clock-fill' class='item'>
					<view class="flex">
						<u-input v-model="form.smscode" type="number" placeholder="请输入验证码" />
						<view style="flex: 1;">
							<u-verification-code ref="uCode" @change="codeChange" seconds="120" changeText="X秒重新获取"></u-verification-code>
							<u-button class="codebtn" @click="getCode" color='#ff5703' type="warning">{{tips}}</u-button>
						</view>
					</view>
				</u-form-item>
				<u-form-item labelWidth='40rpx' class='item'>
					<view style="font-size: 12px;padding:20rpx 0;color:#a5a5a5">
						<text class="mr" style="color:red">*</text>填写银行名称请务必填写准确 如:XX银行XX省XX市XX区XX支行
					</view>
				</u-form-item>
			</u-form>
		</view>
		<view class="bind" @click="bindCard">确认绑卡</view>
		<u-picker v-model="choosePayNameShow" mode="selector" :default-selector="[0]" :range="payNamecolumns" @cancel='close' confirmColor='#ff5703'
			@confirm='payNameconfirm'></u-picker>
		<u-picker v-model="choosePayTypeShow" mode="selector" :default-selector="[0]" :range="payTypecolumns" @cancel='close' confirmColor='#ff5703'
			@confirm='payTypeconfirm'></u-picker>
	</view>
</template>
<script>
	import {paycardbind,paycardbindconfirm} from '@/api/app.js'
	
	export default {
		onLoad() {},
		data() {
			return {
				list: ['借记卡', '信用卡'],
				bankType: '借记卡',
				choiceBankType: true, //true为借记卡 false 为信用卡
				choosePayNameShow: false,
				choosePayTypeShow: false,
				tips: '获取短信验证码',
				payNamecolumns: ['汇聚快捷'],
				payTypecolumns: ['借记卡', '信用卡'],
				smsObj: null,
				form: {
					name: '',
					idCard: '',
					bankId: '',
					phone: '',
					bankName: '',
					payName: '汇聚快捷',
					payType: '借记卡',
					validTimeEnd: '',
					cvv: ''
				},
				rules: {
					name: [{
						required: true,
						message: '请输入正确的持卡人姓名',
						trigger: ['blur', 'change']
					}],
					bankName: [{
						required: true,
						message: '请输入银行名称及支行名称',
						trigger: ['blur', 'change']
					}],
					idCard: [{
						required: true,
						message: '请输入身份证号码',
						trigger: ['blur', 'change']
					},
					{
						// 自定义验证函数，见上说明
						validator: (rule, value, callback) => {
							// 上面有说，返回true表示校验通过，返回false表示不通过
							// uni.$u.test.mobile()就是返回true或者false的
							return uni.$u.test.idCard(value);
						},
						message: '身份证号码不正确',
						// 触发器可以同时用blur和change
						trigger: ['change', 'blur'],
					}
					],
					bankId: [{
						required: true,
						message: '请输入银行卡号',
						trigger: ['blur', 'change']
					}],
					phone: [{
							required: true,
							message: '请输入银行预留手机',
							trigger: ['blur', 'change']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// uni.$u.test.mobile()就是返回true或者false的
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change', 'blur'],
						}
					],
					validTimeEnd: [{
						required: true,
						message: '请输入信用卡有效期',
						trigger: ['blur', 'change']
					}],
					cvv: [{
						required: true,
						message: '请输入信用卡后三位校验码',
						trigger: ['blur', 'change']
					}],
				}
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		methods: {
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					this.getSmsCode();
				} else {
					uni.$u.toast('请在倒计时结束后再点击!');
				}
			},
			getSmsCode() {
				this.$refs.uForm.validate().then(res => {
					if(res){
							this.$refs.uCode.start();
							paycardbind({
								card_id: this.form.bankId,
								realname: this.form.name,
								cardcode: this.form.idCard,
								tel_no: this.form.phone,
								bank_name: this.form.bankName,
								lx: this.form.payName == '汇聚快捷' ? 1 : 6,
								cvv: this.form.cvv,
								validTimeEnd: this.form.validTimeEnd
							}).then(result => {
								console.log(result)
								this.smsObj = result.data;
								console.log(this.smsObj)
							})
					}else{
						uni.$u.toast('请检查信息是否完整!')
					}
				})

			},
			close() {
				this.choosePayNameShow = false;
				this.choosePayTypeShow = false;
			},
			payNameconfirm(e) {
				let index = e[0]
				this.choosePayNameShow = false;
				this.form.payName = this.payNamecolumns[index];
			},
			payTypeconfirm(e) {
				let index = e[0]
				this.choosePayTypeShow = false;
				this.form.payType = this.payTypecolumns[index];
			},
			bindCard() {
				if (!uni.$u.test.isEmpty(this.form.smscode)) {
					paycardbindconfirm({
						lx: 1,
						sms_code: this.form.smscode,
						mch_order_no: this.smsObj.mch_order_no,
						paycardid: this.smsObj.paycardid
					}).then(res => {
						this.$u.toast(res.msg);
						setTimeout((rs) => {
							uni.navigateBack();
						}, 1000)
					})
				} else {
					uni.$u.toast('请先填写信息,获取验证码!')
				}

			}
		}
	}
</script>

<style lang="scss" scoped>
	/deep/ .u-form-item{
		/* #ifdef MP-WEIXIN */
		background-color: #fff;
		padding: 15rpx 25rpx !important;
		margin-bottom: 15rpx !important;
		border-radius: 20rpx;
		/* #endif */
	}
	page {
		background-color: #eee;
	}

	.container {
		margin: 20rpx 40rpx;
	}

	.item {
		/* #ifndef MP-WEIXIN */
		background-color: white;
		padding: 10rpx 25rpx;
		border-bottom: 1rpx solid #eee;
		position: relative;
		border-radius: 20rpx;
		margin-bottom: 15rpx;
		/* #endif */
		
		.flex{
			.codebtn{
				height: 82rpx;
				color: #fff;
				background-color: $-color-primary;
			}
		}
	}

	.item:nth-child(2) {
		margin-bottom: 20rpx;
	}
	
	
	.container /deep/ .u-form-item__body__right__message {
		position: absolute;
		bottom: 5rpx;
		left: 45rpx;
		z-index: 100;
	}

	.bind {
		// position: absolute;
		// bottom: 100rpx;
		// left: 25rpx;
		margin: 0 auto;
		width: 700rpx;
		height: 90rpx;
		background: $-color-primary;
		border-radius: 100rpx;
		line-height: 90rpx;
		font-size: 32rpx;
		text-align: center;
		color: white;
		font-weight: 600;
		letter-spacing: 5rpx;
	}

	.cardType {
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
		width: 100%;
		padding: 0 20rpx;
		font-size: 24rpx;
	}
</style>