<template>
    <view class="download">
        <image class="logo" src="@/static/images/logo.png" mode="widthFix"></image>
        <view class="title">{{ $t('download.title') }}</view>
        <view class="description">{{ $t('download.description') }}</view>
        <view class="download-btn" @tap="handleDownload">
            {{ $t('download.downloadButton') }}
        </view>
        <image class="sceenshot" src="@/static/images/download.png" mode="widthFix"></image>

        <!-- 微信环境引导遮罩层 -->
        <view class="wechat-mask" v-if="isWechat">
            <view class="mask-content">
                <image class="guide-image" src="@/static/images/guide_browser.jpg" mode="widthFix"></image>
            </view>
        </view>
    </view>
</template>

<script>
	import {
        getCopyright
	} from "@/api/user";
import { mapGetters } from 'vuex';
import pageTitleMixin from '@/mixins/page-title-mixin';
import { isWeixinClient } from '@/utils/tools';

    export default {
        mixins: [pageTitleMixin],
        data() {
            return {
                images: [],
                isWechat: false
            }
        },

        methods: {
            handleDownload() {
                // #ifdef H5
                    window.location.href = 'https://api.aichatgpt.net.cn/uploads/apk/chatGPT.apk';
                // #endif
            },
            getCopyrightFunc(id) {
                getCopyright({shop_id: id}).then(res => {
                    this.images = res.data
                })
            },

            viewImage(current) {
                uni.previewImage({
                    current,
                    urls: this.images// 需要预览的图片http链接列表
                });
            },

            // 检测是否为微信环境
            checkWechatEnvironment() {
                // #ifdef H5
                this.isWechat = isWeixinClient();
                // #endif
            }
        },

        computed: {
            ...mapGetters(['currentLanguage'])
        },

        onLoad() {
            const id = this.$Route.query.id;
            this.getCopyrightFunc(id);
            this.checkWechatEnvironment();
        }
    }
</script>

<style lang="scss">
    .download {
        padding: 30rpx;
        text-align: center;
        .logo {
            width: 196rpx;
            height: 196rpx;
        }
        .title {
            font-size: 38rpx;
            color: $-color-primary;
            margin-top: 20rpx;
        }
        .description {
            font-size: 28rpx;
            color: #90BAEB;
            margin-top: 20rpx;
        }
        .download-btn {
            margin-top: 60rpx;
            display: inline-block;
            width: 360rpx;
            height: 88rpx;
            line-height: 88rpx;
            font-size: 38rpx;
            color: #1D1D3B;
            background-color: $-color-primary;
            cursor: pointer;
            border-radius: 44rpx;
        }
        .sceenshot {
            width: 624rpx;
            margin-top: 60rpx;
        }

        /* 微信环境引导遮罩层样式 */
        .wechat-mask {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 999;
            display: flex;
            justify-content: center;
            align-items: center;

            .mask-content {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: rgba(255, 255, 255, 1);

                .guide-image {
                    width: 100%;
                    max-width: 600rpx;
                }
            }
        }
    }
</style>
