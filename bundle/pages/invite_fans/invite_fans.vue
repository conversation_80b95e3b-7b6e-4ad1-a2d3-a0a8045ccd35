<template>
    <view class="pages">
        <view class="invite-fans flex-col col-center">
            <image :src="haibao" mode="widthFix" class="poster"></image>
            <!-- <invite-poster :config="{
    avatar: userInfo<strong>.</strong>avatar,
    nickname: userInfo.nickname,
    code: userInfo.sn,
    link: link,
    qrCode: qrCode,
    poster: poster
   }" @success="handleSuccess" /> -->
            <view class="footer flex-1">
                <view class="m-b-40">
                    <view class="m-b-10 sm lighter">{{ $t('inviteFans.myInviteCode') }}</view>
                    <view class="flex row-between invite-code">
                        <view class="font-size-30 invite-code-text">{{ userInfo.sn }}</view>
                        <view class="sm m-r-30 copy-btn" @tap="onCopy(userInfo.sn)">{{ $t('inviteFans.clickToCopy') }}
                        </view>
                    </view>
                </view>
                <view class="m-b-40">
                    <view class="m-b-10 sm lighter">{{ $t('inviteFans.inviteLink') }}</view>
                    <view class="flex row-between invite-code">
                        <view class="font-size-30 invite-code-text flex-1 p-r-20"><input type="text" :value="link"
                                disabled style="width: 100%;"></view>
                        <view class="sm m-r-30 copy-btn" @tap="onCopy(link)">{{ $t('inviteFans.clickToCopy') }}</view>
                    </view>
                </view>
                <!-- #ifndef H5  -->
                <button class="save-btn br60" size="lg" @tap="saveImageToAlbum">{{ $t('inviteFans.saveToAlbum')
                    }}</button>
                <!-- #endif -->
                <!-- #ifdef H5 -->
                <button class="save-btn br60" size="lg">{{ $t('inviteFans.longPressSaveToAlbum') }}</button>
                <!-- #endif -->
            </view>
        </view>
        <loading-view v-show="loading"></loading-view>
    </view>
</template>

<script>
import {
    apiMnpQrCode
} from '@/api/app'
import {
    baseURL,
    basePath
} from '@/config/app'
import {
    mapGetters
} from 'vuex'

import {
    apiDistributionPoster,
    getUserPoster,
    getUserInfo
} from '@/api/user'

import {
    copy
} from '@/utils/tools.js'
import pageTitleMixin from '@/mixins/page-title-mixin'
export default {
    mixins: [pageTitleMixin],
    data() {
        return {
            path: '',
            qrCode: '', //生成的小程序码
            loading: true,
            showPoster: false,
            poster: '', //背景图
            haibao: "",//海报
            userInfo: {}
        };
    },

    async onLoad() {
        await this.getPoster()



        // #ifdef APP-PLUS || H5
        this.showPoster = true
        // #endif
        getUserInfo().then(res => {
            this.userInfo = res.data
             // #ifdef MP-WEIXIN
            this.getMnpQrCode()
            // #endif
            getUserPoster({ url: `/mobile/bundle_c/pages/register/register?invite_code=${this.userInfo.sn}` }).then(res => {
                this.haibao = res.data.url
                this.loading = false
            })
        })

    },

    methods: {
        onCopy(text) {
            copy(text, this.$t('common.copySuccess'))
        },
        async getPoster() {
            const res = await apiDistributionPoster()
            this.poster = res.data.poster
        },
        getMnpQrCode() {
            apiMnpQrCode({
                type: 0,
                url: `/mobile/bundle_c/pages/register/register?invite_code=${this.userInfo.sn}`
            }).then(res => {
                console.log(res)
                this.qrCode = res.data.qr_code
                this.showPoster = true
            })
        },
        saveImageToAlbum() {
            // #ifndef H5
            uni.downloadFile({
                url: this.haibao, // 替换为网络图片的地址
                success: (res) => {
                    if (res.statusCode === 200) {
                        uni.saveImageToPhotosAlbum({
                            filePath: res.tempFilePath, // 使用下载到的本地路径
                            success: res => {
                                this.$toast({
                                    title: this.$t('inviteFans.saveSuccess')
                                });
                            },
                            fail: err => {
                                this.$toast({
                                    title: this.$t('inviteFans.saveFailed')
                                });
                            }
                        });
                    } else {
                        console.error('下载失败，状态码：', res.statusCode);
                    }
                },
                fail(err) {
                    console.error('下载失败：', err);
                }
            });
            /* uni.saveImageToPhotosAlbum({
                filePath: this.path,
                success: res => {
                    this.$toast({
                        title: "保存成功"
                    });
                },
                fail: err => {
                    this.$toast({
                        title: '保存失败'
                    });
                }
            }); */
            // #endif

            // #ifdef H5
            this.$toast({
                title: this.$t('inviteFans.pleaseLongPressToSave')
            })
            // #endif

        },
        handleSuccess(val) {
            this.path = val
            this.loading = false
        }
    },
    computed: {
        ...mapGetters(['inviteCode', 'currentLanguage']),
        link() {
            //return `${baseURL}${basePath}?invite_code=${this.inviteCode}`
            return `${baseURL}${basePath}/bundle_c/pages/register/register?invite_code=${this.userInfo.sn}` //inviteCode
        }
    }
};
</script>
<style lang="scss">
page {
    padding: 0
}

.invite-fans {
    overflow: hidden;

    .poster {
        width: 600rpx;
        margin: 40rpx 0;
    }

    .footer {
        padding: 30rpx;
        width: 100%;
        position: fixed;
        bottom: 0;
        z-index: 100;

        .lighter {
            display: flex;
            align-items: center;
            color: #fff;

            &::before {
                content: '';
                display: block;
                width: 1px;
                height: 20rpx;
                background-color: #fff;
                margin-right: 10rpx;
            }
        }

        .invite-code-text {
            color: $-color-primary;
            margin-left: 24rpx;
        }

        .invite-code {
            height: 88rpx;
            background-image: url('@/static/images/<EMAIL>');
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }

    .copy-btn {
        color: $-color-primary;
    }

    .save-btn {
        color: #1D1D3B;
        height: 88rpx;
        line-height: 88rpx;
        background-color: $-color-primary;
        border-radius: 0;
    }
}
</style>
