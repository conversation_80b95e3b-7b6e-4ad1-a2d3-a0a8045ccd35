<template>
  <view class="language-settings">

    <view class="content">
      <view class="section">
        <view class="section-title">{{ $t('settings.currentLanguage') }}</view>
        <view class="language-list">
          <view
            class="language-item"
            v-for="(lang, index) in languages"
            :key="index"
            @tap="selectLanguage(lang.value)"
          >
            <view class="language-item-content">
              <text class="language-name">{{ lang.label }}</text>
              <u-icon v-if="currentLanguage === lang.value" name="checkmark" color="#04F9FC" size="32"></u-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';

let systemInfo = uni.getSystemInfoSync();

export default {
  data() {
    return {
      statusBarHeight: systemInfo.statusBarHeight,
      languages: [
        { label: 'English', value: 'en' },
        { label: '简体中文', value: 'zh-CN' },
        { label: '日本語', value: 'ja' },
        { label: '한국어', value: 'ko' },
        { label: 'Русский', value: 'ru' },
        { label: 'Español', value: 'es' },
        { label: 'Deutsch', value: 'de' },
        { label: 'Français', value: 'fr' }
      ]
    };
  },
  computed: {
    ...mapGetters(['currentLanguage'])
  },
  methods: {
    ...mapActions(['changeLanguage']),
    goBack() {
      uni.navigateBack();
    },
    selectLanguage(lang) {
      if (this.currentLanguage !== lang) {
        this.$i18n.locale = lang;
        this.changeLanguage(lang);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.language-settings {
  min-height: 100vh;
  background-color: #f5f5f5;

  .header {
    background-color: #fff;

    &-content {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 30rpx;

      .back-icon, .placeholder {
        width: 60rpx;
      }

      .title {
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }

  .content {
    padding: 30rpx;

    .section {
      background-color: #fff;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 30rpx;

      &-title {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 20rpx;
      }

      .language-list {
        .language-item {
          padding: 30rpx 0;
          border-bottom: 1px solid #eee;

          &:last-child {
            border-bottom: none;
          }

          &-content {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            .language-name {
              font-size: 28rpx;
            }
          }
        }
      }
    }
  }
}
</style>
