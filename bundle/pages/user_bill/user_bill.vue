<!-- 账户明细 -->

<template>
	<view class="user-bill">
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @up="upCallback" :up="upOption" @down="downCallback">
			<u-tabs :list="tabsList" :is-scroll="false" :current="currentTab" :bold="false" :active-color="'#04F9FC'"
				inactiveColor="#CDE1F8" bgColor="transparent" @change="changeTab" />
			<view class="p-t-20">
				<view class="record-list" v-for="(item, index) in list" :key="index">
					<!-- <record-cell :remark="item.source_type" :date="item.create_time_format" :money="item.change_amount_format" :type="item.change_type" /> -->
					<view class="record-cell flex row-between">
						<!-- Left -->
						<view>
							<view class="remark md">{{ item.source_type }}</view>
							<view class="time m-t-10 muted sm">{{ item.create_time_format }}</view>
						</view>

						<!-- Right -->
						<view class="black">
							<view class="money lg" :class="{ primary: item.change_type == 1 }">{{
								item.change_amount_format }}</view>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
import { getAccountLog } from "@/api/user";
import { mapGetters } from 'vuex';
import pageTitleMixin from '@/mixins/page-title-mixin'
export default {
	mixins: [MescrollMixin, pageTitleMixin], // 使用mixin
	data() {
		return {
			// Tabs 列表
			tabsList: [],
			upOption: {
				page: {
					size: 15 // 每页数据的数量,默认10
				},
				onScroll: true,
				noMoreSize: 5, // 配置列表的总数量要大于等于5条才显示'-- END --'的提示
				empty: {
					icon: require('@/bundle_c/images/order_null.png'),
					tip: this.$t('userBill.noData'), // 提示
				}
			},
			currentTab: 0,
			list: [], // 列表数据--全部
			source: 1
		};
	},

	methods: {
		// Update tab names with current language
		updateTabNames() {
			this.tabsList = [{
				name: this.$t('userBill.all')
			}, {
				name: this.$t('userBill.income')
			}, {
				name: this.$t('userBill.expenditure')
			}];

			// Update empty tip text
			this.upOption.empty.tip = this.$t('userBill.noData');
		},

		// 改变当前的Tabs位置
		changeTab(index) {
			this.currentTab = index;
			this.list = []
			this.mescroll.resetUpScroll();
		},

		// 上拉加载
		upCallback(page) {
			const pageNum = page.num; // 页码, 默认从1开始
			const pageSize = page.size; // 页长, 默认每页10条
			getAccountLog({
				page_size: pageSize,
				page_no: pageNum,
				source: this.source,
				type: this.currentTab
			}).then(({
				data
			}) => {
				if (page.num == 1) this.list = [];
				const curPageData = data.list;
				const curPageLen = curPageData.length;
				const hasNext = !!data.more;
				this.list = this.list.concat(curPageData);
				console.log(this.list)
				this.mescroll.endSuccess(curPageLen, hasNext);
			}).catch(() => {
				this.mescroll.endErr()
			})

		}
	},

	computed: {
		...mapGetters(['currentLanguage'])
	},

	watch: {
		currentLanguage() {
			// Update tab names when language changes
			this.updateTabNames();
		}
	},

	onLoad(options) {
		console.log(options, "option")
		this.currentTab = options.mode || 0;
		this.source = options.type || 1;

		// Initialize tab names with current language
		this.updateTabNames();
	}
}
</script>

<style lang="scss">
.user-bill {
	padding: 24rpx;
	.record-list{
		margin-bottom: 20rpx;
		padding: 24rpx;
		background-image: url('@/static/images/<EMAIL>');
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}
	.remark {
		color: $-color-primary;
		font-size: 30rpx;
	}
	.time {
		color: #5C81AB;
		margin-top: 12rpx;
	}
	.money {
		color: $-color-primary;
	}
}
</style>
