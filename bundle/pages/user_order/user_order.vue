<template>
	<view class="user-order">
		<!-- <u-sticky offset-top="0" h5-nav-height="0" bg-color="transparent">
			<u-navbar :is-fixed="false" :border-bottom="false" :background="{ background: 'transparent' }"
				:is-back="false">
				<view class="flex-1 flex row p-l-24" style="position: relative;">
					<u-image src="@/static/images/<EMAIL>" width="35rpx" height="24rpx"></u-image>
				</view>
			</u-navbar>
		</u-sticky> -->
		<!-- <tabs :current="active" @change="changeShow" bar-width="60" :is-scroll="false">
        <tab v-for="(item, index) in order" :key="index" :name="item.name">
            <order-list  :order-type="item.type" :i="index" :index="active"></order-list>
        </tab>
    </tabs> -->
		<!-- <u-sticky>
			<u-tabs :list="order" :current="active" @change="changeShow" :is-scroll="false" :font-size="30"
				inactive-color="#333" active-color="#FF2C3C" />
		</u-sticky> -->
		<mescroll-uni ref="mescrollRef" top="80rpx" @init="mescrollInit" @down="downCallback" @up="upCallback"
			:down="downOption" :up="upOption">
			<view class="order-list">
				<view v-for="(item, index) in orderList" :key="index" class="order-item m-t-20">
					<router-link class="flex order-item-link">
						<view class="goods-img">
									<u-image width="216rpx" height="216rpx" lazy-load
										:src="item.order_goods[0].image"></u-image>
								</view>
						<view class="order-con flex-1 p-l-24">
							<view class="goods-info flex space-between">
								<view class="goods-memo flex-1">
									<view class="goods-name primary">{{item.order_goods[0].goods_name}}</view>
									<view class="goods-desc m-t-12">GT100AI</view>
								</view>
								<button class="btn bg-primary" @tap="handleRefund(item)">{{ $t('userOrder.refundRent') }}</button>
							</view>
							<view class="all-price flex m-t-40">
								<view class="price-item">
									<view class="muted xs p-title">{{ $t('userOrder.actualPayment') }}</view>
									<view class="font-size-36 primary m-t-12">{{ parseInt(item.order_amount) }}U</view>
								</view>
								<view class="price-item">
									<view class="muted xs p-title">{{ $t('userOrder.originalPrice') }}</view>
									<view class="font-size-36 primary m-t-12">{{ parseInt(item.order_amount) }}U</view>
								</view>
								<view class="price-item">
									<view class="muted xs p-title">{{ $t('userOrder.status') }}</view>
									<view class="font-size-36 primary m-t-12">{{ item.pay_status_desc }}</view>
								</view>
							</view>
						</view>
					</router-link>
				</view>
			</view>
		</mescroll-uni>
		<!-- <view class="float-tab ~column">
			<navigator class="tab-img" :style="{top: 3*top + 'px'}" hover-class="none" open-type="switchTab"
				url="/pages/index/index">
				<image class="tab-icon" src="/bundle_c/static/images/icon_float_home.png" />
			</navigator>
			<navigator class="tab-img" :style="{top: 2*top + 'px'}" hover-class="none" open-type="navigate"
				url="/bundle/pages/chat/chat">
				<image class="tab-icon" src="/bundle_c/static/images/icon_float_help.png" />
			</navigator>
			<navigator class="tab-img" :style="{top: top + 'px'}" hover-class="none" open-type="switchTab"
				url="/bundle_c/pages/shop_cart/shop_cart">
				<image class="tab-icon" src="/bundle_c/static/images/icon_float_cart.png" />
			</navigator>
			<image style="z-index: 99" :style="{transform: `rotateZ(${showMore ? 135 : 0}deg)`}" class="tab-img" src="/bundle_c/static/images/icon_float_more.png" @click="onChange" />
		</view> -->
		<order-dialog ref="orderDialog" :order-id="orderId" :type="type" @confirm="confirmDialog"></order-dialog>
	</view>
</template>

<script>
	import {
		orderType
	} from '@/utils/type';
	import orderList from '@/bundle_c/components/order-list/order-list.vue'
	import orderGoods from '@/bundle_c/components/order-goods/order-goods.vue'
	import shopTitle from '@/bundle_c/components/shop-title/shop-title.vue'
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import MescrollMoreItemMixin from "@/components/mescroll-uni/mixins/mescroll-more-item.js";
	import {
		getOrderList,
		cancelOrder,
		delOrder,
		confirmOrder,
	} from '@/api/order';
	import {
		getRect
	} from "@/utils/tools"
	import { mapGetters } from 'vuex'
	import pageTitleMixin from '@/mixins/page-title-mixin';
	export default {
		components: {
			orderList, orderGoods, shopTitle,
		},
		mixins: [MescrollMixin, MescrollMoreItemMixin, pageTitleMixin],
		data() {
			return {
				active: -1,
				order: [],
				orderList: [],
				downOption: {
					auto: false // 不自动加载 (mixin已处理第一个tab触发downCallback)
				},
				upOption: {
					auto: false, // 不自动加载
					noMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
					empty: {
						icon: '/static/images/order_null.png',
						tip: this.$t('userOrder.noOrders'), // 提示
						fixed: true
					}
				},
				showCancel: false,
				type: 0,
				orderId: "",
				showLoading: false,
				orderType: orderType.ALL,
				showMore: false,
				top: 0
			};
		},
		onLoad() {
			// Update order tab names with current language
			this.updateOrderTabNames();

			// Get the order type from route query or use default ALL
			let type = this.$Route.query.type || orderType.ALL;
			let index = this.order.findIndex(item => item.type == type);
			this.changeShow(index);
		},


		created() {
			uni.$on("refreshorder", () => {
				this.refresh()
			})

			uni.$on('payment', params => {
				setTimeout(() => {
					if (params.result) {
						this.$toast({
							title: this.$t('userOrder.paymentSuccess')
						})
						this.refresh()
					} else {
						this.$toast({
							title: this.$t('userOrder.paymentFailed')
						})
					}
				}, 500)
			})
		},
		destroyed() {
			uni.$off("payment")
			uni.$off("refreshorder")
		},
		mounted() {
			getRect(".tab-img", false, this).then(res => {

				this.height = res.height
				console.log(this.height)
			});
		},
		methods: {
			handleRefund(item) {
				uni.navigateTo({
					url: `/pages/withdrawal/withdrawal?lx=3&sn=${item.order_sn}&amount=${item.order_amount}`
				})
			},
			// Update order tab names with current language
			updateOrderTabNames() {
				this.order = [
					{
						name: this.$t('userOrder.all'),
						type: orderType.ALL
					},
					{
						name: this.$t('userOrder.pendingPayment'),
						type: orderType.PAY
					},
					{
						name: this.$t('userOrder.pendingReceipt'),
						type: orderType.DELIVERY
					},
					{
						name: this.$t('userOrder.completed'),
						type: orderType.FINISH
					},
					{
						name: this.$t('userOrder.closed'),
						type: orderType.CLOSE
					}
				];
			},
			changeShow(index) {
				console.log(index)
				if (index != -1) {
					this.$nextTick(() => {
						this.active = index

						this.orderType = this.order[index].type
						this.refresh()
					})
				}
			},
			async confirmDialog() {
				const {
					type,
					orderId
				} = this
				let res = null
				switch (type) {
					case 0:
						res = await cancelOrder(orderId);
						break;

					case 1:
						res = await delOrder(orderId);
						break;

					case 2:
						res = await confirmOrder(orderId);
						break;
				}

				if (res.code == 1) {
					this.refresh()
					this.$toast({
						title: res.msg
					});
				}
			},
			dialogOpen() {
				this.$refs.orderDialog.open()
			},
			refresh() {
				this.mescroll.resetUpScroll()
			},
			delOrder(id) {
				this.orderId = id
				this.type = 1
				this.$nextTick(() => {
					this.dialogOpen();
				});
			},

			comfirmOrder(id) {
				this.orderId = id
				this.type = 2
				this.$nextTick(() => {
					this.dialogOpen();
				});
			},

			cancelOrder(id) {
				this.orderId = id
				this.type = 0
				this.$nextTick(() => {
					this.dialogOpen();
				});
			},

			payNow(id) {
				uni.navigateTo({
					url: `/pages/payment/payment?from=${'trade'}&order_id=${id}`
				})
			},

			upCallback(page) {
				let pageNum = page.num; // 页码, 默认从1开始
				let pageSize = page.size; // 页长, 默认每页10条
				let {
					orderType,
				} = this;
				getOrderList({
					page_size: pageSize,
					page_no: pageNum,
					type: orderType
				}).then(({
					data
				}) => {
					let curPageData = data.list;
					let curPageLen = curPageData.length;
					let hasNext = !!data.more;
					if (page.num == 1) this.orderList = [];
					this.orderList = this.orderList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen, hasNext);
				})
			},
			onChange() {
				this.showMore = !this.showMore
			},
		},
		computed: {
			...mapGetters(['currentLanguage']),
			getOrderStatus() {
				return (status) => {
					let text = ''
					switch (status) {
						case 0:
							text = this.$t('userOrder.orderStatus.pendingPayment');
							break;
						case 1:
							text = this.$t('userOrder.orderStatus.pendingDelivery');
							break;
						case 2:
							text = this.$t('userOrder.orderStatus.pendingReceipt');
							break;
						case 3:
							text = this.$t('userOrder.orderStatus.completed');
							break;
						case 4:
							text = this.$t('userOrder.orderStatus.closed');
							break;
					}
					return text
				}
			},
			getCancelTime() {
				return (time) => time - Date.now() / 1000
			}
		},
		watch: {
			currentLanguage() {
				this.updateOrderTabNames();
			},
			showMore(val) {
				if (val) {
					this.top = -this.height
				} else {
					this.top = 0
				}
			}
		}
	};
</script>
<style lang="scss">
	.order-list {
		min-height: calc(100vh - 80rpx);
		padding: 0 20rpx;
		overflow: hidden;

		.order-item {
			color: #fff;
			background-image: url('@/static/images/<EMAIL>');
			background-size: 100% 100%;
			background-repeat: no-repeat;
			padding: 24rpx;
			.order-item-link{
				align-items: flex-start;
			}
			.order-header {
				height: 80rpx;
				padding: 0 24rpx;
				border-bottom: 1px dotted #E5E5E5;
			}
			.btn{
				width: 160rpx;
				height: 64rpx;
				line-height: 64rpx;
				background-color: $-color-primary;
				color: #1D1D3B;
				border-radius: 0rpx;
				font-size: 32rpx;
			}

			.all-price {
				text-align: center;
				gap: 80rpx;
				font-size: 34rpx;
				.p-title{
					font-size: 24rpx;
					color: #5C81AB;
				}
			}
			.goods-name{
				font-size: 32rpx;
			}
			.goods-desc{
				font-size: 24rpx;
				color: #5C81AB;
			}
			.order-footer {
				height: 100rpx;
				border-top: $-solid-border;
				padding: 0 24rpx;

				.plain {
					border: 1px solid #BBBBBB;

					&.red {
						border-color: $-color-primary;
					}

				}
			}
		}
	}
	.float-tab {
		position: fixed;
		right: 16rpx;
		bottom: 200rpx;
		width: 96rpx;
		height: 96rpx;
		z-index: 777;

		.tab-img {
			width: 100%;
			height: 100%;
			position: absolute;
			transition: all .5s;
			.tab-icon {
				width: 100%;
				height: 100%;
			}
		}
	}
</style>