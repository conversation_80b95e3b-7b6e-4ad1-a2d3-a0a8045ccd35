<template>
	<view class="user-profile p-t-10 bg-white">
		<view class="content">
			<view class="bg-white">
				<view class="user-avatar-box flex-col col-center text-center">
					<view @click="onChooseAvatar">
						<image class="user-avatar"
							:src="userInfo.avatar1!= '' ? userInfo.avatar:'/bundle_c/images/portrait_empty.png'">
						</image>
						<view class="muted sm">{{ $t('userProfile.tapToChangeAvatar') }}</view>
					</view>
					<!-- #ifdef MP-WEIXIN -->
					<!-- <view class="xs muted flex row-center m-t-20" @tap="getUserProfileFun">更新微信头像</view> -->
					<!-- #endif -->
				</view>
				
				<!-- <view class="row-info flex bdb-line" @click="changeSex">
					<view class="label md">性别</view>
					<view class="md flex-1" :class="userInfo.sex == 0 ? 'muted' : ''">{{ userInfo.sex }}</view>
					<u-icon name="arrow-right" />
				</view> -->
				<view class="row-info flex bdb-line">
					<view class="label md">{{ $t('userProfile.phoneNumber') }}</view>
					<view
						class="md p-r-10"
						:class="{ 'muted': !userInfo.mobile }"
					>{{ userInfo.mobile ? userInfo.mobile : $t('userProfile.notBound') }}</view>
				
				<!-- #ifdef H5 || APP-PLUS -->
				<!--<view
						class="bd-btn br60 flex row-center"
						@tap="showModifyMobile"
					>{{ userInfo.mobile ? '更换手机号' : '绑定手机号' }}</view>-->
				<!-- #endif -->
				<!-- #ifdef MP-WEIXIN -->
				<!-- <button
						class="bd-btn br60 flex row-center"
						size="sm"
						open-type="getPhoneNumber"
						@getphonenumber="getPhoneNumber"
					>{{ userInfo.mobile ? '更换手机号' : '绑定手机号' }}</button> -->
				<!-- #endif -->
				</view>
				<view class="row-info flex bdb-line">
					<view class="label md">ID</view>
					<view class="md p-r-10">{{ userInfo.sn }}</view>
				</view>
				<!--<view class="row-info flex bdb-line"  @click="showFieId=true">
					<view class="label md">视频号ID</view>
					<view class="md flex-1" :style="{color:!userInfo.showcase_id?'#999':'#333'}">{{ userInfo.showcase_id ? userInfo.showcase_id : '请绑定视频号' }}</view>
					<u-icon name="arrow-right" />
				</view>-->
				<view class="row-info flex row-between bdb-line mt10" @tap="goToExplain(0)">
					<view class="label md">{{ $t('userProfile.serviceAgreement') }}</view>
					<u-icon name="arrow-right" />
				</view>
				<view class="row-info flex row-between bdb-line" @tap="goToExplain(1)">
					<view class="label md">{{ $t('userProfile.privacyPolicy') }}</view>
					<u-icon name="arrow-right" />
				</view>

				<!--<view class="row-info flex row-between">
					<view class="label md">关于我们</view>
					<view>v{{ version }}</view>
				</view>-->
			</view>
		</view>

		<view class="bg-primary white save-btn flex row-center lg" @tap="logout">{{ $t('userProfile.logout') }}</view>

		<!-- 版权信息 -->
		<!-- <view class="license xs text-center" v-if="appConfig.copyright_info">
			<view>{{ appConfig.copyright_info }}</view>
			<view>{{ appConfig.icp_number }}</view>
		</view> -->

		<!-- #ifndef MP-WEIXIN -->
		<!-- <u-popup type="center" closeable v-model="showMobile" mode="center" border-radius="14">
			<view class="modify-container bg-white" v-show="showMobile">
				<view class="title xl text-center">{{ userInfo.mobile ? '更换手机号' : '绑定手机号' }}</view>
				<u-field
					label="+86"
					v-if="userInfo.mobile"
					label-width="100"
					disabled
					v-model="userInfo.mobile"
				></u-field>
				<u-field label="+86" v-else label-width="140" v-model="new_mobile" placeholder="请输入手机号"></u-field>
				<u-field v-model="mobileCode" label="验证码" label-width="140" placeholder="请输入验证码">
					<view slot="right" class="primary send-code-btn br60 flex row-center" @tap="sendSmsFun">
						<u-verification-code
							:keep-running="true"
							unique-key="mobile"
							ref="uCode"
							@change="codeChange"
						></u-verification-code>
						<view class="sm">{{ codeTips }}</view>
					</view>
				</u-field>
				<u-field
					label="新手机号"
					v-if="userInfo.mobile"
					label-width="140"
					placeholder="请输入新的手机号码"
					v-model="new_mobile"
				></u-field>
				<view class="primary m-t-10 xs">{{ userInfo.mobile ? '更改' : '绑定' }}手机号码成功后，您的账号将会变更为该设置号码</view>
				<view class="btn bg-primary white flex row-center" @tap="changeUserMobileFun">确定</view>
			</view>
		</u-popup> -->
		<!-- #endif -->
		<u-popup v-model="showNickName" :closeable="true" mode="center" border-radius="14">
			<view class="modify-container bg-white" v-show="showNickName">
				<view class="title xl text-center">{{ $t('userProfile.modifyUsername') }}</view>
				<!-- <u-field v-model="newNickname" label="新昵称" placeholder="请输入新的昵称"></u-field> -->
				<view class="p-l-30 p-r-30">
					<input type="nickname" class="userName" :placeholder="$t('userProfile.pleaseEnterNickname')" v-model="newNickname" @blur="bindblur"
						style="border: 2rpx solid #ddd;height: 80rpx;line-height:80rpx;border-radius: 10rpx;padding: 0 20rpx"
					/>
				</view>
				<view class="btn bg-primary white flex row-center" @tap="changeNameConfirm">{{ $t('userProfile.confirm') }}</view>
			</view>
		</u-popup>
		<!-- 修改视频号id -->
		<u-popup v-model="showFieId" :closeable="true" mode="center" border-radius="14">
			<view class="modify-container bg-white" v-show="showFieId">
				<view class="title xl text-center">{{ $t('userProfile.modifyVideoChannelId') }}</view>
				<view class="p-l-30 p-r-30">
					<input type="text" class="userName" :placeholder="$t('userProfile.pleaseEnterVideoChannelId')" v-model="fieId"
						style="border: 2rpx solid #ddd;height: 80rpx;line-height:80rpx;border-radius: 10rpx;padding: 0 20rpx"
					/>
				</view>
				<view class="btn bg-primary white flex row-center" @tap="changeFieIdConfirm">{{ $t('userProfile.confirm') }}</view>
			</view>
		</u-popup>
		<!-- #ifndef MP-WEIXIN -->
		<!-- <u-popup v-model="showPwd" closeable mode="center" border-radius="14">
			<view class="modify-container bg-white">
				<view class="title xl text-center">设置密码</view>
				<u-field label="+86" disabled label-width="100" v-model="userInfo.mobile"></u-field>
				<u-field v-model="pwdCode" label="验证码" label-width="140" placeholder="请输入验证码">
					<view slot="right" class="primary send-code-btn br60 flex row-center" @tap="sendSmsFun">
						<u-verification-code
							unique-key="password"
							:keep-running="true"
							ref="uCode"
							@change="codeChange"
						></u-verification-code>
						<view class="sm">{{ codeTips }}</view>
					</view>
				</u-field>
				<u-field label="设置密码" label-width="140" type="password" placeholder="请输入新密码" v-model="pwd"></u-field>
				<u-field
					label="确认密码"
					label-width="140"
					type="password"
					placeholder="再次输入新密码确认"
					v-model="comfirmPwd"
				></u-field>
				<view class="btn bg-primary white flex row-center" @tap="forgetPwdFun">确定</view>
			</view>
		</u-popup> -->
		<!-- #endif -->
		<u-picker mode="selector" v-model="showPicker" :default-selector="[0]" :range="sexList" @confirm="onConfirm" />


		<u-popup v-model="showUploadTypePop" mode='bottom' border-radius="30" width='100vw'>
			<view class="kf_pop_wrap">
				<view class="cell" @click="selectUploadType('camera')">
					{{ $t('userProfile.camera') }}
				</view>
				<view class="cell" @click="selectUploadType('album')">
					{{ $t('userProfile.album') }}
				</view>
				<view class="pop_btn" @click="showUploadTypePop = false">
					{{ $t('userProfile.cancel') }}
				</view>
			</view>
		</u-popup>
		<u-popup v-model="showMessagePop" mode='center' border-radius="30" width='670'>
			<dialogPop :MessagePop='MessagePop' @MessagePopbtn2='MessagePopbtn2'></dialogPop>
		</u-popup>
	</view>
</template>

<script>
	import {
		userLogout,
		getUserInfo,
		getWxMnpMobile,
		setUserInfo,
		setWechatInfo,
		changeUserMobile
	} from "@/api/user";
	import dialogPop from '@/components/dialog_pop/dialog_pop.vue'
	import store from '@/store'
	import {
		baseURL,
		version
	} from '@/config/app';
	import {
		sendSms,
		forgetPwd,
	} from '@/api/app'
	import {
		SMSType
	} from '@/utils/type'
	import {
		mapGetters
	} from 'vuex'
	import {
		uploadFile,
		isWeixinClient,
		trottle,

	} from '@/utils/tools'
	import {
		getWxCode,
		getUserProfile
	} from '@/utils/login'
	import pageTitleMixin from '@/mixins/page-title-mixin'

	const FieldType = {
		NONE: '',
		SEX: 'sex',
		NICKNAME: 'nickname',
		AVATAR: 'avatar',
		MOBILE: 'mobile',
		FIEID: 'finder_id',
	}
	export default {
		name: 'userProfile',
		mixins: [pageTitleMixin],
		components: {
			dialogPop
		},
		data() {
			return {
				action: baseURL + '/api/file/formimage',
				fileList: [],
				userInfo: {},
				new_mobile: '',
				pwdCode: '',
				mobileCode: '',
				newNickname: '',
				sexList: [this.$t('userProfile.male'), this.$t('userProfile.female')],
				fieldType: FieldType.NONE,
				showPicker: false,
				showMobile: false,
				showPwd: false,
				showNickName: false,
				codeTips: '',
				canSendSms: true,
				pwd: '',
				comfirmPwd: '',
				smsType: '',
				code: '',
				version,
				showUploadTypePop: false,
				showMessagePop: false,
				MessagePop: {},
				showFieId:false,
				fieId: '',
				setauthority: '',
			}
		},
		methods: {
			codeChange(text) {
				this.codeTips = text;
			},
			onSuccess(e) {
				console.log(e)
			},
			onChooseAvatar() {
				this.showUploadTypePop = true
			},
			selectUploadType(type) {
				if (type == 'camera') {
					let authorityInfo = uni.getStorageSync('authorityInfo') ? uni.getStorageSync('authorityInfo') : {}
					console.log("authorityInfo: ", authorityInfo);
					if (authorityInfo.camera != 1 && authorityInfo.camera != 2) {
						this.MessagePop.title = this.$t('userProfile.prompt')
						this.MessagePop.message = this.$t('userProfile.cameraPermissionDesc')
						// this.MessagePop.btn1_text = '拒绝'
						this.MessagePop.btn2_text = this.$t('userProfile.iKnow')
						this.setauthority = 'camera'
						this.showUploadTypePop = false
						this.showMessagePop = true
					} else if (authorityInfo.camera == 1) {
						this.showUploadTypePop = false
						this.openChoose('camera')
					} else {
						this.showUploadTypePop = false
					}

				} else {
					let authorityInfo = uni.getStorageSync('authorityInfo') ? uni.getStorageSync('authorityInfo') : {}
					console.log("authorityInfo: ", authorityInfo);
					if (authorityInfo.album != 1 && authorityInfo.album != 2) {
						this.MessagePop.title = this.$t('userProfile.prompt')
						this.MessagePop.message = this.$t('userProfile.albumPermissionDesc')
						// this.MessagePop.btn1_text = '拒绝'
						this.MessagePop.btn2_text = this.$t('userProfile.iKnow')
						this.setauthority = 'album'
						this.showUploadTypePop = false
						this.showMessagePop = true
					} else if (authorityInfo.album == 1) {
						this.showUploadTypePop = false
						this.openChoose('album')
					} else {
						this.showUploadTypePop = false
					}
				}
			},
			MessagePopbtn2() {
				if (this.MessagePop.btn2_text == '知道了') {
					if (this.setauthority == 'camera') {
						this.showMessagePop = false
						this.openChoose('camera')
					} else if (this.setauthority == 'album') {
						this.showMessagePop = false
						this.openChoose('album')
					}
				}
			},
			openChoose(type) {
				let that = this
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: [type],
					success: function(res) {
						let authorityInfo = uni.getStorageSync('authorityInfo') ? uni.getStorageSync(
							'authorityInfo') : {}
						if (that.setauthority == 'camera') {
							authorityInfo.camera = 1
						} else if (that.setauthority == 'album') {
							authorityInfo.album = 1
						}
						uni.setStorageSync('authorityInfo', authorityInfo)
						const tempFilePaths = res.tempFilePaths;
						uni.uploadFile({
							url: baseURL + '/api/file/formimage', //仅为示例，非真实的接口地址
							filePath: tempFilePaths[0],
							name: 'file',
							header: {
								token: store.getters.token,
								// version: '1.2.1.20210717'
							},
							success: (uploadFileRes) => {
								const res = JSON.parse(uploadFileRes.data)
								console.log(uploadFileRes, 'uploadFileRes');
								that.fieldType = 'avatar'
								that.setUserInfoFun(res.data.uri)
							}
						});
					},
					fail: function(err) {
						if (err.errMsg == 'chooseImage:fail No Permission') {
							let authorityInfo = uni.getStorageSync('authorityInfo') ? uni.getStorageSync(
								'authorityInfo') : {}
							if (that.setauthority == 'camera') {
								authorityInfo.camera = 2
							} else if (that.setauthority == 'album') {
								authorityInfo.album = 2
							}
							uni.setStorageSync('authorityInfo', authorityInfo)
						} else {
							let authorityInfo = uni.getStorageSync('authorityInfo') ? uni.getStorageSync(
								'authorityInfo') : {}
							if (that.setauthority == 'camera') {
								authorityInfo.camera = 1
							} else if (that.setauthority == 'album') {
								authorityInfo.album = 1
							}
							uni.setStorageSync('authorityInfo', authorityInfo)
						}
						console.log("err: ", err);
					}
				});
			},
			// 文件上传读取
			afterRead(e) {
				const file = e
				console.log(file)
				uni.showLoading({
					title: '正在上传中...',
					mask: true
				});
				file.forEach(item => {
					uploadFile(item.path).then(res => {
						uni.hideLoading();
						this.fieldType = FieldType.AVATAR
						this.setUserInfoFun(res.base_uri)
					});
				})
			},
			// 更新小程序头像信息
			async getUserProfileFun() {
				const {
					userInfo
				} = await getUserProfile()
				const {
					avatarUrl,
					nickName,
					gender
				} = userInfo
				const {
					msg,
					code
				} = await setWechatInfo({
					nickname: nickName,
					avatar: avatarUrl,
					sex: gender
				})
				if (code == 1) {
					this.$toast({
						title: msg
					});
					this.getUserInfoFun()
				}
			},

			// end
			logout() {
				//  退出登录
				userLogout().then(res => {
					if (res.code == 1) {
						this.$store.commit("logout");
						this.$toast({
							title: this.$t('common.success')
						})
						setTimeout(() => {
							this.$Router.replaceAll('/pages/index/index')
						}, 500)
					}
				})
			},
			goToExplain(value) {
				this.$Router.push({
					path: '/bundle/pages/server_explan/server_explan',
					query: {
						type: value
					}
				})
			},
			goLicense() {
				this.$Router.push({
					path: '/bundle/pages/license/license',
					query: {
						id: ''
					}
				})
			},

			// 发送短信
			sendSmsFun(type) {
				if (!this.$refs.uCode.canGetCode) return
				sendSms({
					mobile: this.userInfo.mobile || this.new_mobile,
					key: this.smsType
				}).then(res => {
					if (res.code == 1) {
						this.$toast({
							title: res.msg
						});
						this.$refs.uCode.start();
					}
				})
			},
			getUserInfoFun() {
				getUserInfo().then(res => {
					if (res.code == 1) {
						this.userInfo = res.data;
					}
				})
			},
			// 更换手机号
			showModifyMobile() {
				this.new_mobile = '';
				this.showMobile = true
				this.smsType = this.userInfo.mobile ? SMSType.CHANGE_MOBILE : SMSType.BIND
			},
			changeUserMobileFun() {
				if (!this.mobileCode) {
					this.$toast({
						title: '请输入验证码'
					})
					return;
				}
				if (!this.new_mobile) {
					this.$toast({
						title: '请输入新的手机号码'
					})
					return;
				}

				changeUserMobile({
					mobile: this.userInfo.mobile,
					new_mobile: this.new_mobile,
					code: this.mobileCode,
					action: this.userInfo.mobile ? 'change' : 'binding'
				}).then(res => {
					if (res.code == 1) {
						this.showMobile = false;
						this.$toast({
							title: res.msg
						})
						this.getUserInfoFun();
					}
				})
			},
			// 修改用户信息
			setUserInfoFun(value) {
				setUserInfo({
					field: this.fieldType,
					value: value
				}).then(res => {
					if (res.code == 1) {
						this.$toast({
							title: res.msg,
							icon:'success'
						});
						this.getUserInfoFun()

						this.newNickname = '';
						this.showNickName = false;
						this.fieId = ''
						this.showFieId = false
					}
				})
			},
			onConfirm(value) {
				this.setUserInfoFun(value[0] + 1);
				this.showPicker = false;
			},
			changeSex(e) {
				this.showPicker = true;
				this.fieldType = FieldType.SEX;
			},

			// 修改密码
			showPwdPop() {
				if (!this.userInfo.mobile) {
					this.$toast({
						title: '请绑定手机后再设置密码'
					})
					return;
				}
				this.smsType = SMSType.FINDPWD
				this.showPwd = true
			},
			forgetPwdFun() {
				let {
					pwdCode,
					pwd,
					comfirmPwd
				} = this;
				if (!pwdCode) {
					this.$toast({
						title: '请输入短信验证码'
					});
					return;
				}
				if (!pwd) {
					this.$toast({
						title: '请输入新密码'
					});
					return;
				}
				if (!comfirmPwd) {
					this.$toast({
						title: '再次输入新密码确认'
					});
					return;
				}
				if (pwd != comfirmPwd) {
					this.$toast({
						title: '两次密码输入不一致'
					});
					return;
				}
				let data = {
					mobile: this.userInfo.mobile,
					code: pwdCode,
					password: pwd,
					repassword: comfirmPwd
				};
				forgetPwd(data).then(res => {
					if (res.code == 1) {
						this.showPwd = false;
						this.$toast({
							title: '设置密码成功'
						});
						this.getUserInfoFun();
					}
				})
			},
			// 修改昵称
			changeName() {
				this.fieldType = FieldType.NICKNAME;
				this.newNickname = '';
				this.showNickName = true;
			},
			changeNameConfirm() {
				if(!this.newNickname){
					this.$toast({title: this.$t('userProfile.pleaseEnterNickname')})
					return false
				}
				this.fieldType = FieldType.NICKNAME;
				this.setUserInfoFun(this.newNickname);
				//this.showNickName = false;
			},
			//设置视频号Id
			changeFieIdConfirm(){
				if(!this.fieId){
					this.$toast({title: this.$t('userProfile.pleaseEnterVideoChannelId')})
					return false
				}
				this.fieldType = FieldType.FIEID;
				this.setUserInfoFun(this.fieId);
			},
			//获取微信昵称
			bindblur(event) {
				this.nameUser = event.target.value;
			},
			// 微信获取手机号
			// #ifdef MP-WEIXIN
			async getPhoneNumber(e) {
				console.log(e, this.code)
				const {
					encryptedData,
					iv
				} = e.detail;
				const params = {
					code: this.code,
					encrypted_data: encryptedData,
					iv
				}
				this.fieldType = FieldType.MOBILE
				if (encryptedData) {
					const {
						data,
						code,
						msg
					} = await getWxMnpMobile(params)
					if (code == 1) {
						this.$toast({
							title: msg
						});
						// #ifdef MP-WEIXIN
						getWxCode().then(res => {
							this.code = res
						})
						// #endif
						this.getUserInfoFun()
					}
				}
			},
			// #endif
		},
		onLoad() {
			this.getUserInfoFun()
			// #ifdef MP-WEIXIN
			getWxCode().then(res => {
				this.code = res
			})
			// #endif
			this.getUserProfileFun = trottle(this.getUserProfileFun, 500, this)
		},
		computed: {
			...mapGetters(['token', 'appConfig', 'currentLanguage']),

			getLanguageName(lang) {
				const languageMap = {
					'zh-CN': '简体中文',
					'en': 'English',
					'ja': '日本語',
					'ko': '한국어',
					'ru': 'Русский',
					'es': 'Español',
					'de': 'Deutsch',
					'fr': 'Français'
				};
				return languageMap[lang] || 'English';
			}
		},
	}
</script>

<style lang="scss">
	.user-profile {
		min-height: calc(100vh - env(safe-area-inset-bottom));
		display: flex;
		flex-direction: column;

		.content {
			// flex: 1;
			border-top-left-radius: 28rpx;
			border-top-right-radius: 28rpx;

			.user-avatar-box {
				padding: 60rpx;

				.user-avatar {
					width: 150rpx;
					height: 150rpx;
					border-radius: 50%;
					margin-bottom: 20rpx;
				}
			}

			.row-info {
				padding: 30rpx 20rpx;

				.label {
					flex: 1;
				}

				.bd-btn {
					padding: 8rpx 24rpx;
					border: 1px solid $-color-primary;
					color: $-color-primary;
				}
			}

			.bdb-line {
				border-bottom: 1rpx solid #e5e5e5;
			}
		}

		.license {
			margin-top: 80rpx;
			color: #a7a7a7;
		}

		.save-btn {
			margin: 40rpx 54rpx 0;
			height: 88rpx;
			border-radius: 10rpx;
			color: #1d1d1d;
		}

		.modify-container {
			padding: 30rpx;
			width: 620rpx;
			border-radius: 30rpx;

			.title {
				padding: 26rpx 0rpx;
			}

			.btn {
				height: 80rpx;
				border-radius: 20rpx;
				margin: 60rpx 50rpx 0;
			}
		}

		.kf_pop_wrap {
			display: flex;
			flex-direction: column;
			align-items: center;
			// background-color: #fff;
			border-radius: 30rpx;
			margin-bottom: 50rpx;

			.cell {
				width: 100%;
				line-height: 88rpx;
				border-bottom: 1rpx solid #F1F3F5;
				text-align: center;
				background-color: #fff;
			}

			.cell:first-child {
				border-radius: 20rpx 20rpx 0 0;
			}

			.cell:nth-of-type(2) {
				border-radius: 0 0 20rpx 20rpx;
			}

			.pop_btn {
				margin-top: 30rpx;
				text-align: center;
				line-height: 88rpx;
				width: 100%;
				background-color: #fff;
				border-radius: 20rpx;
			}
		}
	}
</style>