<template>
	<view class="bank">
		<scroll-view scroll-y="true" class="scroll-Y" @scroll="scroll">
			<u-swipe-action :options="options" v-for="(v,i) in bankList" :key="i" :show="v.show"  @open='open(i)' @click="deletaItem(v.id)" @content-click="checkPaycard(v)">
				<view class="p-b-20">
					<view class="bank-card">
						<view class="">
							<view style="margin-left:20rpx;position:relative" >
								<view style='font-size: 32rpx;'>姓名:{{v.nickname}}</view>
								<view style='font-size: 28rpx;'>
									卡号:{{v.account}}
								</view>
								<view style="font-size: 28rpx;margin-top:4rpx;">预留手机号:{{v.phone}}</view>
								<view>
									<span class="m-r-20">{{v.name}}</span>
									<span>{{v.branch}}</span>
								</view>
								<view class="cardType">
									<!-- <view>
									 <u-tag text="汇聚快捷" type="error" shape="circle" plain></u-tag>
									</view> -->
									<!-- <view v-else>
									  <u-tag text="新生快捷"  shape="circle"></u-tag>
									</view> -->
								</view>
							</view>
						</view>
					</view>
				</view>
			</u-swipe-action>
		</scroll-view>
		<view class="tip">轻按目标银行卡,左滑可以删除</view>
		<view @click="toPath" class="bind">绑定银行卡</view>
	</view>
</template>

<script>
	import {banklists, bankdel} from '@/api/youpin.js'
	export default {
		data() {
			return {
				bankList: [],
				options: [{
					text: '删除',
					style: {
						color:'white',
						backgroundColor: 'red',
						marginBottom:'20rpx'
					}
				}],
				scrollTop: 0,
				old: {
					scrollTop: 0
				},
				isjs:0,
				source:0,
				userInfo:{}
			}
		},
		onShow(){
			
			// let pagearr = getCurrentPages();//获取应用页面栈
			// let currentPage = pagearr[pagearr.length - 1];//获取当前页面信息
			// console.log('option:' , currentPage.__page__.options)//获取页面传递的信息
			// if(currentPage.__page__.options.hasOwnProperty('isjs')){
				
			// 	this.isjs = currentPage.__page__.options.isjs
			// 	console.log(this.isjs);
			// }
			this.getCardList();
		},
		onLoad(option) {
			if(option.source>0){
				this.source = option.source;
			}
		},
		methods: {
			//选择地址
			checkPaycard(item){
				if(this.source == 1){
					console.log(item);
					uni.$emit('selectPaycard',item);
					uni.navigateBack();
				}
			},
			open(index){
				this.bankList[index].show = true;
				this.bankList.map((val, idx) => {
					if(index != idx) this.bankList[idx].show = false;
				})
			},
			deletaItem(id){
				console.log(id)
				bankdel({id}).then( res=>{
					uni.showToast({
						title:res,
						icon:'none'
					})
					this.getCardList();
				})
			},
			getCardList(){
				this.bankList = []
				banklists().then(res => {
					this.bankList = res.data.lists;
					this.bankList.forEach(item=>{
						item.show=false
					})
				})
			},
			scroll: function(e) {
				this.old.scrollTop = e.detail.scrollTop
			},
			toPath(url) {
				uni.navigateTo({
					url: '/bundle_c/pages/addbank/addbank'
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	page{
		height: 100%;
	}
	.bank{
		height: 100%;
	}
	.u-page {
		padding: 0;
	}

	.u-demo-block__title {
		padding: 10px 0 2px 15px;
	}

	/deep/.uni-scroll-view {
		.u-swipe-action{
			border-radius: 40rpx;
		}
		width: auto;
		margin: 0 20rpx;
		&__content {
			padding: 25rpx 0;

			&__text {
				font-size: 15px;
				color: $u-main-color;
				padding-left: 30rpx;
			}
		}
	}

	.scroll-Y {
		height: 100%;
		//margin:40rpx 0 ;
		font-size: 24rpx;
	}
	.bind {
		position: fixed;
		bottom: 50rpx;
		left: 25rpx;
		width: 700rpx;
		z-index: 1000;
		height: 90rpx;
		background: $-color-primary;
		border-radius: 100rpx;
		line-height: 90rpx;
		font-size: 32rpx;
		text-align: center;
		color: white;
		font-weight: 600;
		letter-spacing: 5rpx;
	}
	.cardType{
		position: absolute;
		top:0;
		right:50rpx;
		z-index: 10;
	}
	.tip{
		position: absolute;
		bottom:180rpx;
		left:50%;
		transform: translate(-50%,-50%);
		color: $-color-primary;
		text-decoration: underline;
	}
	.bank-card{
		// margin: 40rpx 0;
		padding: 20rpx;
		background: linear-gradient(270deg, #FF2C3C 0%, #F95F2F 100%);
		color: #fff;
		// border-radius: 40rpx;
		.margin{
			margin: 10rpx 0;
		}
	}
</style>
