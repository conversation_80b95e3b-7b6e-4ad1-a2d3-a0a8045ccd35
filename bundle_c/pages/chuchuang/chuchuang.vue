<template>
	<view class="chuchuang">
		<view class="kuang">
			<u-field v-model="nickname" label="视频号昵称" placeholder="视频号昵称" label-width="160"></u-field>
			<u-field v-model="id" label="视频号ID" placeholder="视频号ID" label-width="160"></u-field>
			<view class="btn bg-primary white flex row-center md" @tap="confirm">确定</view>
		</view>
	</view>
</template>

<script>
	import { mapGetters, mapActions } from 'vuex'
	import { bindShowcase } from "@/api/youpin.js"
	export default {
		name: "Chuchuang",
		data (){
			return {
				nickname:'',
				id:'',
			}
		},
		onShow() {
			this.getUser();
			console.log(this.userInfo)
			this.nickname = this.userInfo.showcase_name
			this.id = this.userInfo.showcase_id
		},
		methods:{
			...mapActions([ 'getUser']),
			confirm() {
				if(!this.nickname){
					this.$toast({title: '请输入视频号昵称'});
					return false
				}
				if(!this.id){
					this.$toast({title: '请输入视频号ID'});
					return false
				}
				
				bindShowcase({shop_id:this.id,nickname:this.nickname}).then(res =>{
					if(res.code==1){
						this.$toast({title: res.msg, icon:'success'})
						this.getUser();
					}
				})
			},
		},
		computed: {
			...mapGetters(["userInfo"]),
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-field {
		padding: 30rpx 28rpx !important;
	}
	.chuchuang{
		padding: 30rpx;
		
		.kuang{
			padding: 0 30rpx 40rpx 30rpx;
			background-color: #fff;
			
			.btn {
				height: 80rpx;
				border-radius: 10rpx;
				margin: 40rpx 0 0;
			}
		}
	}
</style>