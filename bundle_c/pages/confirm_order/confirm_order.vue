<template>
    <view>
        <view class="confirm-order">
            <view class="confirm-con order-item flex">
                <view class="goods-img">
                            <u-image width="216rpx" height="216rpx" lazy-load
                                :src="shopLists[0] && shopLists[0].goods && shopLists[0].goods[0].image"></u-image>
                        </view>
                <view class="order-con flex-1 p-l-24">
                    <view class="goods-info flex space-between">
                        <view class="goods-memo flex-1">
                            <view class="goods-name primary">{{shopLists[0] && shopLists[0].goods && shopLists[0].goods[0].name}}</view>
                            <view class="goods-desc m-t-12"></view>
                        </view>
                    </view>
                    <view class="all-price flex m-t-40">
                        <view class="price-item">
                            <view class="muted xs p-title">{{ $t('confirmOrder.actualPayment') }}</view>
                            <view class="font-size-36 primary m-t-12">{{ parseInt(orderInfo.total_amount) }} U</view>
                        </view>
                        <view class="price-item">
                            <view class="muted xs p-title">{{ $t('confirmOrder.originalPrice') }}</view>
                            <view class="font-size-36 primary m-t-12">{{ parseInt(orderInfo.total_amount) }} U</view>
                        </view>
                    </view>
                </view>
				<u-checkbox :value="true" shape="circle" activeColor="#04F9FC" size="46" activeIconColor="#1d1d3b" style="margin-right: -24rpx;"></u-checkbox>
            </view>
            <view class="footer bg-white flex row-between fixed">
                <view class="all-price lg text-center">
                    <text class="font-size-24 muted" style="color: #5C81AB;">{{ $t('confirmOrder.total') }}</text>
                    <view class="primary">
                        <text class="font-size-36">{{ parseInt(orderInfo.total_amount) }} U</text>
                    </view>
                </view>
                <button class="btn br60" size="md" hover-class="none" @tap="onSubmitOrder">
                    {{ $t('confirmOrder.submitOrder') }}
                </button>
            </view>
        </view>

        <loading-view v-if="showLoading" background-color="transparent" :size="50"></loading-view>
        <loading-view v-if="isFirstLoading"></loading-view>
    </view>
</template>

<script>
    import {
        orderInfo,
        orderBuy,
        getOrderCoupon
    } from '@/api/order';
    import {
        teamBuy,
        teamKaiTuan
    } from '@/api/activity'
    import {
        prepay,
        getMnpNotice,
        getPayway
    } from '@/api/app';
    import {
        wxpay,
        alipay
    } from '@/utils/pay'
	import orderShop from "../../components/order-shop/order-shop.vue"
    import pageTitleMixin from '@/mixins/page-title-mixin'
    // total_amount
    export default {
        mixins: [pageTitleMixin],
		components:{orderShop},
        data() {
            return {
                isFirstLoading: true,
                showLoading: false,
                address: {},
                orderInfo: {},
                shopLists: [],
                addressId: '',
                useIntegral: 0,
                userRemark: [],
                couponId: [],
                teamId: undefined,
                carts: [],
                type: '',
                goods: '',
                bargainLaunchId: -1,
                invoiceArr: [] ,// 发票数组
				isAgreement: false,//购买协议是否勾选
				goods_xieyi: null,
				isSelect : -1
            };
        },

        onLoad(options) {

            uni.$on("selectaddress", (e) => {
                this.addressId = e.id;
				this.orderBuyFun();
            })

            // 监听发票传回的值，
            uni.$on('invoice', params => {
                const index = this.invoiceArr.findIndex(el => el.shop_id == params.shop_id)
                if ( params.del == true && this.invoiceArr.length) {
                    this.invoiceArr.splice(index, 1);
                } else {
                    if ( index == -1 ) this.invoiceArr = [...this.invoiceArr, params]
                    else this.invoiceArr.splice(index, 1, params);
                }
            })

            const {
                data: {
                    goods,
                    carts,
                    teamId,
                    foundId,
                    type
                }
            } = this.$Route.query

            this.goods = goods
            this.bargainLaunchId = goods[0].bargain_launch_id || -1
            this.carts = carts || []
            this.type = type
            this.teamId = teamId

            // 参团的id，如果为空的话就是开团，如果有数据就是参团
            this.foundId = foundId || ''

			this.orderBuyFun();
        },

		onUnload() {
			uni.$off("selectaddress")
			uni.$off("payment")
			uni.$off("invoice")
		},

        methods: {
            // 备注
            changeRemark(e) {
                let index = this.userRemark.findIndex((item) => item.shop_id == e.shop_id)
                if (index == -1) {
                    this.userRemark.push(e)
                } else {
                    this.userRemark[index].remark = e.remark
                }
                this.userRemark = this.userRemark.filter((item) => item.remark)
            },

            // 选中优惠券
            changeCoupon(e, index) {
                this.couponId[index] = e
                this.orderBuyFun()
            },

			// 选择对应的配送方式赋值给商品中的配送方式
			changeDeliveryType(type, row) {
				this.isSelect = type
				row.delivery_type = type;
				for(let i=0;i<this.goods.length;i++) {
					const item = this.goods[i];
					if(row.shop_id == item.shop_id) {
						this.goods[i].delivery_type = type;
					}
				}
				this.orderBuyFun();
			},

            getAuthMsg() {
                return new Promise(resolve => {
                    getMnpNotice({
                        scene: 1
                    }).then(res => {
                        if (res.code == 1) {
                            uni.requestSubscribeMessage({
                                tmplIds: res.data,

                                fail(res) {
                                    console.log(res.errMsg);
                                },

                                complete() {
                                    resolve();
                                }

                            });
                        } else {
                            resolve();
                        }
                    });
                });
            },

            onSubmitOrder() {
				//选择配送方式，需要的时候要放开
				/* if(this.isSelect == -1){
					// Show delivery method selection dialog
					this.$refs.orderShopRef[0].chengeDelivery();
					// Display translated message
					// this.$toast({ title: this.$t('confirmOrder.selectDeliveryMethod') })
					return false
				} */
				if(this.goods_xieyi == 1){ //只有 goods_xieyi=1 的情况才需要勾选协议
					if(!this.isAgreement) return this.$toast({ title: this.$t('confirmOrder.readAndAgree') })
				}
                uni.showModal({
                    title: this.$t('confirmOrder.confirmOrderPrompt'),
                    content: this.$t('confirmOrder.confirmOrderContent'),
                    confirmColor: '#FF2C3C',
                    success: async res => {
                        let {
                            confirm
                        } = res;
                        if (confirm) {
                            // #ifdef MP-WEIXIN
                            await this.getAuthMsg();
                            //#endif
                            this.showLoading = true
                            this.orderBuyFun('submit');
                        }
                    }
                });
            },

            async orderBuyFun(action = 'info') {
                const {
                    userRemark,
                    useIntegral,
                    carts,
                    goods,
                    bargainLaunchId,
                    couponId
                } = this;
                const submitObj = {
                    goods: JSON.stringify(goods),
                    address_id: this.addressId,
                    cart_id: carts.join(),
                    coupon_id: couponId.filter(item => item),
                    // bargain_launch_id是砍价的判断
                    bargain_launch_id: this.bargainLaunchId == -1 ? '' : this.bargainLaunchId
                };

                // 判断是不是拼团的，并且是获取订单数据
                if (this.teamId && action == 'info') {
                    delete submitObj.goods;
                    submitObj.action = 'info';
                    submitObj.item_id = this.goods[0].item_id;
                    submitObj.delivery_type = this.goods[0].delivery_type;
                    submitObj.count = this.goods[0].num;
                    submitObj.goods_id = this.goods[0].goods_id
                    submitObj.team_id = this.teamId;
                }
                // 判断是不是拼团的，并且是提交订单
                if (this.teamId && action == 'submit') {
                    submitObj.action = 'buy';
                    submitObj.item_id = this.goods[0].item_id;
                    submitObj.delivery_type = this.goods[0].delivery_type;
                    submitObj.count = this.goods[0].num;
                    submitObj.goods_id = this.goods[0].goods_id
                    submitObj.team_id = this.foundId;
                }

                if (action == 'submit') {
                    // 拿第一个店铺的 delivery_type 类型，虚拟商品不能加入购物车所以不用考虑会虚拟商品和实物商品出错
                    submitObj.delivery_type = this.shopLists[0]?.delivery_type || 0
                    submitObj.remark = userRemark.length ? JSON.stringify(userRemark) : ''
                    submitObj.invoice = JSON.stringify(this.invoiceArr)
                }

                let {
                    data: orderData,
                    code: orderCode,
                    msg: orderMsg
                } = action == 'info' ? this.teamId ? await teamKaiTuan(submitObj) : await orderInfo(submitObj) :
                this.teamId ? await teamKaiTuan(submitObj) : await orderBuy(submitObj)
                // 如果是info的话说明是获取订单数据，？用拼团的id判断当前是否是拼团，是的话调用teamKaiTuan，不是的话调用普通订单获取orderInfo
                // ：判断是不是拼团订单，是的话调用teamKaiTuan提交拼团订单，否则就是普通订单orderBuy

                if(orderMsg == this.$t('confirmOrder.outOfStock')) {
                    setTimeout(() => {
                        uni.navigateBack(1)
                    },500)
                }

                if (orderCode !== 1) return this.showLoading = false

                if (action == 'info') {
                    const {
                        shop,
                        address
                    } = orderData
                    this.address = address
                    this.shopLists = shop
                    this.orderInfo = orderData
                    this.$nextTick(() => {
                        this.isFirstLoading = false
                    });
					this.goods_xieyi = this.orderInfo?.shop[0]?.goods[0]?.goods_xieyi
                } else if (action == 'submit') {
					this.showLoading = false

					let order_id = ''
					const type = orderData.type

					switch(type) {
						case 'order': order_id = orderData.order_id; break;
						case 'trade': order_id = orderData.trade_id; break;
					}

					uni.$on('payment', params => {
						setTimeout(() => {
							if (params.result) {
								console.log('Jason', this)
								this.$Router.replace({
									path: '/bundle_c/pages/pay_result/pay_result',
									query: {
										id: params.order_id,
										from: params.from
									}
								})
							} else {
								this.$Router.replace({
									path: '/bundle/pages/user_order/user_order'
								})
							}
						}, 1 * 1000)
					})

					uni.navigateTo({
						url: `/bundle_c/pages/payment/payment?from=${type}&order_id=${order_id}`
					})
                }
            }
        },
        watch: {
            address(val) {
                this.addressId = val.id
            }
        },
        computed: {
            currentLanguage() {
                return this.$i18n.locale;
            }
        }

    }
</script>
<style lang="scss" scoped>
    .confirm-order {
        color: #fff;
        padding: 24rpx;
        .confirm-con{

        }
        .order-item {
			color: #fff;
			background-image: url('@/static/images/<EMAIL>');
			background-size: 100% 100%;
			background-repeat: no-repeat;
			padding: 24rpx;
			.order-item-link{
				align-items: flex-start;
			}
			.order-header {
				height: 80rpx;
				padding: 0 24rpx;
				border-bottom: 1px dotted #E5E5E5;
			}
			.btn{
				width: 160rpx;
				height: 64rpx;
				line-height: 64rpx;
				background-color: $-color-primary;
				color: #1D1D3B;
				border-radius: 0rpx;
				font-size: 32rpx;
			}

			.all-price {
				text-align: center;
				gap: 80rpx;
				font-size: 34rpx;
				.p-title{
					font-size: 24rpx;
					color: #5C81AB;
				}
			}
			.goods-name{
				font-size: 32rpx;
			}
			.goods-desc{
				font-size: 24rpx;
				color: #5C81AB;
			}
			.order-footer {
				height: 100rpx;
				border-top: $-solid-border;
				padding: 0 24rpx;

				.plain {
					border: 1px solid #BBBBBB;

					&.red {
						border-color: $-color-primary;
					}

				}
			}
		}


        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
			z-index: 99;
            height: 100rpx;
            padding: 0 30rpx;
            box-sizing: content-box;
            padding-bottom: env(safe-area-inset-bottom);
            background-color: #011750;
            .btn {
                width: 396rpx;
                background: $-color-primary;
                padding: 0 50rpx;
                border-radius: 0rpx;
            }
        }

    }

    // .confirm-order .van-cell:after {
    // 	border: none;
    // }

    // .goods .shop-icon {
    // 	width: 40rpx;
    // 	height: 40rpx;
    // }

    // .pop-title {
    // 	height: 100rpx;
    // 	border-bottom: 1rpx solid #F2F2F2;
    // }

    // .pop-title .title {
    // 	margin-left: 30rpx;
    // 	font-size: 34rpx;
    // 	font-weight: bold;
    // 	line-height: 36rpx;
    // }
</style>
