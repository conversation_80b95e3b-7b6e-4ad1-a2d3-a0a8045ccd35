<template>
	<view class="forget-pwd">
		<view class="input-container">
			<view class="input-item">
				<view class="input-label flex normal"><text class="required">*</text>{{ $t('forgetPwd.phoneNumber') }}</view>
				<view class="input-content">
					<u-input v-model="mobile" class="input" :placeholder="$t('forgetPwd.enterPhoneNumber')" :custom-style="{
						'height': '88rpx',
					}" placeholder-style="color: #90BAEB" />
				</view>
			</view>
			<!-- 
			<view class="input-item">
				<view class="input-label flex normal"><text class="required">*</text>{{ $t('forgetPwd.smsVerificationCode') }}</view>
				<view class="input-content">
					<u-input v-model="smsCode" :placeholder="$t('forgetPwd.enterVerificationCode')" :custom-style="{
						'height': '88rpx',
					}" placeholder-style="color: #90BAEB" />
					<button class="bd-primary xs primary get-code flex row-center" @click="sendSmsFun">

					<u-verification-code unique-key="forget-pwd" ref="uCode" @change="codeChange">
					</u-verification-code>
						<view class="xs">{{ codeTips }}</view>
					</button>
				</view>
			</view> -->
			<view class="input-item">
				<view class="input-label flex normal"><text class="required">*</text>{{ $t('forgetPwd.originalPassword') }}</view>
				<view class="input-content">
					<u-input type="password" v-model="old_password" :placeholder="$t('forgetPwd.enterOriginalPassword')" :custom-style="{
						'height': '88rpx',
					}" placeholder-style="color: #90BAEB" />
				</view>
			</view>
			<view class="input-item">
				<view class="input-label flex normal"><text class="required">*</text>{{ $t('forgetPwd.resetPassword') }}</view>
				<view class="input-content">
					<u-input type="password" v-model="resetPwd" :placeholder="$t('forgetPwd.passwordRequirements')" :custom-style="{
						'height': '88rpx',
					}" placeholder-style="color: #90BAEB" />
				</view>
			</view>
			<view class="input-item">
				<view class="input-label flex normal"><text class="required">*</text>{{ $t('forgetPwd.confirmPassword') }}</view>
				<view class="input-content">
					<u-input type="password" v-model="comfirmPwd" :placeholder="$t('forgetPwd.enterPasswordAgain')" :custom-style="{
						'height': '88rpx',
					}" placeholder-style="color: #90BAEB" />
				</view>
			</view>
		</view>
		<view class="login-wrap">
			<view class="btn white bg-primary flex row-center" @click="forgetPwdFun">
				{{ $t('forgetPwd.confirm') }}
			</view>
		</view>
	</view>
</template>

<script>
import {
	forgetPwd,
	sendSms
} from '@/api/app.js'
import { mapGetters } from 'vuex'
import {
	ACCESS_TOKEN
} from '@/config/app.js'
import {
	SMSType
} from '@/utils/type.js'
import {
	mapMutations
} from 'vuex'
import pageTitleMixin from '@/mixins/page-title-mixin'
export default {
	name: 'forgetPwd',
	mixins: [pageTitleMixin],
	data() {
		return {
			mobile: '',
			smsCode: '',
			old_password:'',
			resetPwd: '',
			comfirmPwd: '',
			time: 59,
			codeTips: '',
		}
	},

	computed: {
		...mapGetters(['currentLanguage'])
	},
	onLoad() {

	},
	methods: {
		...mapMutations(['login']),
		codeChange(tip) {
			this.codeTips = tip
		},
		forgetPwdFun() {
			let {
				mobile,
				smsCode,
				resetPwd,
				comfirmPwd
			} = this;
			if (!mobile) {
				this.$toast({
					title: this.$t('forgetPwd.pleaseEnterPhoneNumber')
				});
				return;
			}
			// if (!smsCode) {
			// 	this.$toast({
			// 		title: this.$t('forgetPwd.pleaseEnterVerificationCode')
			// 	});
			// 	return;
			// }
			if(!this.old_password){
				this.$toast({
					title: this.$t('forgetPwd.enterOriginalPassword')
				});
				return;
			}
			if (!resetPwd) {
				this.$toast({
					title: this.$t('forgetPwd.pleaseEnterResetPassword')
				});
				return;
			}
			if (!comfirmPwd) {
				this.$toast({
					title: this.$t('forgetPwd.pleaseEnterConfirmPassword')
				});
				return;
			}
			if (resetPwd != comfirmPwd) {
				this.$toast({
					title: this.$t('forgetPwd.passwordsDoNotMatch')
				});
				return;
			}
			let data = {
				mobile: mobile,
				// code: smsCode,
				old_password: this.old_password,
				password: resetPwd,
				repassword: comfirmPwd
			};
			forgetPwd(data).then(res => {
				if (res.code == 1) {
					this.login(data);
					this.$toast({
						title: res.msg
					});
					//  跳转到登录页
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				}
			})
		},
		sendSmsFun() {
			if (!this.$refs.uCode.canGetCode) return
			if (!this.mobile) {
				this.$toast({
					title: this.$t('forgetPwd.pleaseEnterPhoneInfo')
				})
				return;
			}
			sendSms({
				mobile: this.mobile,
				key: SMSType.FINDPWD
			}).then(res => {
				if (res.code == 1) {
					this.$toast({ title: res.msg });
					this.$refs.uCode.start();
				}
			})
		}
	},
}
</script>

<style lang="scss">
page {
	padding: 0;
}

.forget-pwd {
	min-height: 100vh;
	padding: 40px 20px 0;
	padding: 80rpx 40rpx 0;

	.input-container {
		.input-item {
			margin-bottom: 32rpx;

			.input-label {
				flex: none;
				margin-bottom: 24rpx;
				color: #fff;
				font-size: 26rpx;

				.required {
					color: #F93E3E;
					margin-right: 5rpx;
					vertical-align: middle;
				}

				&:before {
					content: '';
					display: inline-block;
					height: 20rpx;
					width: 2rpx;
					margin-right: 10rpx;
					background-color: #fff;
				}
			}
			.input-content {
                display: flex;
                align-items: center;
                background-image: url('@/static/images/<EMAIL>');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                padding: 0 24rpx;
            }
			.get-code {
                border-radius: 0;
            }

			.bd-primary {
				height: 56rpx;
				width: 176rpx;
				flex: none;
				border: 1px solid $-color-primary;

				.seconds {
					color: $-color-primary;
				}
			}
		}
	}
	.login-wrap {
		position: fixed;
		display: flex;
		flex-direction: column;
		align-items: center;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #011750;
		padding-top: 18rpx;
		.btn {
			border-radius: 0rpx;
			width: 702rpx;
			height: 88rpx;
			margin: 20rpx 0 50rpx;
			color: #1D1D3B;
			font-size: 34rpx;
		}
	}
}
</style>
