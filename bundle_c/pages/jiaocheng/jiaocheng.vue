<template>
	<view class="jiaocheng p-20 text-center">
		<view class="news-details">
			<view class="header">
				<view class="title xxl m-b-20">{{ articleDetail.title }}</view>
				<view class="flex row-between">
					<view class="xs lighter">发布时间：{{ articleDetail.create_time }}</view>
					<view class="flex">
						<u-icon name="eye" color="#666"></u-icon>
						<view class="m-l-10 xs muted">{{ articleDetail.visit }}人浏览</view>
					</view>
				</view>
			</view>
			<view class="main">
				<view v-if="video">
					<view class="m-b-20 br24" style="width:100%;height: 600rpx;">
						<video :src="video" controls object-fit="contain" style="width:100%;height: 100%;"></video>
					</view>
					<view @click.stop="downVideo" style="background-color: #FF4B59;width: 100%;height:76rpx;line-height:76rpx;color: #fff;
						border-radius:10rpx;margin: 0 auto 20rpx auto;">点击保存</view>
				</view>
				<view class="m-b-30" style="width:100%;" v-else>
					<image :src="image" mode="widthFix" style="width: 100%;display: block;"></image>
				</view>
				<u-parse :html="article_content" />
				<view @click.stop="copyText" style="background-color: #FF4B59;width: 100%;height:76rpx;line-height:76rpx;color: #fff;
					border-radius:10rpx;margin: 20rpx auto;" v-if="article_content">复制文案</view>
			</view>
		</view>
		<loading-view v-if="showLoading"></loading-view>
	</view>
</template>

<script>
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	import {
		getArticleDetail
	} from '@/api/store';
	import {copy} from "@/utils/tools.js"

	export default {
		data() {
			return {
				showLoading: true,
				articleDetail: {},
				article_content: "",
				video: '',
				image: '',
			};
		},
		onLoad: function(options) {
			this.type = Number(options.type) || '';
			this.id = options.id;
			this.video = decodeURIComponent(options.video)
			this.image = decodeURIComponent(options.image)

			uni.setNavigationBarTitle({
				title: '详情'
			});
			this.getArticleDetailFun();
		},
		methods: {
			...mapActions(['getUser']),
			getArticleDetailFun() {
				getArticleDetail({
					type: this.type,
					id: this.id
				}).then(res => {
					if (res.code == 1) {
						this.articleDetail = res.data
						setTimeout(() => {
							this.article_content = res.data.content;
						}, 200);
						setTimeout(() => {
							this.showLoading = false
						}, 300);
					}
				});
			},
			//下载视频到本地
			downVideo() {
				uni.showLoading({
				  title: '保存中...',
				  mask: true, // 是否显示透明蒙层，防止触摸穿透
				});
				wx.downloadFile({
				  url: this.video,
				  success(res) {
				    if (res.statusCode === 200) {
				      wx.saveVideoToPhotosAlbum({
				        filePath: res.tempFilePath,
				        success: () => {
				          wx.showToast({ title: '保存成功' });
				        },
				        fail: () => {
				          wx.showToast({ title: '保存失败', icon: 'none' });
						  uni.hideLoading()
				        },
				      });
				    }
				  },
				  fail: () => {
				    wx.showToast({ title: '下载失败', icon: 'none' });
					uni.hideLoading()
				  },
				});
				/* uni.showLoading()
				uni.downloadFile({
					url: this.video,
					timeout: 30000,
					header: {
						"Content-Type": "video/mp4"
					},
					success: res => {
						uni.saveVideoToPhotosAlbum({
							filePath: res.tempFilePath,
							success: res => {
								uni.hideLoading()
								uni.showToast({
									title: "保存成功",
									duration: 2000,
								})
							}
						})
					},
					fail() {
						uni.hideLoading()
					}
				})
				uni.hideLoading() */
			},
			copyText(){
				// 过滤 HTML 标签并复制纯文本
				function copyPlainText(htmlString) {
				  // 正则移除 HTML 标签
				  const plainText = htmlString.replace(/<\/?[^>]+(>|$)/g, "");
				
				  // 使用 UniApp 提供的 API 复制到剪贴板
				  uni.setClipboardData({
				    data: plainText,
				    success: () => {
					  uni.showToast({
					  	title: "复制成功",
					  	icon:"success"
					  })
				    },
				    fail: (err) => {
				      console.error('复制失败:', err);
				    }
				  });
				}
				copyPlainText(this.article_content);
				//copy(this.article_content)
			},
		},
		computed: {
			...mapGetters(["userInfo"]),
		}
	};
</script>
<style lang="scss">
	page {
		background-color: #fff;
	}

	.jiaocheng .header {
		padding: 20rpx 15px;
		border-bottom: $-solid-border;
	}

	.jiaocheng .main {
		padding: 40rpx 0;
	}
</style>