<template>
	<view class="jiaocheng p-20 text-center">
		<view class="news-details">
			<view class="main">
				<view v-if="video" class="m-b-20 br24" style="width:100%;height: 400rpx;overflow: hidden;">
					<video :src="video" controls object-fit="fill" style="width:100%;height: 100%;"></video>
				</view>
				<view v-if="video && userInfo.level > 1" @click.stop="downVideo" style="background-color: #FF4B59;width: 100%;height:76rpx;line-height:76rpx;color: #fff;
					border-radius:10rpx;margin: 0 auto 20rpx auto;">点击保存</view>
				<view class="m-b-20" style="width:100%;">
					<image :src="image" mode="widthFix" style="width: 100%;display: block;"></image>
				</view>
				<u-parse :html="article_content" />
			</view>
		</view>
		<loading-view v-if="showLoading"></loading-view>
	</view>
</template>

<script>
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	import {
		navigation_detail
	} from '@/api/youpin';

	export default {
		data() {
			return {
				showLoading: true,
				video: '',
				image: '',
				id:'',
				name:'',
			};
		},
		onLoad: function(options) {
			this.id = options.id;
			this.name = options.name;
			uni.setNavigationBarTitle({
				title: decodeURIComponent(this.name)
			});
			this.getArticleDetailFun();
		},
		methods: {
			...mapActions(['getUser']),
			getArticleDetailFun() {
				navigation_detail({
					id: this.id
				}).then(res => {
					if (res.code == 1) {
						this.video = res.data.video
						this.image = res.data.image
						setTimeout(() => {
							this.showLoading = false
						}, 300);
					}
				});
			},
			//下载视频到本地
			downVideo() {
				uni.showLoading({
				  title: '保存中...',
				  mask: true, // 是否显示透明蒙层，防止触摸穿透
				});
				wx.downloadFile({
				  url: this.video,
				  success(res) {
				    if (res.statusCode === 200) {
				      wx.saveVideoToPhotosAlbum({
				        filePath: res.tempFilePath,
				        success: () => {
				          wx.showToast({ title: '保存成功' });
				        },
				        fail: () => {
				          wx.showToast({ title: '保存失败', icon: 'none' });
						  uni.hideLoading()
				        },
				      });
				    }
				  },
				  fail: () => {
				    wx.showToast({ title: '下载失败', icon: 'none' });
					uni.hideLoading()
				  },
				});
			},
		},
		computed: {
			...mapGetters(["userInfo"]),
		}
	};
</script>
<style lang="scss">
	page {
		background-color: #fff;
	}
</style>