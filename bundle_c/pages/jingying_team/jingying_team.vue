<template>
	<view class="daren">
		<!-- <mescroll-body ref="mescrollRef" @init="mescrollInit" @up="upCallback" :up="upOption" :down="{auto: false}" @down="downCallback"> -->
			<view class="main">
				<view class="user">
					<view class="list flex flex-wrap">
						<view class="card" v-for="(item,index) in list" :key="index">
							<view class="cardImage">
								<image :src="item.image" mode="widthFix"></image>
							</view>
							<view class="name m-t-15 m-b-10">{{item.title}}</view>
							<view class="position m-b-15">{{item.position}}</view>
						</view>
					</view>
				</view>
			</view>
		<!-- </mescroll-body> -->
	</view>
</template>

<script>
	import { mapActions, mapMutations, mapGetters } from 'vuex'
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import MescrollMoreItemMixin from "@/components/mescroll-uni/mixins/mescroll-more-item.js";
	import {showcaseList} from "@/api/youpin.js"
	
	let systemInfo = uni.getSystemInfoSync()
	export default {
		mixins: [MescrollMixin,MescrollMoreItemMixin], // 使用mixin
		data() {
			return {
				top:'',
				dataList:[],
				// Tabs 列表
				upOption: {
					page: {
						size: 15 // 每页数据的数量,默认10
					},
					onScroll:true,
					noMoreSize: 5, // 配置列表的总数量要大于等于5条才显示'-- END --'的提示
					empty: {
						icon: '@/bundle_c/static/nodata.png',
						tip: '暂无数据', // 提示
					}
				},
				list:[]
			}
		},
		onLoad(option) {
			this.list = this.$Route.query.list || 0;
		},
		onShow() {
			this.getUser();
		},
		methods: {
			...mapActions([ 'getUser']),
			// 上拉加载
			upCallback(page) {
				const pageNum = page.num; // 页码, 默认从1开始
				const pageSize = page.size; // 页长, 默认每页10条
				
				showcaseList({
					page_size: pageSize,
					page_no: pageNum,
				}).then(({
					data
				}) => {
					if (page.num == 1) this.dataList = [];
					const curPageData = data.list;
					const curPageLen = curPageData.length;
					const hasNext = !!data.more;
					this.dataList = this.dataList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen, hasNext);
				}).catch(() => {
					this.mescroll.endErr()
				})
			},
			
			onPageScroll(res) {
				this.top = res.scrollTop;
			},
		},
		computed: {
			...mapGetters(["userInfo"]),
		}
	}
</script>

<style lang="scss" scoped>
	page{
	}
	.daren{
		position: relative;
		
		.main{
			padding: 24rpx 0 0 0;
			position: relative;
			z-index: 3;
			
			.user{
				padding: 0 24rpx 24rpx 24rpx;
				
				.list{
					border-radius: 24rpx;
					
					.card{
						width: calc((100% - 48rpx) / 3);
						margin-right: 24rpx;
						margin-bottom: 24rpx;
						border-radius: 12rpx;
						overflow: hidden;
						background-color: #fff;
						
						&:nth-child(3n){
							margin-right: 0;
						}
						
						.cardImage{
							width: 100%;
							height: 250rpx;
							position: relative;
							overflow: hidden;
							background-color: #000;
							
							image{
								width: 100%;
								height: 100%;
								position: absolute;
								top:50%;
								left: 50%;
								transform: translate(-50%,-50%);
							}
						}
						.name{
							color: #333;
							font-size: 30rpx;
							padding: 0 20rpx;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}
						.position{
							color: #989896;
							font-size: 24rpx;
							padding: 0 20rpx;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}
					}
				}
			}
		}
	}
</style>
