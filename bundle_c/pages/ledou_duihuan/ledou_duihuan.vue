<template>
	<view class="ledou_duihuan">
		<view class="bg"></view>
		<ParHeader @SetChild_winHeight="GetChild_winHeight" @SetChild_HeaderHeight="GetChild_HeaderHeight"></ParHeader>
		<view class="main" :style="{paddingTop: HeaderHeight + 20 + 'px',height:winHeight + 'px'}" v-show="HeaderHeight">
			<view class="title flex row-left">
				<view class="name">乐豆兑换代金券</view>
				<image src="https://lwyz.hntianheng.cn/static/common/image/pay/icon_balance.png" mode="widthFix"/>
				
				<!-- <view class="top flex row-between">
					<view class="select flex row-center" @click="show = true">
						<view class="selectValue">{{selectValue}}</view>
						<u-icon :name="show?'arrow-up-fill':'arrow-down-fill'" size="18"></u-icon>
					</view>
				</view> -->
			</view>
			<view class="content">
				<u-form :model="form" ref="uForm" class="uForm" :label-style="{'font-size':'20rpx','color':'transparent','font-weight':'700'}">
					<!-- <view>
						<view class="change" @click="ClickChange">
							<image src="/bundle_c/static/personal/change.png" mode="widthFix"/>
						</view>
					</view> -->
					<u-form-item label="数量" label-position='top' :border-bottom="false">
						<u-input v-model="form.num" type="number" placeholder='请输入数量'/>
						<view class="sub">
							我的<text :class="[selectId==0?'text1':'text2']">{{selectId==0?'代金券':'钻石'}}</text>
							数量: {{selectId==0?userInfo.user_money:userInfo.diamond}}张</view>
						<view class="biao">
							<image v-if="selectId==0" src="https://lwyz.hntianheng.cn/static/common/image/pay/icon_balance.png" mode="widthFix"/>
							<image v-else src="/bundle_c/static/personal/zuan.png" mode="widthFix"/>
						</view>
					</u-form-item>
					<u-form-item label="数量" label-position='top' prop="num" :border-bottom="false">
						<view class="tit">需消耗掉：</view>
						<u-input v-model="setNum" type="number" placeholder="" disabled/>
						<view class="sub">我的<text class="text0">乐豆</text>数量: {{userInfo.fen}}个</view>
						<view class="biao"><image src="/bundle_c/static/jiang/dou.png" mode="widthFix"/></view>
					</u-form-item>
					<u-form-item label="数量" label-position='top' prop="num" :border-bottom="false">
						<view class="tit">需消耗掉：</view>
						<u-input v-model="setLove" type="number" placeholder="" disabled/>
						<view class="sub">我的<text class="text3">爱心值</text>数量: {{userInfo.love}}个</view>
						<view class="biao"><image src="/static/images/icon_collection_s.png" mode="widthFix"/></view>
					</u-form-item>
				</u-form>
				<!-- <view class="tips">
					<view>
						<view class="tit">兑换比例：1乐豆=1钻石</view>
						<view>手续费：普通用户10%，VIP5%，大玩家2%</view>
					</view>
				</view> -->
				<view class="jiang">
					<image src="/bundle_c/static/jiang/jiang.png" mode="widthFix"/>
				</view>
			</view>			
			<view class="handle" @click="submit">确认兑换</view>
		</view>
		<u-select v-model="show" mode="single-column" :list="selectList" @confirm="confirm" confirm-color="#FFBB24"></u-select>
	</view>
</template>

<script>
	import { mapActions, mapMutations, mapGetters } from 'vuex'
	import { toast } from "@/utils/tools.js" //轻提示弹框
	import ParHeader from '@/components/pai-header/par-header.vue'
	import {
		ExchangeDiamond,ExchangeDaijinquan
	} from '@/api/leyou.js' //新版api请求
	
	export default {
		components: {ParHeader},
		data() {
			return {
				HeaderHeight:'',//顶部距离
				winHeight:'', //屏幕高度
				current:0,
				form: {
					num: null
				},
				rules: {
					num: [
						{
							required: true,
							message: '请输入数量',
							trigger: 'blur,change'
						}
					]
				},
				diamond:'', //钻石
				fen:'', //乐豆
				isClick:true,//点击事件除重
				show: false,
				selectValue: '兑换代金券',
				selectId:0,
				selectList:[
					{value:0,label:'兑换代金券'},
					{value:1,label:'兑换钻石'}
				],
			}
		},
		// 必须要在onReady生命周期，因为onLoad生命周期组件可能尚未创建完毕
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		onShow() {
			this.getUser() //更新
		},
		onLoad(option){
			//this.diamond = option.diamond
			//this.fen = option.fen
		},
		methods: {
			...mapActions(['getUser']),
			GetChild_HeaderHeight(e){ //head组件传过来的值
				this.HeaderHeight = e
			},
			GetChild_winHeight(e){ //head组件传过来的值
				this.winHeight = e
			},
			// 回调参数为包含多个元素的数组，每个元素分别反应每一列的选择情况
			confirm(e) {
				this.selectValue = ''
				this.selectId = e[0].value
				this.selectValue = e[0].label
				console.log(e)
			},
			
			submit() {
				if(!this.isClick) return false
				this.isClick = false
				this.$refs.uForm.validate(valid => {
					if (valid) {
						let data = {
							num: this.form.num
						}
						ExchangeDaijinquan(data).then(res => {
							if(res.code == 1){
								this.$refs.uForm.resetFields() //重置 
								this.$toast({ title: res.msg, icon:'success'})
								this.getUser() //更新
							}
						}).finally(() => {
							this.isClick = true
						})
					} else {
						console.log('验证失败');
						this.isClick = true
					}
				});
			},
			
			//切换
			/* ClickChange(){
				if(this.type == 'ledou'){
					this.type = 'zuanshi'
				}else if(this.type == 'zuanshi'){
					this.type = 'ledou'
				}
			}, */
		},
		computed: {
			...mapGetters(["userInfo"]),
			setNum(){
				return this.form.num ? ((this.form.num * 1.1)/this.userInfo.fen_price).toFixed(5) : null
			},
			setLove(){
				return this.form.num ? (this.form.num*0.1).toFixed(5) : null
			},
		}
	}
</script>

<style lang="scss" scoped>
	page{
		width: 100%;
		/* #ifndef APP-PLUS */
		height: calc(100vh);
		/* #endif */
		/* #ifdef APP-PLUS */
		height: 100vh;
		/* #endif */
	}
	
	.ledou_duihuan{
		width: 100%;
		height: 100%;
		/* #ifdef APP-PLUS */
		height: calc(100vh);
		/* #endif */
		background: linear-gradient(to bottom,#49474A,#292826);
		position: relative;
		overflow: hidden;
		
		.bg{
			width: 100%;
			height: 100%;
			/* #ifdef APP-PLUS */
			height: calc(100vh);
			/* #endif */
			position: absolute;
			top:0;
			left: 0;
			background: url(@/bundle_c/static/wenli.png) no-repeat 0 0;
			background-size: 100% auto;
			pointer-events: none; /*点击无效*/
		}
		
		.main{
			position: relative;
			
			.title{
				padding: 0 0 42rpx 80rpx;
				
				.name{
					font-size: 40rpx;
					margin-right: 22rpx;
					color: #FFFFFF;
					font-weight: 600;
				}
				image{
					width: 50rpx;
					height: 50rpx;
				}
				
				.top{
					margin-left: auto;
					margin-right: 30rpx;
					
					.select{
						padding: 0 30rpx;
						height: 66rpx;
						line-height: 66rpx;
						font-size: 26rpx;
						border-radius: 88rpx;
						color: #333;
						text-align: center;
						background-color: #fff;
						font-weight: 700;
						
						.selectValue{
							margin-right: 16rpx;
						}
					}
					.text{
						font-size: 24rpx;
						color: #777777;
					}
				}
			}
			
			.content{
				padding: 0 32rpx;
				height: 100%;
				background: -webkit-linear-gradient(to bottom, rgba(255,255,255,0) 10%,rgba(255,255,255,1) 28%, rgba(255,255,255,1) 100%); /* Chrome, Safari */
				background: linear-gradient(to bottom, rgba(255,255,255,0) 10%,rgba(255,255,255,1) 28%, rgba(255,255,255,1) 100%); /* 标准语法 */
				
				.change{
					width: 72rpx;
					height: 72rpx;
					margin: 30rpx auto 0 auto;
					
					image{
						width: 100%;
					}
				}
				.uForm{
					padding: 40rpx 28rpx;
					background: -webkit-linear-gradient(to bottom, #ffffff, #F8F8F8); /* Chrome, Safari */
					background: linear-gradient(to bottom, #ffffff, #F8F8F8); /* 标准语法 */
					border-radius: 20rpx;
					
					::v-deep .u-form-item{
						padding-top: 0;
						position: relative;
						
						.u-input{
							background-color: #EEEEEF;
						}
						&:first-child{
							.u-input{
								background-color: #fff;
							}
						}
					}
					::v-deep .u-form-item__message{
						display: none;
					}
					::v-deep .u-input{
						width: 100%;
						padding: 0 110rpx 0 24rpx!important;
						height: 80rpx;
						border:2rpx solid;
						background-color: #fff;
						border-color: #B1B1B1;
						border-radius: 8rpx;
					}
					
					::v-deep input{
						height: 80rpx;
						line-height: 80rpx;
						font-size: 28rpx;
					}
					.sub{
						font-size: 24rpx;
						color: #777777;
						position: absolute;
						top:15rpx;
						right: 0;
						
						.text0{
							color: #E6A639;
							font-weight: 700;
						}
						.text1{
							color: #F96969;
							font-weight: 700;
						}
						.text2{
							color: #BE60FE;
							font-weight: 700;
						}
						.text3{
							color: #FF2C3C;
							font-weight: 700;
						}
					}
					.biao{
						width: 48rpx;
						height: 48rpx;
						position: absolute;
						top:92rpx;
						right: 30rpx;
						
						image{
							width: 100%;
						}
					}
				}
				.tips{
					margin-top: 40rpx;
					font-size: 24rpx;
					line-height: 36rpx;
					padding: 0 16rpx;
					color: #BFBFBF;
					
					.tit{
						font-size: 26rpx;
						margin-bottom: 24rpx;
						color: #C7622A;
					}
				}
				.jiang{
					width: 252rpx;
					height: 146rpx;
					position: absolute;
					bottom: 30rpx;
					left: 32rpx;
					
					image{
						width: 100%;
					}
				}
			}
			.handle{
				width: 684rpx;
				height: 92rpx;
				line-height: 92rpx;
				font-size: 32rpx;
				border-radius: 100rpx;
				background-color: #333333;
				color: #F6C995;
				text-align: center;
				position: absolute;
				bottom: 124rpx;
				left:50%;
				transform: translateX(-50%);
			}
		}
	}
</style>
