<template>
	<view class="ledou_duihuan">
		<view class="main">
			<view class="title flex row-left">
				<view class="name">余额兑换购物券</view>
				<image src="/bundle_c/static/icon_love.png" mode="widthFix"/>
			</view>
			<view class="content">
				<u-form :model="form" ref="uForm" class="uForm" :label-style="{'font-size':'20rpx','color':'transparent','font-weight':'700'}">
					<u-form-item label="数量" label-position='top' :border-bottom="false">
						<u-input v-model="form.num" type="number" placeholder='请输入数量'/>
						<view class="sub">我的<text class="text0">购物券</text>数量: {{userInfo.goldcoin}}张</view>
						<view class="biao">
							<image src="/bundle_c/static/icon_quan.png" mode="widthFix"/>
						</view>
					</u-form-item>
					<u-form-item label="数量" label-position='top' prop="num" :border-bottom="false">
						<view class="tit">需消耗掉余额数量：</view>
						<u-input v-model="form.num" type="number" placeholder="对应消耗数量" disabled/>
						<view class="sub">
							我的<text class="text1">余额</text>
							数量: {{userInfo.love}}个</view>
						<view class="biao biao2">
							<image src="/bundle_c/static/icon_love.png" mode="widthFix"/>
							</view>
					</u-form-item>
				</u-form>
				<!-- <view class="tips">
					<view>
						<view class="tit">兑换比例：1乐豆=1钻石</view>
						<view>手续费：普通用户10%，VIP5%，大玩家2%</view>
					</view>
				</view> -->
				<view class="jiang">
					<!-- <image src="/bundle_c/static/jiang/jiang.png" mode="widthFix"/> -->
				</view>
			</view>			
			<view class="handle" @click="submit">确认兑换</view>
		</view>
		<u-select v-model="show" mode="single-column" :list="selectList" @confirm="confirm" confirm-color="#FFBB24"></u-select>
	</view>
</template>

<script>
	import { mapActions, mapMutations, mapGetters } from 'vuex'
	import { toast } from "@/utils/tools.js" //轻提示弹框
	import {
		ExchangeCoin
	} from '@/api/youpin.js' //新版api请求
	
	export default {
		data() {
			return {
				HeaderHeight:'',//顶部距离
				winHeight:'', //屏幕高度
				current:0,
				form: {
					num: null
				},
				rules: {
					num: [
						{
							required: true,
							message: '请输入数量',
							trigger: 'blur,change'
						}
					]
				},
				diamond:'', //钻石
				fen:'', //乐豆
				isClick:true,//点击事件除重
				show: false,
				selectValue: '兑换代金券',
				selectList:[
					{value:0,label:'兑换代金券'},
					{value:1,label:'兑换钻石'}
				],
			}
		},
		// 必须要在onReady生命周期，因为onLoad生命周期组件可能尚未创建完毕
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		onShow() {
			this.getUser() //更新
		},
		onLoad(option){
			//this.diamond = option.diamond
			//this.fen = option.fen
		},
		methods: {
			...mapActions(['getUser']),
			GetChild_HeaderHeight(e){ //head组件传过来的值
				this.HeaderHeight = e
			},
			GetChild_winHeight(e){ //head组件传过来的值
				this.winHeight = e
			},
			// 回调参数为包含多个元素的数组，每个元素分别反应每一列的选择情况
			confirm(e) {
				this.selectValue = ''
				this.selectValue = e[0].label
				console.log(e)
			},
			
			submit() {
				if(!this.isClick) return false
				this.isClick = false
				this.$refs.uForm.validate(valid => {
					if (valid) {
						let data = {
							num: this.form.num
						}
						ExchangeCoin(data).then(res => {
							if(res.code == 1){
								this.form.num = null
								this.$refs.uForm.resetFields() //重置 
								this.$toast({ title: res.msg, icon:'success'})
								this.getUser() //更新
							}
						}).finally(() => {
							this.isClick = true
						})
					} else {
						console.log('验证失败');
						this.isClick = true
					}
				});
			},
			
			//切换
			/* ClickChange(){
				if(this.type == 'ledou'){
					this.type = 'zuanshi'
				}else if(this.type == 'zuanshi'){
					this.type = 'ledou'
				}
			}, */
		},
		computed: {
			...mapGetters(["userInfo"]),
			/* setNum(){
				return this.form.num >= 0 ? this.form.num : 0 //0.3
			} */
		}
	}
</script>

<style lang="scss" scoped>
	page{
		height: 100vh;
	}
	
	.ledou_duihuan{
		width: 100%;
		padding: 30rpx 0;
		
		.main{
			position: relative;
			
			.title{
				padding: 0 0 0 60rpx;
				
				.name{
					font-size: 40rpx;
					margin-right: 22rpx;
					font-weight: 600;
				}
				image{
					width: 50rpx;
					height: 50rpx;
				}
				
				.top{
					margin-left: auto;
					margin-right: 30rpx;
					
					.select{
						padding: 0 30rpx;
						height: 66rpx;
						line-height: 66rpx;
						font-size: 26rpx;
						border-radius: 88rpx;
						color: #333;
						text-align: center;
						font-weight: 700;
						
						.selectValue{
							margin-right: 16rpx;
						}
					}
					.text{
						font-size: 24rpx;
						color: #777777;
					}
				}
			}
			
			.content{
				padding: 0 32rpx;
			
				.change{
					width: 72rpx;
					height: 72rpx;
					margin: 30rpx auto 0 auto;
					
					image{
						width: 100%;
					}
				}
				.uForm{
					padding: 40rpx 28rpx;
					border-radius: 20rpx;
					
					::v-deep .u-form-item{
						padding-top: 0;
						position: relative;
						
						&:last-child{
							.u-input{
								background-color: #EEEEEF;
							}
						}
					}
					::v-deep .u-form-item__message{
						display: none;
					}
					::v-deep .u-input{
						width: 100%;
						padding: 0 110rpx 0 24rpx!important;
						height: 80rpx;
						border:2rpx solid;
						background-color: #fff;
						border-color: #B1B1B1;
						border-radius: 8rpx;
					}
					
					::v-deep input{
						height: 80rpx;
						line-height: 80rpx;
						font-size: 28rpx;
					}
					.sub{
						font-size: 24rpx;
						color: #777777;
						position: absolute;
						top:15rpx;
						right: 0;
						
						.text0{
							color: #E6A639;
							font-weight: 700;
						}
						.text1{
							color: #F96969;
							font-weight: 700;
						}
						.text2{
							color: #BE60FE;
							font-weight: 700;
						}
					}
					.biao{
						width: 48rpx;
						height: 48rpx;
						position: absolute;
						top:95rpx;
						right: 30rpx;
						
						&.biao2{
							top:165rpx;
						}
						image{
							width: 100%;
							height: 100%;
						}
					}
				}
				.tips{
					margin-top: 40rpx;
					font-size: 24rpx;
					line-height: 36rpx;
					padding: 0 16rpx;
					color: #BFBFBF;
					
					.tit{
						font-size: 26rpx;
						margin-bottom: 24rpx;
						color: #C7622A;
					}
				}
				.jiang{
					width: 252rpx;
					height: 146rpx;
					position: absolute;
					bottom: 30rpx;
					left: 32rpx;
					
					image{
						width: 100%;
					}
				}
			}
			.handle{
				width: 684rpx;
				height: 92rpx;
				line-height: 92rpx;
				font-size: 32rpx;
				border-radius: 100rpx;
				background-color: #333333;
				color: #F6C995;
				text-align: center;
				margin: 0 auto;
			}
		}
	}
</style>
