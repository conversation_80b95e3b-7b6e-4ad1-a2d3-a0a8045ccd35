<template>
	<view class="orderList">
		<!-- <u-sticky>
			<u-tabs-swiper ref="uTabs" :list="list" :current="current" @change="tabsChange" :is-scroll="false" class="tabsTop"
			:font-size="30" inactive-color="#333" active-color="#FF2C3C"
			swiperWidth="750"></u-tabs-swiper>
		</u-sticky> -->
		
		<view class="card">
			<u-sticky>
				<u-tabs :list="list" :current="current" @change="tabsChange" :is-scroll="false" 
				:font-size="30"  inactive-color="#333" active-color="#FF2C3C"/>
			</u-sticky>
			<mescroll-uni ref="mescrollRef" @init="mescrollInit" @up="upCallback" :up="upOption" :down="{auto: false}" @down="downCallback">
				<view class="item" v-for="(item,idx) in orderList" :key="idx">
					<view class="top">
						<view class="time">付款时间：{{item.create_time}}</view>
						<view class="tag">{{item.status_text}}</view>
					</view>
					<view class="cont">
						<view class="jiazong">总价值：<text class="zhi">{{SetZongPrice(item.total_price, item.income_price)}}</text><text>乐豆</text></view>
						<view class="info">
							<view class="infoL">
								<image :src="item.goods.goods_image" mode="widthFix"/>
								<view class="biao">{{item.goods.issues_text}}</view>
							</view>
							<view class="infoR">
								<view class="text">
									<view class="name">金蟾等级：</view>
									<view class="param">{{item.goods.goods_name}}</view>
								</view>
								<view class="text">
									<view class="name">拍到价格：</view>
									<view class="param">{{item.total_price}}<text>乐豆</text></view>
								</view>
								<view class="text">
									<view class="name">预计奖励：</view>
									<view class="param">{{item.income_price}}<text>乐豆</text></view>
								</view>
								<view class="text">
									<view class="name">预计到期：</view>
									<view class="param">{{item.expire_time}}</view>
								</view>
								<view class="text">
									<view class="name">订单号：</view>
									<view class="param">{{item.order_no}}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</mescroll-uni>
		</view>
	</view>
</template>

<script>
	import { mapActions, mapMutations, mapGetters } from 'vuex'
	//import { getOrderList } from "@/api/leyou.js"
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import MescrollMoreItemMixin from "@/components/mescroll-uni/mixins/mescroll-more-item.js";
	import {
		getOrderList,
		cancelOrder,
		delOrder,
		confirmOrder
	} from '@/api/order';
	export default {
		components: {},
		mixins: [MescrollMixin,MescrollMoreItemMixin], // 使用mixin
		data() {
			return {
				statusBarHeight:'',
				titleBarHeight:'',
				cardTop:'',
				winHeight:'',
				// 因为内部的滑动机制限制，请将tabs组件和swiper组件的current用不同变量赋值
				current: 0, // tabs组件的current值，表示当前活动的tab选项
				swiperCurrent: 0, // swiper组件的current值，表示当前那个swiper-item是活动的
				list: [{
					name: '待付款'
				}, {
					name: '待发货'
				}, {
					name: '待收货'
				}, {
					name: '商品评价'
				}, {
					name: '退款/售后'
				}],
				orderList:[],
				// Tabs 列表
				upOption: {
					page: {
						size: 15 // 每页数据的数量,默认10
					},
					onScroll:true,
					noMoreSize: 5, // 配置列表的总数量要大于等于5条才显示'-- END --'的提示
					empty: {
						icon: '/bundle_c/static/nodata.png',
						tip: '暂无数据', // 提示
					}
				},
				type: 0,
				orderType: 0,
			}
		},
		onLoad(option) {
			
		},
		created(){
			
		},
		methods: {
			// tabs通知swiper切换
			tabsChange(index) {
				this.orderList = []
				this.current = index;
				this.orderType = index
				this.mescroll.resetUpScroll()
			},
			
			// 上拉加载
			/* upCallback(page) {
				const pageNum = page.num; // 页码, 默认从1开始
				const pageSize = page.size; // 页长, 默认每页10条
				
				getOrderList({
					page_size: pageSize,
					page_no: pageNum,
					status: this.source, //类型 10 => '待付款', 20 => '持有中', 30 => '转售中', 40 => '交割中', 50 => '已完成'
				}).then(({
					data
				}) => {
					if (page.num == 1) this.orderList = [];
					const curPageData = data.data;
					const curPageLen = curPageData.length;
					const hasNext = !!data.more;
					this.orderList = this.orderList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen, hasNext);
				}).catch(() => {
					this.mescroll.endErr()
				})
			}, */
			upCallback(page) {
				let pageNum = page.num; // 页码, 默认从1开始
				let pageSize = page.size; // 页长, 默认每页10条
				let {
					orderType,
				} = this;
				getOrderList({
					page_size: pageSize,
					page_no: pageNum,
					type: orderType
				}).then(({
					data
				}) => {
					let curPageData = data.list;
					let curPageLen = curPageData.length;
					let hasNext = !!data.more;
					if (page.num == 1) this.orderList = [];
					this.orderList = this.orderList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen, hasNext);
				})
			},
		},
		computed:{
			//计算总价值
			SetZongPrice(){
				return (a,b)=>{
					return Number(a) + Number(b)
				}
			}
		},
		filters:{
			/* typeFilter(e){
				let text = '';
				if(e==1){
					text = '已完成'
				}else if(e==2){
					text = '待付款'
				}else if(e==3){
					text = '持有中'
				}else if(e==4){
					text = '转售中'
				}else if(e==5){
					text = '交割中'
				}
				return text
			}, */
		},
	}
</script>

<style lang="scss" scoped>
	page{
		background-color: #F6F6F6;
		height: 100%;
		overflow: hidden;
	}
	.orderList{
		width: 100vw;
		height: 100vh;
		.tabsTop{
			margin-bottom: 24rpx;
		}
		.card{
			
			.item{
				padding: 0 40rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
				
				&:last-child{
					margin-bottom: 0;
				}
				
				.top{
					height: 76rpx;
					border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);
					display: flex;
					justify-content: space-between;
					align-items: center;
					
					.time{
						font-size: 22rpx;
						color: #999999;
					}
					.tag{
						width: 120rpx;
						height: 46rpx;
						line-height: 46rpx;
						font-size: 24rpx;
						border-radius: 6rpx;
						text-align: center;
						color: #333;
					}
				}
				.cont{
					padding: 20rpx 0 28rpx 0;
					
					.jiazong{
						font-size: 24rpx;
						margin-bottom: 44rpx;
						color: #666666;
						
						text{
							font-size: 28rpx;
							color: #C7632A;
							
							&.zhi{
								margin-right: 10rpx;
							}
						}
					}
					.info{
						display: flex;
						justify-content: flex-start;
						align-items: center;
						
						.infoL{
							width: 138rpx;
							height: 192rpx;
							margin-right: 48rpx;
							position: relative;
							
							image{
								width: 100%;
								height: 100%;
								display: block;
							}
							.biao{
								width: 100%;
								height: 36rpx;
								line-height: 36rpx;
								border-radius: 6rpx;
								text-align: center;
								color: #333;
								position: absolute;
								bottom: 15rpx;
								left: 0;
							}
						}
						.infoR{
							width: 100%;
							.text{
								font-size: 24rpx;
								line-height: 28rpx;
								margin-bottom: 10rpx;
								display: flex;
								justify-content: space-between;
								align-items: center;
								
								&:last-child{
									margin-bottom: 0;
								}
								
								.name{
									color: #999999;
								}
								.param{
									color: #444444;
									
									text{
										margin-left: 10rpx;
									}
								}
							}
						}
					}
				}
			}
		}
	}
</style>
