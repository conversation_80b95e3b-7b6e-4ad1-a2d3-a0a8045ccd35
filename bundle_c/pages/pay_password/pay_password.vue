<template>
	<view class="pay-password">
		<view class="input-container">
			<!-- <view class="input-item">
				<view class="input-label flex normal"><text class="required">*</text>手机号</view>
				<view class="input-content">
					<u-input v-model="mobile" class="input" placeholder="请输入手机号码" :custom-style="{
						'height': '88rpx',
					}" placeholder-style="color: #90BAEB" />
				</view>
			</view>
			<view class="input-item">
				<view class="input-label flex normal"><text class="required">*</text>短信验证码</view>
				<view class="input-content">
					<u-input v-model="smsCode" placeholder="请输入验证码" :custom-style="{
						'height': '88rpx',
					}" placeholder-style="color: #90BAEB" />
					<button class="bd-primary xs primary get-code flex row-center" @click="sendSmsFun">

					<u-verification-code unique-key="forget-pwd" ref="uCode" @change="codeChange">
					</u-verification-code>
						<view class="xs">{{ codeTips }}</view>
					</button>
				</view>
			</view> -->
			<view class="input-item" v-if="type === 'update'">
				<view class="input-label flex normal"><text class="required">*</text>{{ $t('payPassword.originalPassword') }}</view>
				<view class="input-content">
					<u-input type="password" v-model="old_pay_password" :placeholder="$t('payPassword.enterOriginalPassword')" :custom-style="{
						'height': '88rpx',
					}" placeholder-style="color: #90BAEB" />
				</view>
			</view>
			<view class="input-item">
				<view class="input-label flex normal"><text class="required">*</text>{{ $t('payPassword.transactionPassword') }}</view>
				<view class="input-content">
					<u-input type="password" v-model="pay_password" :placeholder="$t('payPassword.enterTransactionPassword')" :custom-style="{
						'height': '88rpx',
					}" placeholder-style="color: #90BAEB" />
				</view>
			</view>
			<view class="input-item">
				<view class="input-label flex normal"><text class="required">*</text>{{ $t('payPassword.confirmTransactionPassword') }}</view>
				<view class="input-content">
					<u-input type="password" v-model="comfirmPwd" :placeholder="$t('payPassword.enterConfirmTransactionPassword')" :custom-style="{
						'height': '88rpx',
					}" placeholder-style="color: #90BAEB" />
				</view>
			</view>
		</view>
		<view class="login-wrap">
			<view class="btn white bg-primary flex row-center" @click="setPayPasswordFun">
				{{ $t('payPassword.confirm') }}
			</view>
		</view>
	</view>
</template>

<script>
import {
	setPayPassword,
	updatePayPassword,
	sendSms
} from '@/api/app.js'
import {
	ACCESS_TOKEN
} from '@/config/app.js'
import {
	SMSType
} from '@/utils/type.js'
import {
	mapMutations
} from 'vuex'
import pageTitleMixin from '@/mixins/page-title-mixin'
export default {
	mixins: [pageTitleMixin],
	name: 'payPassword',
	data() {
		return {
			mobile: '',
			smsCode: '',
			old_pay_password: '',
			pay_password: '',
			comfirmPwd: '',
			time: 59,
			codeTips: '',
			type:'add',
		}
	},
	onLoad(option) {
		this.type = option.type || 'add'
	},
	methods: {
		...mapMutations(['login']),
		codeChange(tip) {
			this.codeTips = tip
		},
		setPayPasswordFun() {
			let {
				mobile,
				smsCode,
				old_pay_password,
				pay_password,
				comfirmPwd
			} = this;
			// if (!mobile) {
			// 	this.$toast({
			// 		title: '请填写手机号'
			// 	});
			// 	return;
			// }
			// if (!smsCode) {
			// 	this.$toast({
			// 		title: '请填写短信验证码'
			// 	});
			// 	return;
			// }
			if(this.type === 'update' && !old_pay_password){
				this.$toast({
					title: this.$t('payPassword.pleaseEnterOriginalPassword')
				});
				return;
			}
			if (!pay_password) {
				this.$toast({
					title: this.$t('payPassword.pleaseEnterTransactionPassword')
				});
				return;
			}
			if (!comfirmPwd) {
				this.$toast({
					title: this.$t('payPassword.pleaseConfirmTransactionPassword')
				});
				return;
			}
			if (pay_password != comfirmPwd) {
				this.$toast({
					title: this.$t('payPassword.passwordsDoNotMatch')
				});
				return;
			}
			let data = {
				// mobile: mobile,
				// code: smsCode,
				pay_password: pay_password,
				confirm_password: comfirmPwd
			};

			if(this.type === 'update'){
				data.old_pay_password = old_pay_password;
				updatePayPassword(data).then(res => {
					if (res.code == 1) {
						this.$toast({
							title: res.msg
						});
						//  跳转到登录页
						setTimeout(() => {
							uni.navigateBack()
						}, 1000)
					} else {
						this.$toast({
							title: res.msg
						});
					}
				})
				return;
			}
			setPayPassword(data).then(res => {
				if (res.code == 1) {
					this.$toast({
						title: res.msg
					});
					//  跳转到登录页
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				} else {
					this.$toast({
						title: res.msg
					});
				}
			})
		},
		sendSmsFun() {
			if (!this.$refs.uCode.canGetCode) return
			if (!this.mobile) {
				this.$toast({
					title: this.$t('forgetPwd.pleaseEnterPhoneInfo')
				})
				return;
			}
			sendSms({
				mobile: this.mobile,
				key: SMSType.FINDPWD
			}).then(res => {
				if (res.code == 1) {
					this.$toast({ title: res.msg });
					this.$refs.uCode.start();
				}
			})
		}
	},
}
</script>

<style lang="scss">
page {
	padding: 0;
}

.pay-password {
	min-height: 100vh;
	padding: 40px 20px 0;
	padding: 80rpx 40rpx 0;

	.input-container {
		.input-item {
			margin-bottom: 32rpx;

			.input-label {
				flex: none;
				margin-bottom: 24rpx;
				color: #fff;
				font-size: 26rpx;

				.required {
					color: #F93E3E;
					margin-right: 5rpx;
					vertical-align: middle;
				}

				&:before {
					content: '';
					display: inline-block;
					height: 20rpx;
					width: 2rpx;
					margin-right: 10rpx;
					background-color: #fff;
				}
			}
			.input-content {
                display: flex;
                align-items: center;
                background-image: url('@/static/images/<EMAIL>');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                padding: 0 24rpx;
            }
			.get-code {
                border-radius: 0;
            }

			.bd-primary {
				height: 56rpx;
				width: 176rpx;
				flex: none;
				border: 1px solid $-color-primary;

				.seconds {
					color: $-color-primary;
				}
			}
		}
	}
	.login-wrap {
		position: fixed;
		display: flex;
		flex-direction: column;
		align-items: center;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #011750;
		padding-top: 18rpx;
		.btn {
			border-radius: 0rpx;
			width: 702rpx;
			height: 88rpx;
			margin: 20rpx 0 50rpx;
			color: #1D1D3B;
			font-size: 34rpx;
		}
	}
}
</style>
