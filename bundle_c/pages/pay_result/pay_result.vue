

<template>
	<view class="pay-result">
		<view class="contain">
			<view class="header flex-col col-center">
				<view>
					<image class="tips-icon" src="/static/images/icon_success.png"></image>
				</view>
				<view class="xl m-t-20">{{ $t('payResult.paymentSuccess') }}</view>
			</view>
			<view class="info">
				<view class="order-num flex row-between m-t-20">
					<view>{{ $t('payResult.orderNumber') }}</view>
					<view>
						{{payInfo.order_sn}}
					</view>
				</view>
				<view v-if="payInfo.pay_time" class="order-time flex row-between m-t-20">
					<view>{{ $t('payResult.paymentTime') }}</view>
					<view>{{payInfo.pay_time}}</view>
				</view>
				<view class="order-pay-type flex row-between m-t-20">
					<view>{{ $t('payResult.paymentMethod') }}</view>
					<view>{{payInfo.pay_way}}</view>
				</view>
				<view class="order-pay-money flex row-between m-t-20">
					<view>{{ $t('payResult.paymentAmount') }}</view>
					<view>
						{{payInfo.total_amount}}
					</view>
				</view>
			</view>
			<view class="line m-t-40"></view>
			<view class="m-t-40 flex-col row-center">
				<button type="primary" size="lg" class="br60 btn" @tap="handleOrder">{{ $t('payResult.viewOrder') }}</button>
				<!-- <router-link :to="getPagesUrl" navType="pushTab">
					<button type="primary" size="lg" class="br60 btn">{{ $t('payResult.viewOrder') }}</button>
				</router-link> -->
				<router-link  navType="pushTab" to="/pages/index/index">
					<button size="lg" class="br60 plain primary m-t-30">{{ $t('payResult.returnToHome') }}</button>
				</router-link>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getPayResult
	} from '@/api/order';
	import { mapGetters } from 'vuex';
	import pageTitleMixin from '@/mixins/page-title-mixin'
	export default {
		mixins: [pageTitleMixin],
		data() {
			return {
				payInfo: {},
				from: ''
			};
		},

		onLoad() {
			const options = this.$Route.query;
			console.log(options)
			this.id = options.id
			this.from = options.from
			setTimeout(()=>{
				this.getOrderResultFun();
			},1500)
		},


		methods: {
			getOrderResultFun() {
				getPayResult({
					id: this.id,
					from: this.from,
				}).then(res => {
					if (res.code == 1) {
						this.payInfo = res.data
					}
				});
			},
			handleOrder() {
				uni.redirectTo({
					url: this.getPagesUrl
				})
			}
		},
		computed: {
			...mapGetters(['currentLanguage']),
			getPagesUrl() {
				switch(this.from) {
					case 'integral': return '/bundle/pages/exchange_order/exchange_order'
					case 'buyvip': return '/bundle_c/pages/personal/personal'
					case 'recharge': return '/bundle_c/pages/record_zhuanzeng/record_zhuanzeng?source=11'
					default: return '/bundle/pages/user_order/user_order'
				}
			}
		}
	};
</script>
<style lang="scss">
	.pay-result {
		.contain {
			background-color: #011750;
			color: #fff;
			border-radius: 10rpx;
			padding: 0 30rpx 40rpx;
			position: relative;
			margin: 78rpx 20rpx 0;
			.tips-icon {
				width: 112rpx;
				height: 112rpx;
			}

			.header {
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				top: -50rpx;
			}
			.info {
				padding-top: 180rpx;
			}
			.line {
				border-top: $-solid-border;
			}
			.plain {
				border: 1px solid $-color-primary;
			}
			.btn{
				color: #1D1D3B;
			}
		}
	}
</style>
