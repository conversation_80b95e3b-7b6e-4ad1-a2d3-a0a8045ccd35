<template>
	<view class="payment-pages">

		<view class="payment u-skeleton">
			<!-- Header -->
			<view class="payment-header">
				<!-- <price-format
				  class="u-skeleton-fillet"
				  :subscript-size="40"
				  :first-size="56"
				  :second-size="40"
				  :price="amount"
				  :weight="500"
				/> -->
				<text class="price primary">{{ parseInt(amount) }} U</text>
				<template v-if="timeout > 0">
					<view class="payment-count-down">
						<text style="color: #5C81AB;">{{ $t('payment.remainingTime') }}</text>
						<u-count-down
						  :timestamp="timeout"
						  :show-days="false"
						  :show-hours="false"
						  :font-size="22"
						  bgColor="#04F9FC"
						/>
					</view>
				</template>
			</view>

			<!-- Main -->
			<view class="payment-main">
				<view class="payway-paytype">{{ $t('payment.selectPaymentMethod') }}</view>
				<view class="payway-container u-skeleton-fillet flex">
					<!-- Payway -->
					<u-radio-group v-if="ishuiju" v-model="id" class="flex-1">
						<view class="payway">
							<view
							class="payway-item"
							v-for="(item, index) in huiju"
							:key="item.id"
							@click="changePayway(item)"
							>
								<!-- <u-image
								:src="item.image"
								width="48"
								height="48"
								mode="scaleToFill"
								/> -->
								<view class="payway-item-content">
									<view class="payway-item-content-name">{{ item.name }}
										<text v-if="item.code==='daijinquan'">（ 可用代金券：{{userInfo.user_money?userInfo.user_money:'0'}} ）</text>
									</view>
									<view class="payway-item-content-tips">{{ item.extra }}</view>
								</view>
								<u-radio shape="circle" :name="item.id" active-color="#04F9FC" activeIconColor="#1D1D3B"  style="margin-right: -24rpx" />
							</view>
						</view>
					</u-radio-group>
				</view>
				<view class="payway-paytype" v-if="iskuaiqian">{{ $t('payment.quickPay') }}</view>
				<view class="payway-container u-skeleton-fillet flex">
					<!-- Payway -->

					<u-radio-group v-if="iskuaiqian" v-model="id" class="flex-1">
						<view class="payway">
							<view
							class="payway-item"
							v-for="(item, index) in kuaiqian"
							:key="item.id"
							@click="changePayway(item)"
							>
								<u-image
								:src="item.image"
								width="48"
								height="48"
								mode="scaleToFill"
								/>
								<view class="payway-item-content">
									<text class="payway-item-content-name">{{ item.name }}</text>
									<text class="payway-item-content-tips">{{ item.extra }}</text>
								</view>
								<u-radio shape="circle" :name="item.id" :active-color="colorConfig.primary" />
							</view>
						</view>
					</u-radio-group>
				</view>
				<template v-if="!huiju.length&&!kuaiqian.length">
					<view class="payway-empty">{{ $t('payment.noPaymentMethod') }}</view>
				</template>
				<template v-if="showBank">
					<view class="bank-card" v-if="hasbank">
						<view class="">
							<view class="">
								<view style="margin-left:20rpx;position:relative" @tap="selectPaycard">
									<view class="flex margin">
										<view style='font-size: 32rpx;margin-right:auto;'>{{ $t('payment.name') }}:{{bankinfo.realname}}</view>
										<view style='font-size: 28rpx;'>
											{{ $t('payment.cardNumber') }}:{{bankinfo.card_id.substr(-4)}}
										</view>
									</view>
									<view class="flex margin">
										<view style="font-size: 28rpx;margin-right:auto;">{{ $t('payment.reservedPhoneNumber') }}:{{bankinfo.tel_no}}</view>
										<view>{{bankinfo.bank_name}}</view>
									</view>
									<!-- <view class="margin" >
										<view v-if="true">
										 <u-tag text="汇聚快捷" type="error" shape="circle" plain></u-tag>
										</view>
									</view> -->
								</view>
							</view>
						</view>
					</view>
					<view class="addbank" @click="addbank">{{ $t('payment.addBankCard') }}</view>
				</template>

				<view class="flex row-right text-right" style="margin-top: 50rpx;" v-if="dikou_status == 'open'">
					<view>
						<view class="m-b-10">{{ $t('payment.ledouAmount') }}：{{userInfo.fen}}</view>
						<view class="flex row-right"><!--  v-if="userInfo.fen > 0" -->
							<u-checkbox v-model="is_dikou" shape="circle" @change="checkboxChange">
								{{ $t('payment.selectDeduction') }}
							</u-checkbox>
							<view>
								<span>{{ $t('payment.cashNeeded') }} {{cash_amount}}</span><!--  v-if="is_dikou" && dikou_amount > 0 -->
								<span class="m-l-5 m-r-5">+</span>
								<span>{{dikou_amount}}乐豆</span>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- Footer -->
			<view class="payment-footer">
				<view :class="['payment-submit', {'payment-submit--disabled': loadingPay}]" @tap="handlePrepay">
					<u-loading mode="circle" :show="loadingPay" />
					<text v-show="!loadingPay">{{ $t('payment.payNow') }}</text>
				</view>
			</view>

		</view>
		<!-- 提示输入交易密码 -->
		<u-popup v-model="showMessagePop" mode='center' border-radius="30" width='670' :closeable="true" @close="close" @open="open">
			<view style="padding: 40rpx;background-color: #fff;">
				<view style="font-size: 32rpx;margin-bottom: 50rpx;
					font-weight: bold;text-align: center;
					color: #282828;">{{ $t('payment.prompt') }}</view>
				<view class="m-b-50">
					<u-input v-model="pay_password" :customStyle="{color:'#333333'}" type="password" :border="true" :placeholder="$t('payment.enterTransactionPassword')" style="color: #282828;"/>
				</view>
				<view style="width: 100%;
					border-radius: 44rpx;
					opacity: 1;
					line-height: 80rpx;
					background: #04F9FC;
					font-size: 28rpx;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #1D1D3B;
					text-align: center;" @click="confirm">{{ $t('payment.confirm') }}</view>
			</view>
		</u-popup>
		<u-skeleton :loading="loadingSkeleton" :animation="true" bgColor="#FFF" />
	</view>

</template>


<script>
	/**
	 * @description 支付页面
	 * @query {String} from 订单来源: order-商品订单; recharge-充值订单;
	 * @query {Number} order_id	订单ID
	 */
	import {
		mapActions,
		mapGetters
	} from 'vuex'
	import { prepay, getPayway,paycardList} from '@/api/app'
	import { getUser } from '@/api/user'
	import { wxpay, alipay } from '@/utils/pay'
	import {getPayResult} from '@/api/order'

	import pageTitleMixin from '@/mixins/page-title-mixin'

	export default {
		name: 'Payment',
		mixins: [pageTitleMixin],
		data() {
			return {
				from: '',				// 订单来源
				order_id: '',			// 订单ID
				amount: 0,				// 支付金额
				timeout: 0,				// 倒计时间戳
				payment: 0,				// 支付方式
				id:0,
				// paywayList: [],			// 支付方式列表
				huiju:[],
				kuaiqian:[],
				pay_way:0,
				loadingSkeleton: true,	// 骨架屏Loading
				loadingPay: false,		// 支付处理中Loading
				url:'https://juletao.doudoule.top/mobile',
				showBank:false,
				bankinfo:{},
				hasbank:false,
				ishuiju:false,
				iskuaiqian:false,
				userInfo:{},
				is_dikou:false, //是否抵扣
				dikou_amount:'', //抵扣金额
				cash_amount:'', //
				dikou_status:'',//是否开启抵扣---后台控制
				showMessagePop: false, // 提示输入交易密码
				pay_password: '', // 交易密码
			}
		},
		onShow(){
			getUser().then(res=>{
				this.userInfo = res.data
				console.log(this.userInfo)
			})

			// this.getCardList()
			this.loadingPay = true
			setTimeout(()=>{
				this.loadingPay = false
				getPayResult({
					id: this.order_id,
					from: this.from,
				}).then(res => {
					console.log(res,99)
					if (res.code == 1&&res.data.pay_status==1) {
						uni.redirectTo({
							url:'/bundle_c/pages/pay_result/pay_result?id='+this.order_id+'&from='+this.from
						})
					}
				});
			},2500)

			/* pay_dikou({
				from: this.from,
				order_id: this.order_id,
			}).then(res =>{
				if(res.data.msg){
					console.log(res)
					//this.dikou_amount = res.data.dikou_amount
					this.$toast({title:res.data.msg})
					this.is_dikou = false
				}else{
					this.dikou_amount = res.data.dikou_amount
					this.is_dikou = true
				}
			}) */
		},
		methods: {
			open() {
				// this.showMessagePop = true
			},
			close() {
				this.showMessagePop = false
			},
			getCardList(){
				paycardList({lx:1}).then(res => {
					if(res.data.length>0){
						this.bankinfo = res.data[0];
						console.log(this.bankinfo,74777)
						this.hasbank=true
					}else{
						this.hasbank=false
					}
					uni.$on('selectPaycard', data => {
						console.log(data,'selectPaycard');
						this.bankinfo = data;
						this.hasbank=true
						console.log(this.bankinfo,789)
					})
				})

			},
			addbank(){
				uni.navigateTo({
					url: '/bundle/pages/addbank/addbank'
				})
			},
			// 选择银行卡
			selectPaycard(){
				uni.navigateTo({
					url:'/bundle/pages/banklist/banklist?source=1'

				})
			},
			// 更改支付方式
			changePayway(value) {
				console.log(value)
				// this.payment = value
				this.$set(this, 'payment', value.payment)
				this.$set(this, 'id', value.id)
				this.$set(this,'pay_way',value.pay_way)
				if(value.name=='银行卡支付'){
					this.showBank = true
					return
				}else{
					this.showBank = false
				}

				console.log(this.payment)
			},

			// 初始化页面数据
			initPageData() {
				// 获取支付方式
				getPayway({
					from: this.from,
					order_id: this.order_id,
				}).then(res => {
					if (res.code != 1) throw new Error(res.msg)
					return res.data
				}).then(data => {
					this.loadingSkeleton = false
					this.amount = data.order_amount
					this.dikou_status = data.dikou_status
					this.dikou_amount = data.dikou_amount
					this.cash_amount = data.cash_amount

					this.is_dikou = this.dikou_status === 'open' ? true : false

					if(data.pay_way[0]?.id==1&&data.pay_way[0].childlist.length>0){
						this.ishuiju = true
						this.huiju = data.pay_way[0].childlist
						this.id = this.huiju[0]?.id
						this.payment = this.huiju[0]?.payment
						this.pay_way = this.huiju[0]?.pay_way

						if(this.id&&this.payment==0){
							this.showBank = true
						}
					}
					if(data.pay_way[1]?.id==2&&data.pay_way[1].childlist.length>0){
						this.iskuaiqian = true
						this.kuaiqian = data.pay_way[1].childlist
						if(!this.id){
							this.id = this.kuaiqian[0]?.id
							this.payment = this.kuaiqian[0]?.payment
							this.pay_way = this.kuaiqian[0]?.pay_way
							if(this.id&&this.payment==0){
								this.showBank = true
							}
						}
					}
					// this.paywayList = data.pay_way
					// console.log(this.paywayList[0])
					// this.payment = this.paywayList[0]?.payment
					// console.log(this.payment)
					// if(this.huiju.length>0||this.kuaiqian.length>0){
					// 	console.log(444)
					// 	getPayResult({
					// 		id: this.order_id,
					// 		from: this.from,
					// 	}).then(res => {
					// 		console.log(res,99)
					// 		if (res.code == 1&&res.data.pay_status==1) {
					// 			uni.reLaunch({
					// 				url:'/pages/pay_result/pay_result?id='+this.order_id+'&from='+this.from
					// 			})
					// 		}
					// 	});
					// }
					// 倒计时
					const startTimestamp = new Date().getTime()
					const endTimestamp = data.cancel_time
					this.timeout = endTimestamp - (startTimestamp / 1000)
				}).catch(err => {
					throw new Error(err)
				})
			},
			// 确认交易密码
			confirm() {
				if(!this.pay_password){
					this.$toast({title: this.$t('payment.pleaseEnterTransactionPassword')})
					return
				}
				this.showMessagePop = false
				this.handlePrepay()
			},
			// 预支付处理
			handlePrepay() {
				if(this.dikou_status == 'open' && !this.is_dikou){
					this.$toast({title: this.$t('payment.pleaseSelectLedouDeduction')})
					return false
				}
				if (this.loadingPay) return
				this.loadingPay = true
				console.log(this.showBank,this.hasbank)
				if(this.showBank&&!this.hasbank){
					uni.$u.toast(this.$t('payment.pleaseBindBankCard'))
					this.loadingPay = false
					return
				}
				if(!this.pay_password){
					this.showMessagePop = true
					this.loadingPay = false
					return
				}
				// #ifdef H5
				/* if(this.payment == 2){
					this.$toast({title:'请使用APP打开支付或选择其他支付方式'})
					this.loadingPay = false
					return false
				} */
				// #endif
				console.log(this.pay_way,this.payment)

				//console.log(this.pay_way==7&&this.payment==2&&!this.userInfo.alipay_user_id)
				if(this.pay_way==7&&this.payment==2){
					let alipaydata = {
						from:this.from,
						order_id: this.order_id,
						pay_way:this.pay_way,
						payment:this.payment?this.payment:0,
						alipay_user_id:this.userInfo.alipay_user_id||'',//快钱支付宝支付所需参数
						token:this.$store.getters.token,
						amount:this.amount
					}
					if(!this.userInfo.alipay_user_id){
						let alipayUrl="https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=****************&scope=auth_user&redirect_uri=https://juletao.doudoule.top/mobile/pages/authorize/authorize?user_id="+this.userInfo.id
						let openURL="alipays://platformapi/startapp?appId=********&url="+encodeURIComponent(alipayUrl);
						document.location=openURL
					}else{
						let alipayUrl="https://juletao.doudoule.top/mobile/pages/alipay/alipay?alipaydata="+JSON.stringify(alipaydata)
						let openURL="alipays://platformapi/startapp?appId=********&url="+encodeURIComponent(alipayUrl);
						document.location=openURL
					}

					this.loadingPay = false
					return
				}
				//console.log('pay_way:88888')
				//console.log(this.from, this.order_id,this.pay_way,this.payment ,this.showBank,this.userInfo.alipay_user_id)
				prepay({
					from: this.from,
					order_id: this.order_id,
					pay_way:this.pay_way,
					payment:this.payment?this.payment:0,
					token_no:this.showBank?this.bankinfo.token_no:'',
					alipay_user_id:this.pay_way==7&&this.payment==2?this.userInfo.alipay_user_id:'',//汇聚支付宝支付所需参数
					check_dikou: this.is_dikou ? 1 : 0,
					pay_password:this.pay_password
				}).then(({ code, data ,msg}) => {
					console.log(code, data)
					//console.log(this.url)
					if(code != 1){
						this.pay_password = ''
					}
					if(code == 1){
						//调用微信支付
						uni.requestPayment({
							provider: 'weixin',
							timeStamp: data.timeStamp,
							nonceStr: data.nonceStr,
							package: data.package,
							signType: data.signType,
							paySign: data.paySign,
							success: (res) => {
								console.log('success:' + JSON.stringify(res));
								this.handleWalletPay();
							},
							fail: (err) => {
								console.log('fail:' + JSON.stringify(err));
								this.$toast({title:'支付失败:'+JSON.stringify(err)})
							}
						})
					}else if(code == 10001){
						//调用支付宝支付
						uni.requestPayment({
							provider: 'alipay',
							orderInfo: data,
							...data,
							success: (res) => {
								console.log('success:' + JSON.stringify(res));
								this.handleWalletPay();
							},
							fail: (err) => {
								console.log('fail:' + JSON.stringify(err));
								this.$toast({title:'支付失败:'+JSON.stringify(err)})
							}
						})
					}else if(code == 20001){
						console.log('代金券')
						this.handleWalletPay();
					}else if(code == 30001){
						console.log('银行卡支付')
						this.handleWalletPay();
					}else{
						this.$toast({title:msg})
					}

					// #ifdef H5
					/* if(code == 20001){
						console.log('代金券')
						this.handleWalletPay();
					}else{
						//新支付宝支付
						let url = `/bundle_c/pages/webview/paynew`
						uni.setStorageSync('Form', encodeURIComponent(data)) //form表单
						uni.navigateTo({
							//url: url
						})
					} */
					// #endif

					// #ifdef APP-PLUS
					/* let url =this.url+'&lelegeyou://'+ JSON.stringify(data)
					if(this.showBank&&code==10001){
						uni.$u.toast('支付成功')
						setTimeout((rs) => {
							uni.reLaunch({
								url:'/pages/pay_result/pay_result?id='+this.order_id+'&from='+this.from
							})
						}, 1000)
						return
					}
					switch(code) {
						case 1:
							console.log('1浏览器：',data.payurl)
							// location.href = data.payurl
							// this.handleWechatPay(data);
							// window.location.href = data.payurl
							// console.log('浏览器：',data.payurl)
							// data.payurl = 'weixin://'+data.payurl

							// let url =this.url+'&lelegeyou://'+ JSON.stringify(data)
							// let str = url.replace(this.url+'&lelegeyou://', '')
							// console.log(789)
							// // console.log(str)
							//document.location = data.payurl

							//支付宝支付--对接H5
							let url = `/bundle_c/pages/webview/pay?to=weixin&id=${this.order_id}&from=${this.from}&url=${encodeURIComponent(data.payurl)}`
							uni.navigateTo({
								url: url
							})

							// console.log('APP:',url)
							// window.open(`weixin://${data.payurl}`)
							// parent.postMessage(`weixin://${data.payurl}`,"*")

							// console.log(window.isNative,'window.isNative')
							break;
						case 10001:
							console.log('2浏览器：',data)
							//uni.navigateTo({
								//url: `/bundle_c/pages/webview/pay?to=zhifubao&id=${this.order_id}&from=${this.from}&url=${encodeURIComponent(data.payurl)}`
							//})

							//支付宝支付--对接app
							uni.requestPayment({
								provider: 'alipay',
								orderInfo: data, //支付宝
								success: function (res) {
									//console.log('success:' + JSON.stringify(res));
								},
								fail: function (err) {
									//console.log('fail:' + JSON.stringify(err));
								}
							});

							// function ready(callback) {
							//   // 如果jsbridge已经注入则直接调用
							//   if (window.AlipayJSBridge) {
							//     callback && callback();
							//   } else {
							//     // 如果没有注入则监听注入的事件
							//     document.addEventListener('AlipayJSBridgeReady', callback, false);
							//   }
							// }
							// ready(function(){
							//   document.querySelector('.tradeno').addEventListener('click', function() {
							//     AlipayJSBridge.call("tradePay", {
							//       tradeNO: "201802282100100427058809844"
							//     }, function(result) {
							//       alert(JSON.stringify(result));
							//     });
							//   });
							// });

							//document.location = "alipays://platformapi/startapp?appId=********&url="+encodeURIComponent(data.payurl);
							//console.log('APP:',url)
							//parent.postMessage(`${data.payurl}`,"*")

							// if(window.isNative){
							// 	let url = 'lelegeyou://'+ JSON.stringify(data)

							// 	document.location = url

							// }else{
							// 	parent.postMessage(`${data.payurl}`,"*")
							// }


							// plus.runtime.openURL(data.payurl);
							// this.handleAlipayPay(data);
							break;
						case 20001:
							console.log('3浏览器：',data.payurl)
							this.handleWalletPay();
							break;
					} */
					// #endif
				}).catch(err => {

				}).finally(() => {
					setTimeout(() => {
						this.loadingPay = false
					}, 1500)
				})
			},

			// 微信支付
			handleWechatPay(data) {
				wxpay(data).then(res => {

					this.handPayResult(res)
				}).catch(err => {
					console.log(err)
				})
			},

			// 支付宝支付
			handleAlipayPay(data) {
				alipay(data).then(res => {
					console.log(res)
					this.handPayResult(res)
				}).catch(err => {
					console.log(err)
				})
			},

			// 钱包余额支付
			handleWalletPay() {
				console.log('支付成功')
				//余额支付成功
				this.handPayResult('success')
			},

			// 支付后处理
			handPayResult(result) {
				// 页面出栈
				//记录支付结果
				console.log(result)
				this.result = result
				// uni.navigateBack()
				//this.$Router.back(1)
				this.$toast({title: this.$t('payment.paymentSuccess'), icon:'success'})
				setTimeout(()=>{
					uni.redirectTo({
						url:'/bundle_c/pages/pay_result/pay_result?id='+this.order_id+'&from='+this.from
					})
				},1000)
			},
			// 选中某个复选框时，由checkbox时触发
			checkboxChange(e) {
				let value = e.value
				//console.log(value)
				/* if(value){

				} */
			},
		},

		async onLoad() {
			const options = this.$Route.query
			const from = options?.from || 'trade'
			const order_id = options?.order_id
			var pages = getCurrentPages() // 获取栈实例
			let currentRoute = pages[pages.length - 1].route; // 获取当前页面路由
			console.log("路由当前页面路径"+currentRoute)
			let currentPage = pages[pages.length - 1]['$page']['fullPath'] //当前页面路径(带参数)
			console.log("哈哈哈哈哈"+currentPage)
			this.url = this.url + currentPage
			try {
				if (!from && !order_id) throw new Error('页面参数有误')
				this.from = from
				this.order_id = order_id
				this.initPageData()
			} catch(err) {
				console.log(err)
				// uni.navigateBack()
				this.$Router.back()
			}
			// this.getCardList()

		},
		onUnload() {
			switch(this.result) {
				case 'success':
					uni.$emit('payment', { result: true, order_id: this.order_id, from: this.from });
					break;
				case 'fail':
				default: uni.$emit('payment', { result: false, order_id: this.order_id, from: this.from })
			}
		},
		computed: {
			...mapGetters(['currentLanguage'])
		}
	}
</script>


<style lang="scss">
	page {
		height: 100%;
		padding: 0;
	}
	.payment-pages {
		height: 100%;

		.payment {
			display: flex;
			flex-direction: column;
			height: calc(100% - env(safe-area-inset-bottom));

			&-header {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				height: 400rpx;
				color: #FFFFFF;
				margin: 20rpx;
				background-image: url('@/static/images/<EMAIL>');
				background-size: 100% 100%;
				background-repeat: no-repeat;
				.price{
					font-size: 84rpx;
					font-weight: bold;
				}
			}


			&-main {
				flex: 1;
				margin-bottom: 20rpx;
				// margin-top: -40rpx;
				padding: 0 20rpx;
				overflow-y: scroll;
			}


			&-footer {
				display: flex;
				align-items: center;
				height: 100rpx;
				padding: 0 20rpx;
				background-color: #011750;
			}

			.payway-container {
				border-radius: 7px;

				.payway-empty {
					display: flex;
					justify-content: center;
					padding: 20rpx 0;
					font-size: 26rpx;
					color: $-color-muted;
				}
			}

			.payway {
				width: 100%;

				&-item {
					padding: 0 20rpx;
					display: flex;
					align-items: center;
					height: 120rpx;
					background-image: url('@/static/images/<EMAIL>');
					background-size: 100% 100%;
					background-repeat: no-repeat;
					&::before{
					 content: "";
					 display: block;
					 width: 10rpx;
					 height: 10rpx;
					 background-color: #0292F5;
					 border-radius: 10rpx;
					}
					&:nth-child(n+2) {
						border-top: $-dashed-border;
					}

					&-content {
						flex: 1;
						display: flex;
						flex-direction: column;
						margin-left: 16rpx;

						&-name {
							font-size: 28rpx;
							color: $-color-primary;
						}

						&-tips {
							font-size: 22rpx;
							color: $-color-muted;
						}
					}
				}
				&-paytype{
					// background-color: #fff;
					margin:20rpx 0;
					font-size: 24rpx;
					color: #fff;
					display: flex;
					align-items: center;
					&::before{
						content: '';
						display: inline-block;
						width: 2rpx;
						height: 24rpx;
						background-color: #fff;
						margin-right: 10rpx;
					}
				}
			}

			&-count-down {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 7rpx 25rpx;
				border-radius: 60px;
				margin-top: 10rpx;
				font-size: 22rpx;
				color: $-color-normal;
				.u-countdown{
					margin-left: 10rpx;
				}
			}

			&-submit {
				flex: 1;
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
				height: 74rpx;
				font-size: 28rpx;
				border-radius: 60px;
				background: $-color-primary;
				color: #1D1D3B;

				&--disabled::before {
					position: absolute;
					top: 0;
					bottom: 0;
					left: 0;
					right: 0;
					height: 100%;
					display: block;
					content: "";
					background: rgba(255, 255, 255, .3) !important;
				}
			}


		}
	}
	.bank-card{
		margin: 40rpx 0;
		padding: 20rpx;
		background: linear-gradient(270deg, #FF2C3C 0%, #F95F2F 100%);
		color: #fff;
		border-radius: 40rpx;
		.margin{
			margin: 10rpx 0;
		}
	}
	.addbank{
		text-align: center;
		color: #7f7f7f;
		text-decoration: underline;
	}
</style>
