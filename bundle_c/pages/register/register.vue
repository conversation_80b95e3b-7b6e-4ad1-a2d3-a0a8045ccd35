<template>
    <view class="register">
        <view class="input-container">
            <view class="input-item">
                <view class="input-label flex normal"><text class="required">*</text>{{ $t('register.phoneNumber') }}</view>
                <view class="input-content">
                    <u-input v-model="mobile" class="input" :placeholder="$t('register.enterPhoneNumber')" :custom-style="{
                        'height': '88rpx',
                    }" placeholder-style="color: #90BAEB" />
                </view>
                <!-- <view class="input-content m-t-24">
                    <view class="input-label flex normal"><text class="required">*</text>{{ $t('register.graphicCaptcha') }}</view>
                    <view class="captcha-container">
                        <image class="captcha-image" :src="captchaImage" @tap="refreshCaptcha"></image>
                        <view class="refresh-btn" @tap="refreshCaptcha">{{ $t('register.refreshCaptcha') }}</view>
                    </view>
                </view>
                <view class="input-content m-t-24">
                    <u-input v-model="graphicCode" :placeholder="$t('register.enterGraphicCaptcha')" :custom-style="{
                        'height': '88rpx',
                    }" placeholder-style="color: #90BAEB" />
                </view> -->
                <view class="input-content m-t-24">
                    <u-input v-model="graphicCode" :placeholder="$t('register.enterGraphicCaptcha')" :custom-style="{
                        'height': '88rpx',
                    }" placeholder-style="color: #90BAEB" />
                    <button class="bd-primary xs primary flex row-center get-code" hover-class="none" @click="refreshCaptcha" style="border: 0;">
                        <u-image class="captcha-image" :src="captchaImage" @tap="refreshCaptcha" width="176rpx" height="56rpx"></u-image>
                    </button>
                </view>
                <!-- <view class="input-content m-t-24">
                    <u-input v-model="smsCode" :placeholder="$t('register.enterVerificationCode')" :custom-style="{
                        'height': '88rpx',
                    }" placeholder-style="color: #90BAEB" />
                    <button class="bd-primary xs primary flex row-center get-code" hover-class="none"
                        @click="sendSmsFun">
                        <u-verification-code unique-key="register" ref="uCode" @change="codeChange">
                        </u-verification-code>
                        <view class="xs">{{ codeTips }}</view>
                    </button>
                </view> -->
            </view>
            <view class="input-item">
                <view class="input-label flex normal"><text class="required">*</text>{{ $t('register.setPassword') }}</view>
                <view class="input-content">
                    <u-input type="password" v-model="password" :placeholder="$t('register.passwordRequirements')" :custom-style="{
                        'height': '88rpx',
                    }" placeholder-style="color: #90BAEB" />
                </view>
            </view>
            <view class="input-item">
                <view class="input-label flex normal"><text class="required">*</text>{{ $t('register.confirmPassword') }}</view>
                <view class="input-content">
                    <u-input type="password" v-model="passwordConfirm" :placeholder="$t('register.passwordRequirements')" :custom-style="{
                        'height': '88rpx',
                    }" placeholder-style="color: #90BAEB" />
                </view>
            </view>
            <view class="input-item">
                <view class="input-label flex normal">{{ $t('register.inviteCode') }}</view>
                <view class="input-content">
                    <u-input type="text" v-model="invite_code" :placeholder="$t('register.enterInviteCode')" :custom-style="{
                        'height': '88rpx',
                    }" placeholder-style="color: #90BAEB" />
                </view>
            </view>
            <!-- #ifdef H5 -->
            <view class="download-app" @tap="handleDownload">{{ $t('register.haveAccount') }}</view>
            <!-- #endif -->
        </view>
        <view class="login-wrap">
            <view class="sm flex row-center">
                <u-checkbox v-model="isAgreement" shape="circle">
                    <div class="flex">
                        {{ $t('register.agreementPrefix') }}
                        <router-link to="/bundle/pages/server_explan/server_explan?type=0" style="margin: 0 5rpx;">
                            <view class="primary">{{ $t('register.serviceAgreement') }}</view>
                        </router-link>
                        {{ $t('register.and') }}
                        <router-link to="/bundle/pages/server_explan/server_explan?type=1" style="margin: 0 5rpx;">
                            <view class="primary">{{ $t('register.privacyAgreement') }}</view>
                        </router-link>
                    </div>
                </u-checkbox>
            </view>
            <view class="btn white bg-primary flex row-center" @click="registerFun">
                {{ $t('register.registerNow') }}
            </view>
        </view>

    </view>
</template>

<script>
import { register, sendSms, getCaptcha } from '@/api/app.js'
import { SMSType } from '@/utils/type.js'
import {
    mapMutations,
    mapGetters
} from 'vuex'
import pageTitleMixin from '@/mixins/page-title-mixin'
export default {
    name: 'register',
    mixins: [pageTitleMixin],
    created() {
        this.refreshCaptcha();
    },
    data() {
        return {
            isAgreement: false,
            mobile: '',
            smsCode: '',
            graphicCode: '', // 图形验证码输入
            password: '',
            passwordConfirm: "",
            canSendSms: true,
            codeTips: '',
            captchaImage: '', // 图形验证码图片
            uniqid: '', // 图形验证码唯一ID
            invite_code: ''
        }
    },
    onLoad() {
        // console.log(this.appConfig)
        this.invite_code = this.$Route.query.invite_code
    },
    methods: {

        ...mapMutations(['login']),
        codeChange(tip) {
            this.codeTips = tip
        },
        handleDownload() {
            uni.navigateTo({
                url: '/bundle/pages/download/download'
            })
        },
        registerFun() {
            let { isAgreement, mobile, password, smsCode, passwordConfirm, graphicCode, uniqid, invite_code } = this;
            if (!isAgreement) return this.$toast({ title: this.$t('register.pleaseAgreeToTerms') })
            if (!mobile) {
                this.$toast({ title: this.$t('register.pleaseEnterPhoneNumber') });
                return;
            }
            if (!graphicCode) {
                this.$toast({ title: this.$t('register.enterGraphicCaptcha') });
                return;
            }
            if (!password) {
                this.$toast({ title: this.$t('register.pleaseSetPassword') })
                return;
            }
            if (password != passwordConfirm) {
                this.$toast({ title: this.$t('register.passwordsDoNotMatch') })
                return;
            }

            let data = {
                mobile: mobile,
                password: password,
                code: smsCode,
                client: 2,
                graphic_code: graphicCode,
                uniqid: uniqid
            }

            if(invite_code){
                data.invite_code = invite_code;
            }

            register(data).then(res => {
                if (res.code == 1) {
                    this.login(data)
                    this.$toast({ title: res.msg });

                    setTimeout(() => {

                        // #ifdef H5
                        uni.navigateTo({
                            url: '/bundle/pages/download/download'
                        })
                        // #endif

                        // #ifndef H5
                        uni.navigateBack()
                        // #endif

                    }, 1000)
                } else {
                    // 验证码错误时刷新
                    this.refreshCaptcha();
                }
            })
        },

        countDownFinish() {
            this.canSendSms = true;
        },



        // 刷新图形验证码
        refreshCaptcha() {
            getCaptcha().then(res => {
                if (res.code == 1) {
                    this.captchaImage = res.data.image;
                    this.uniqid = res.data.uniqid;
                }
            });
        },

        sendSmsFun() {
            if (!this.$refs.uCode.canGetCode) return
            if (!this.mobile) {
                this.$toast({ title: this.$t('register.pleaseEnterPhoneInfo') })
                return;
            }
            if (!this.graphicCode) {
                this.$toast({ title: this.$t('register.enterGraphicCaptcha') })
                return;
            }

            // 添加图形验证码参数
            sendSms({
                mobile: this.mobile,
                key: SMSType.REGISTER,
                graphic_code: this.graphicCode,
                uniqid: this.uniqid
            }).then(res => {
                if (res.code == 1) {
                    this.$toast({ title: res.msg });
                    this.$refs.uCode.start();
                } else {
                    // 验证码错误时刷新
                    this.refreshCaptcha();
                }
            })
        }
    },
    computed: {
        ...mapGetters(['appConfig', 'currentLanguage']),
    }
}
</script>

<style lang="scss">
page {
    background-color: white;
}

.register {
    padding: 80rpx 24rpx;
    .download-app{
        color: $-color-primary;
    }
    .input-container {
        .captcha-container {
            display: flex;
            align-items: center;
            margin-top: 10rpx;
            margin-bottom: 10rpx;

            .captcha-image {
                width: 100%;
                height: 100%;
                background-color: #f5f5f5;
                border-radius: 8rpx;
            }

            .refresh-btn {
                margin-left: 20rpx;
                color: $-color-primary;
                font-size: 24rpx;
            }
        }

        .input-item {
            margin-bottom: 32rpx;

            .input-label {
                flex: none;
                margin-bottom: 24rpx;
                color: #fff;
                font-size: 26rpx;

                .required {
                    color: #F93E3E;
                    margin-right: 5rpx;
                    vertical-align: middle;
                }
                &:before {
                    content: '';
                    display: inline-block;
                    height: 20rpx;
                    width: 2rpx;
                    margin-right: 10rpx;
                    background-color: #fff;
                }
            }

            .bd-primary {
                height: 56rpx;
                width: 176rpx;
                flex: none;
                border: 1px solid $-color-primary;

                .seconds {
                    color: $-color-primary;
                }
            }

            .input-content {
                display: flex;
                align-items: center;
                background-image: url('@/static/images/<EMAIL>');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                padding: 0 24rpx;
            }

            .get-code {
                border-radius: 0;
            }
        }
    }


    .login-wrap {
        position: fixed;
        display: flex;
        flex-direction: column;
        align-items: center;
        bottom: 0;
        left: 0;
        width: 100%;
        background-color: #011750;
        padding-top: 18rpx;

        .btn {
            border-radius: 0rpx;
            width: 702rpx;
            height: 88rpx;
            margin: 20rpx 0 50rpx;
            color: #1D1D3B;
            font-size: 34rpx;
        }
    }
}
</style>
