<template>
	<view class="team"><!--  :style="{height:(winHeight) + 'px'}" -->
		<view class="team-header flex">
			<view class="item flex-1">
				<view class="title">{{ $t('team.teamIdTotal') }}</view>
				<view class="desc">{{ teamcount || 0 }}</view>
			</view>
			<view class="item flex-1">
				<view class="title">{{ $t('team.teamEffectiveId') }}</view>
				<view class="desc">{{ teameffectivecount || 0 }}</view>
			</view>
			<view class="item flex-1">
				<view class="title">{{ $t('team.inviteIdTotal') }}</view>
				<view class="desc">{{ zhituicount || 0 }}</view>
			</view>
			<view class="item flex-1">
				<view class="title">{{ $t('team.inviteEffectiveId') }}</view>
				<view class="desc">{{ zhituieffectivecount || 0 }}</view>
			</view>
		</view>
		<!-- 面包屑导航 -->
		<view class="breadcrumb" v-if="teamPath.length > 0">
			<view class="breadcrumb-container">
				<view class="breadcrumb-item" @click="navigateToRoot">
					<text>{{ $t('team.myTeam') }}</text>
					<text class="separator" v-if="teamPath.length > 0"> > </text>
				</view>
				<view
					class="breadcrumb-item"
					v-for="(item, index) in teamPath"
					:key="index"
					@click="navigateToLevel(index)"
				>
					<text>{{ item.nickname }}</text>
					<text class="separator" v-if="index < teamPath.length - 1"> > </text>
				</view>
			</view>
		</view>

		<mescroll-body ref="mescrollRef" @init="mescrollInit" @up="upCallback" :up="upOption" :down="{ auto: false }"
			@down="downCallback">
			<view class="main">
				<!-- 我的团队 -->
				<!-- <view class="team">
					<view class="teamCont flex row-between flex-wrap col-top">
						<view class="teamContItem">
							<view class="num">{{mytuanduiyeji || 0}}</view>
							<view class="title">{{ $t('team.teamPerformance') }}</view>
						</view>
						<view class="teamContItem">
							<view class="num">{{zhituicount || 0}}</view>
							<view class="title">{{ $t('team.directReferrals') }}</view>
						</view>
						<view class="teamContItem">
							<view class="num">{{teamcount || 0}}</view>
							<view class="title">{{ $t('team.team') }}</view>
						</view>
					</view>
				</view> -->
				<!-- <div class="" style="width: 80%;margin: 30rpx auto;">
					<u-subsection :list="list" :current="current" bg-color="#c3343f" inactive-color="#fff" @change="change"></u-subsection>
				</div> -->
				<!-- 竞拍月卡会员 -->
				<view class="user">
					<view class="user">
						<view class="list">
							<view class="newsCard" v-for="(item, index) in dataList" :key="index"
								@click="clickLook(item)">
								<view class="newsCardBox flex row-left">
									<view class="newsCardL">
										<u-avatar :size="95" :src="item.avatar"></u-avatar>
									</view>
									<view class="newsCardM flex-1">
										<view class="newsCardMTitle">{{ item.nickname }}</view>
										<view class="flex">
											<!-- newsCardR -->
											<view class="flex" style="margin-left: auto;">
												<!-- <view class="memo xs"><span>{{ $t('team.validUser') }}</span></view> -->
												<view class="memo xs"><span class="m-l-10">{{ getLevel(item) }}</span></view>
												<view class="memo xs"><span class="m-l-10">{{ item.mobile }}</span></view>
											</view>
										</view>
									</view>
									<view class="m-l-30" v-if="item.childCount > 0">
										<u-icon name="arrow-right" color="#04F9FC"></u-icon>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
import MescrollMoreItemMixin from "@/components/mescroll-uni/mixins/mescroll-more-item.js";
import { myTeam, expandChild } from "@/api/youpin.js"
import pageTitleMixin from '@/mixins/page-title-mixin'
export default {
	mixins: [MescrollMixin, MescrollMoreItemMixin,pageTitleMixin], // 使用mixin
	data() {
		return {
			top: '',
			mytuanduiyeji: '',
			zhituicount: 0,
			teamcount: 0,
			zhituieffectivecount: 0,
			teameffectivecount: 0,
			user: [],
			dataList: [],
			// 团队路径，用于面包屑导航
			teamPath: [],
			// 当前查看的团队ID，为null时表示查看根级团队
			currentTeamId: null,
			// Tabs 列表
			upOption: {
				page: {
					size: 15 // 每页数据的数量,默认10
				},
				onScroll: true,
				noMoreSize: 5, // 配置列表的总数量要大于等于5条才显示'-- END --'的提示
				empty: {
					icon: '/bundle_c/static/nodata.png',
					tip: this.$t('team.noData'), // 提示
				}
			},
			list: [
				{
					name: '直推'
				},
				{
					name: '团队'
				}
			],
			current: 0
		}
	},
	onShow() {
		this.getUser();
	},
	onLoad(option) {
		// 检查是否有团队ID参数
		if (option && option.teamId) {
			this.currentTeamId = option.teamId;

			// 如果有团队成员信息，添加到路径
			if (option.memberInfo) {
				try {
					const memberInfo = JSON.parse(decodeURIComponent(option.memberInfo));
					this.teamPath.push(memberInfo);
				} catch (e) {
					console.error('Failed to parse member info:', e);
				}
			}
		}

		setTimeout(() => {
			this.mescroll.resetUpScroll()
		}, 100)
	},
	methods: {
		...mapActions(['getUser']),
		getLevel(item) {
			let name = ''
			switch (item.level) {
				case 1:
					if(item.tiyan == 1){
						name = this.$t('team.userLevels.registeredUser') // '普通用户'
						break;
					} else if(item.tiyan == 0){
						name = this.$t('user.userLevel.validUser') // '有效会员'
						break;
					}
					break;
				case 2:
					name = this.$t('user.userLevel.v1') // 'V1'
					break;
				case 3:
					name = this.$t('user.userLevel.v2') // 'V2'
					break;
				case 4:
					name = this.$t('user.userLevel.v3') // 'V3'
					break;
				case 5:
					name = this.$t('user.userLevel.v4') // 'V4'
					break;
				case 6:
					name = this.$t('user.userLevel.v5') // 'V5'
					break;
			}
			return name
		},
		// 上拉加载
		upCallback(page) {
			const pageNum = page.num; // 页码, 默认从1开始
			const pageSize = page.size; // 页长, 默认每页10条

			// 根据当前是否有团队ID决定使用哪个API
			if (this.currentTeamId) {
				// 使用expandChild API获取子团队
				expandChild({
					page_size: pageSize,
					page_no: pageNum,
					id: this.currentTeamId,
					type: this.current + 1,
				}).then(({
					data
				}) => {
					if (page.num == 1) this.dataList = [];
					const curPageData = data.xiajiuser.list || [];
					const curPageLen = curPageData.length;
					const hasNext = !!data.xiajiuser.more;
					this.zhituicount = data?.xiajiuser.zhituicount
					this.teamcount = data?.xiajiuser.teamcount
					this.zhituieffectivecount = data?.xiajiuser.zhituieffectivecount
					this.teameffectivecount = data?.xiajiuser.teameffectivecount

					this.dataList = this.dataList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen, hasNext);
				}).catch(() => {
					this.mescroll.endErr()
				})
			} else {
				// 使用myTeam API获取根级团队
				myTeam({
					page_size: pageSize,
					page_no: pageNum,
					type: this.current + 1,
				}).then(({
					data
				}) => {
					this.mytuanduiyeji = data?.mytuanduiyeji
					this.zhituicount = data?.xiajiuser.zhituicount
					this.teamcount = data?.xiajiuser.teamcount
					this.zhituieffectivecount = data?.xiajiuser.zhituieffectivecount
					this.teameffectivecount = data?.xiajiuser.teameffectivecount

					if (page.num == 1) this.dataList = [];
					const curPageData = data.xiajiuser.list;
					const curPageLen = curPageData.length;
					const hasNext = !!data.xiajiuser.more;

					this.dataList = this.dataList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen, hasNext);
				}).catch(() => {
					this.mescroll.endErr()
				})
			}
		},
		onPageScroll(res) {
			this.top = res.scrollTop;
		},
		change(e) {
			//console.log(e)
			this.current = e
			this.mescroll.resetUpScroll()
		},
		// 点击团队成员
		clickLook(item) {
			// 如果有子团队，展开子团队
			if (item.childCount > 0) {
				// 保存当前团队成员到路径
				const memberInfo = {
					id: item.id,
					nickname: item.nickname,
					avatar: item.avatar,
					level: item.level
				};

				// 添加到团队路径
				this.teamPath.push(memberInfo);

				// 设置当前团队ID
				this.currentTeamId = item.id;
				this.dataList = [];

				// 重新加载数据
				this.mescroll.resetUpScroll();
			}
			// 如果有视频号，跳转到视频号
			else if (item.finder_id) {
				wx.openChannelsUserProfile({
					finderUserName: item.finder_id //视频号id
				})
			}
		},

		// 导航到根级团队
		navigateToRoot() {
			this.teamPath = [];
			this.currentTeamId = null;
			this.mescroll.resetUpScroll();
		},

		// 导航到指定层级
		navigateToLevel(index) {
			if (index < 0 || index >= this.teamPath.length) return;

			// 截取到指定索引（包含）
			this.teamPath = this.teamPath.slice(0, index + 1);

			// 设置当前团队ID为路径中最后一个团队的ID
			this.currentTeamId = this.teamPath[this.teamPath.length - 1].id;

			// 重新加载数据
			this.mescroll.resetUpScroll();
		},
	},
	computed: {
		...mapGetters(["userInfo"]),
	}
}
</script>

<style lang="scss" scoped>
page {
	background-color: #EFEFEF;
	height: 100%;
}

.team {
	position: relative;
	height: 100%;
	.team-header{
		padding: 24rpx;
		gap: 20rpx;
		.item{
			background-image: url('@/static/images/<EMAIL>');
			background-size: 100% 100%;
			background-repeat: no-repeat;
			padding: 24rpx;
			text-align: center;
			.title{
				font-size: 24rpx;
				color: #5C81AB;
			}
			.desc{
				font-size: 32rpx;
				font-weight: bold;
				color: #04F9FC;
			}

		}
	}
	.breadcrumb {
		padding: 10rpx 24rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 10;
		position: relative;

		&-container {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			font-size: 28rpx;
		}

		&-item {
			padding: 5rpx 0;
			display: flex;
			align-items: center;
			color: #04F9FC;

			.separator {
				margin: 0 10rpx;
				color: #fff;
			}

			&:last-child {
				color: #5C81AB;
			}
		}
	}
	/* &::after{
			content: '';
			width: 100%;
			height: 600rpx;
			display: block;
			background-color: #EFEFEF;
			position: absolute;
			bottom: 0;
			left: 0;
		} */

	.main {
		position: relative;
		z-index: 3;

		.team {
			padding: 30rpx 0;
			background: linear-gradient(180deg, #ff2c3c 0%, #ff316a 100%);

			.teamCont {
				border-radius: 24rpx;
				display: flex;
				justify-content: center;
				align-items: flex-start;
				flex-wrap: wrap;

				.teamContItem {
					width: 33.33%;
					padding: 32rpx 20rpx;
					text-align: center;
					color: #fff;

					.num {
						font-size: 35rpx;
						font-weight: bold;
						margin-bottom: 10rpx;
					}

					.title {
						font-size: 30rpx;
					}
				}
			}
		}

		.user {
			//padding: 24rpx 24rpx 24rpx 24rpx;
			border-radius: 24rpx;

			.list {
				padding: 24rpx;
				border-radius: 24rpx;

				.newsCard {
					padding: 0 24rpx;
					background-image: url('@/static/images/<EMAIL>');
					background-size: 100% 100%;
					background-repeat: no-repeat;
					margin-bottom: 24rpx;

					&:last-child .newsCardBox {
						border-bottom: 0;
					}

					.newsCardBox {
						padding: 24rpx 0;
						border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);
						display: flex;
						justify-content: flex-start;
						align-items: center;

						.newsCardL {
							width: 120rpx;
							height: 120rpx;
							border-radius: 100%;
							overflow: hidden;
							border: 2rpx solid #05C4FB;
							display: flex;
							justify-content: center;
							align-items: center;
						}

						.newsCardM {
							height: 100%;
							display: flex;
							flex-direction: column;
							justify-content: flex-end;
							align-items: flex-start;
							margin-left: 16rpx;
							.newsCardMTitle {
								font-size: 30rpx;
								line-height: 32rpx;
								font-weight: bold;
								color: $-color-primary;
								margin-bottom: auto;
								margin-bottom: 16rpx;
							}

							.memo {
								font-size: 24rpx;
								color: #A6CFFF;
							}

							.newsCardMVip {
								margin-top: 6rpx;
								font-size: 20rpx;
								color: #919191;
								display: flex;
								justify-content: flex-start;
								align-items: center;

								image {
									width: 32rpx;
									height: 32rpx;
									display: block;
									margin-right: 8rpx;
								}
							}
						}

						.newsCardR {
							//width: 37%;
							height: 100%;
							margin-left: auto;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							align-items: flex-end;

							.txt {
								width: 100%;
								color: #333;
								text-align: right;
								display: flex;
								justify-content: space-between;
								align-items: center;

								.ai {
									letter-spacing: 3rpx;
								}

								&:nth-child(2) {
									margin-top: 4rpx;
								}
							}
						}
					}
				}
			}
		}
	}
}
</style>
