<template>
	<view class="tixian">
		<view class="p-40">
			<view class="steps flex col-center row-around text-center m-t-20 m-t-40">
				<view class="stepsItem">
					<view class="m-b-20 flex col-center row-center">1</view>
					<text>提交申请</text>
				</view>
				<view class="stepsItem">
					<view class="m-b-20 flex col-center row-center">2</view>
					<text>审核打款</text>
				</view>
				<view class="stepsItem">
					<view class="m-b-20 flex col-center row-center">3</view>
					<text>提现成功</text>
				</view>
			</view>
			<view class="m-t-40">
				<view class="normal bold flex row-between col-center">
					<view class="md">提现银行</view>
					<view>
						<view class="btn text-center white sm"  @click="add" style="width:150rpx;line-height: 60rpx;
							background: linear-gradient(180deg, #ff2c3c 0%, #ff316a 100%);margin: 0 auto;border-radius: 10rpx;">添加账户</view>
					</view>
				</view>
				<view class="m-t-20 p-15" style="border: 2rpx solid #ddd;border-radius: 6rpx;position: relative;" @click="showSelect=true">
					<text>{{form.bank}}</text>
					<u-icon name="arrow-down" color="#666" size="28" style="position: absolute;right: 20rpx;top:20rpx;"></u-icon>
				</view>
			</view>
			<!-- 上传图片 -->
			<!-- <view class="flex-col m-t-40">
				<view class="md m-b-30 bold">上传发票</view>
				<view class="flex-col col-center row-center" style="width: 120rpx;height: 120rpx;border: 2rpx solid #ddd;"  @click="onChooseAvatar">
					<image class="user-avatar" style="width: 120rpx;height: 120rpx;" v-if="form.fapiao"
						:src="form.fapiao">
					</image>
					<view class="upload-image flex-col col-center row-center" v-else>
						<u-icon name="/bundle/static/icon_camera_line.png" width="54" />
					</view>
				</view>
			</view> -->
			<!-- 这一段是上面上传发票里的 -->
			<!-- <u-upload ref="uUpload" :header="{token: $store.getters.token}" :auto-upload="true" :show-progress="false" max-count="1" width="142" height="142" :custom-btn="true" :action="action" @on-success="onSuccess"  @on-remove="onRemove" >
				<view slot="addBtn" class="flex-col col-center row-center" style="width: 120rpx;height: 120rpx;border: 2rpx solid #ddd;">
					<view  class="upload-image flex-col col-center row-center" >
						<u-icon name="/bundle/static/icon_camera_line.png" width="54" />
					</view>
				</view>
			</u-upload> -->
			
			
			
			<!-- <view class="bg-primary white save-btn flex row-center lg"
				style=""
				@click="onChooseAvatar">上传付款凭证</view> -->
			
			
			<view class="flex flex-1 m-t-40">
				<text class="font-size-30 m-r-30 normal bold">提现金额</text>
				<u-input v-model="form.money" placeholder="0.00" border  :custom-style="{'font-size': '30rpx'}" />
			</view>
			<view class="m-t-40">
				<view class="font-size-30 m-r-30 normal bold">实时到账</view>
				<view class="m-t-30" style="color: #D83B52;">
					<view class="xs m-t-15">提现说明：税费6%，提供发票可以抵税</view>
					<view class="xs m-t-15">周一至周四提现本周到账，周五至周日提现下周到账，节假日顺延。</view>
				</view>
			</view>
			<view class="m-t-50">
				<view class="btn text-center white"  @click="onSubmit" style="width:100%;line-height: 80rpx;
					background: linear-gradient(180deg, #ff2c3c 0%, #ff316a 100%);margin: 0 auto;border-radius: 10rpx;">申请提现</view>
			</view>
		</view>
		<u-select v-model="showSelect" :list="list" z-index="99999" @confirm="confirm"></u-select>
		<u-popup v-model="showUploadTypePop" mode='bottom' border-radius="30" width='100vw'>
			<view class="kf_pop_wrap">
				<view class="cell" @click="selectUploadType('camera')">
					拍摄
				</view>
				<view class="cell" @click="selectUploadType('album')">
					从相册选择
				</view>
				<view class="pop_btn" @click="showUploadTypePop = false">
					取消
				</view>
			</view>
		</u-popup>
		<u-popup v-model="showMessagePop" mode='center' border-radius="30" width='670'>
			<dialogPop :MessagePop='MessagePop' @MessagePopbtn2='MessagePopbtn2'></dialogPop>
		</u-popup>
	</view>
</template>

<script>
	import { mapActions, mapMutations, mapGetters } from 'vuex'
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import MescrollMoreItemMixin from "@/components/mescroll-uni/mixins/mescroll-more-item.js";
	import {
		applyWithdraw,
		getWithdrawConfig,
		setUserInfo,
	} from "@/api/user";
	import {
		trottle
	} from "@/utils/tools";
	import {banklists} from '@/api/youpin.js'
	import dialogPop from '@/components/dialog_pop/dialog_pop.vue'
	import store from '@/store'
	import {
		baseURL,
	} from '@/config/app';
	
	import {
		uploadFile,
	} from '@/utils/tools'
	const FieldType = {
		NONE: '',
		SEX: 'sex',
		NICKNAME: 'nickname',
		AVATAR: 'avatar',
		MOBILE: 'mobile'
	}
	export default {
		mixins: [MescrollMixin,MescrollMoreItemMixin], // 使用mixin
		components: {
			dialogPop
		},
		data() {
			return {
				title:"",
				// Tabs 列表
				upOption: {
					page: {
						size: 15 // 每页数据的数量,默认10
					},
					onScroll:true,
					noMoreSize: 5, // 配置列表的总数量要大于等于5条才显示'-- END --'的提示
					empty: {
						icon: '/bundle_c/static/nodata.png',
						tip: '暂无数据', // 提示
					}
				},
				isShow: false, //弹框
				dataList:[],
				// 表单数据
				form: {
					bank: '请选择银行', // 银行
					//fapiao: '', //图片
					money: '', // 提现金额
					subbank: '', // 支行
					bank_id:-1, //银行
				},
				widthDrawConfig: {},
				showSelect: false,
				list: [],
				action: baseURL + '/api/file/formimage',
				imgUrl: '',
				fieldType: FieldType.NONE,
				showUploadTypePop: false,
				showMessagePop: false,
				MessagePop: {},
				type: null,
				lx: null,
			}
		},
		onLoad(options) {
			let type = ''
			if(options.type == "money"){
				type = "账户余额"
			}else if(options.type == "fen"){
				type = "管理奖"
				this.lx = 1
			}else if(options.type == "earnings"){
				type = "贡献值"
			}else if(options.type == "love"){
				type = "分红"
				this.lx = 2
			}else if(options.type == "daikuan"){
				type = "货款"
				this.lx = 3
			}
			uni.setNavigationBarTitle({
				title: type + '提现'
			});
			this.getWithdrawConfigFun();
			this.onSubmit = trottle(this.onSubmit, 1000, this)
		},
		onShow() {
			this.getCardList()
		},
		methods:{
			getCardList(){
				this.bankList = []
				banklists().then(res => {
					// 转换函数
					const convertToTreeData = (data) => {
						return data.map((node) => ({
							value: node.id,
							label: node.name,
						}));
					};
					this.list = convertToTreeData(res.data.lists)
				})
				uni.$on('selectPaycard', data => {
					//console.log(data,'selectPaycard');
					this.form.bank = data.name;
					this.form.subbank = data.branch // 支行
					this.form.bank_id = data.id //银行
				})
			},
			// 上拉加载
			upCallback(page) {
				const pageNum = page.num; // 页码, 默认从1开始
				const pageSize = page.size; // 页长, 默认每页10条
				
				/* myTeam({
					page_size: pageSize,
					page_no: pageNum,
				}).then(({
					data
				}) => {
					if (page.num == 1) this.dataList = [];
					const curPageData = data.xiajiuser.list;
					const curPageLen = curPageData.length;
					const hasNext = !!data.xiajiuser.more;
					//console.log(hasNext)
					this.dataList = this.dataList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen, hasNext);
				}).catch(() => {
					this.mescroll.endErr()
				}) */
				this.mescroll.endErr()
			},
			clickLook(){
				this.isShow = true
			},
			open(){
				uni.hideTabBar()
			},
			close(){
				uni.showTabBar()
			},
			async getWithdrawConfigFun() {
				const {
					code,
					data
				} = await getWithdrawConfig()
			
				if (code == 1) {
					this.widthDrawConfig = data
					this.tabsList = data.type
				}
			},
			// 提交表单
			onSubmit() {
				const data = {
					lx: this.lx,
					...this.form
				} 
				//console.log(data.bank_id)
				if (data.bank_id==-1) {
				  this.$toast({
				    title: '请选择银行'
				  });
				  return;
				}
				/* if (!data.fapiao) {
				  this.$toast({
				    title: '请上传发票'
				  });
				  return;
				} */
				if (!data.money) {
				  this.$toast({
				    title: '请输入提现金额'
				  });
				  return;
				}
				applyWithdraw(data).then(res => {
				  if (res.code == 1) {
				    this.$toast({
				      title: '提交成功'
				    }, {
				      tab: 2,
				      url: '/bundle/pages/widthdraw_result/widthdraw_result?id=' + res.data.id
				    });
				  }
				});
			},
			onSuccess(e) {
				console.log(e)
				this.form.fapiao = e.data.base_uri
			},
			onRemove(index) {
				this.form.fapiao = ""
			},
			confirm(e){
				console.log(e)
				this.form.bank = e[0].label
				this.form.bank_id = e[0].value //银行
			},
			add(){
				this.$Router.push({
					path: '/bundle_c/pages/bank_list/bank_list?source=1',
					query: {}
				})
			},
			onChooseAvatar() {
				this.showUploadTypePop = true
			},
			selectUploadType(type) {
				if (type == 'camera') {
					let authorityInfo = uni.getStorageSync('authorityInfo') ? uni.getStorageSync('authorityInfo') : {}
					console.log("authorityInfo: ", authorityInfo);
					if (authorityInfo.camera != 1 && authorityInfo.camera != 2) {
						this.MessagePop.title = '提示'
						this.MessagePop.message = '相机权限使用说明，用于拍照、录制视频等场景。'
						// this.MessagePop.btn1_text = '拒绝'
						this.MessagePop.btn2_text = '知道了'
						this.setauthority = 'camera'
						this.showUploadTypePop = false
						this.showMessagePop = true
					} else if (authorityInfo.camera == 1) {
						this.showUploadTypePop = false
						this.openChoose('camera')
					} else {
						this.showUploadTypePop = false
					}
			
				} else {
					let authorityInfo = uni.getStorageSync('authorityInfo') ? uni.getStorageSync('authorityInfo') : {}
					console.log("authorityInfo: ", authorityInfo);
					if (authorityInfo.album != 1 && authorityInfo.album != 2) {
						this.MessagePop.title = '提示'
						this.MessagePop.message = '存储权限使用说明，用于获取你的图库信息以及图像的存储。'
						// this.MessagePop.btn1_text = '拒绝'
						this.MessagePop.btn2_text = '知道了'
						this.setauthority = 'album'
						this.showUploadTypePop = false
						this.showMessagePop = true
					} else if (authorityInfo.album == 1) {
						this.showUploadTypePop = false
						this.openChoose('album')
					} else {
						this.showUploadTypePop = false
					}
				}
			},
			MessagePopbtn2() {
				if (this.MessagePop.btn2_text == '知道了') {
					if (this.setauthority == 'camera') {
						this.showMessagePop = false
						this.openChoose('camera')
					} else if (this.setauthority == 'album') {
						this.showMessagePop = false
						this.openChoose('album')
					}
				}
			},
			openChoose(type) {
				let that = this
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: [type],
					success: function(res) {
						let authorityInfo = uni.getStorageSync('authorityInfo') ? uni.getStorageSync(
							'authorityInfo') : {}
						if (that.setauthority == 'camera') {
							authorityInfo.camera = 1
						} else if (that.setauthority == 'album') {
							authorityInfo.album = 1
						}
						//uni.setStorageSync('authorityInfo', authorityInfo)
						const tempFilePaths = res.tempFilePaths;
						that.form.fapiao =  tempFilePaths[0]
						/* uni.uploadFile({
							url: that.action, //仅为示例，非真实的接口地址
							filePath: tempFilePaths[0],
							name: 'file',
							header: {
								token: store.getters.token,
								// version: '1.2.1.20210717'
							},
							success: (uploadFileRes) => {
								const res = JSON.parse(uploadFileRes.data)
								console.log(uploadFileRes, 'uploadFileRes');
								that.fieldType = 'avatar'
								//that.setUserInfoFun(res.data.uri)
								that.form.fapiao = res.data.uri
							}
						}); */
					},
					fail: function(err) {
						if (err.errMsg == 'chooseImage:fail No Permission') {
							let authorityInfo = uni.getStorageSync('authorityInfo') ? uni.getStorageSync(
								'authorityInfo') : {}
							if (that.setauthority == 'camera') {
								authorityInfo.camera = 2
							} else if (that.setauthority == 'album') {
								authorityInfo.album = 2
							}
							uni.setStorageSync('authorityInfo', authorityInfo)
						} else {
							let authorityInfo = uni.getStorageSync('authorityInfo') ? uni.getStorageSync(
								'authorityInfo') : {}
							if (that.setauthority == 'camera') {
								authorityInfo.camera = 1
							} else if (that.setauthority == 'album') {
								authorityInfo.album = 1
							}
							uni.setStorageSync('authorityInfo', authorityInfo)
						}
						console.log("err: ", err);
					}
				});
			},
			// 修改用户信息
			/* setUserInfoFun(value) {
				setUserInfo({
					field: this.fieldType,
					value: value
				}).then(res => {
					if (res.code == 1) {
						this.$toast({
							title: res.msg
						});
						this.getUserInfoFun()
					}
				})
			}, */
		},
		computed: {
			...mapGetters(['token', 'appConfig'])
		}
	}
</script>

<style lang="scss">
	.tixian{
		height: 100vh;
		background-color: #fff;
		
		.steps{
			.stepsItem{
				position: relative;
				color: #777;
				
				view{
					width: 45rpx;
					height: 45rpx;
					margin-left: auto;
					margin-right: auto;
					border-radius: 50%;
					border: 2rpx solid #777;
				}
				&:nth-child(2){
					&:before{
						content: '';
						width: 90rpx;
						height: 2rpx;
						display: block;
						position: absolute;
						top:20rpx;
						left: -95rpx;
						background-color: #777;
					}
					&::after{
						content: '';
						width: 90rpx;
						height: 2rpx;
						display: block;
						position: absolute;
						top:20rpx;
						right: -95rpx;
						background-color: #777;
					}
				}
			}
		}
		.kf_pop_wrap {
			display: flex;
			flex-direction: column;
			align-items: center;
			// background-color: #fff;
			border-radius: 30rpx;
			margin-bottom: 50rpx;
		
			.cell {
				width: 100%;
				line-height: 88rpx;
				border-bottom: 1rpx solid #F1F3F5;
				text-align: center;
				background-color: #fff;
			}
		
			.cell:first-child {
				border-radius: 20rpx 20rpx 0 0;
			}
		
			.cell:nth-of-type(2) {
				border-radius: 0 0 20rpx 20rpx;
			}
		
			.pop_btn {
				margin-top: 30rpx;
				text-align: center;
				line-height: 88rpx;
				width: 100%;
				background-color: #fff;
				border-radius: 20rpx;
			}
		}
	}
</style>