<template>
	<view class="user-wallet">
		<view class="contain m-b-24">
			<!-- 资产总计 -->
			<view class="header">
				<view class="m-b-20">
					<view class="left">
						<view class="xs title">{{ $t('userLove.totalAssets') }}</view>
						<view class="amount" style="font-size: 40rpx">{{ wallet.user_money || '0.00' }} U</view>
					</view>
					<view class="right m-t-24 flex">
						<navigator url="/pages/topup/topup" hover-class="none">
							<view class="btns">{{ $t('userLove.recharge') }}</view>
						</navigator>
						<!-- <navigator url="/pages/withdrawal/withdrawal" hover-class="none">
							<view class="btns m-l-24">{{ $t('userLove.withdraw') }}</view>
						</navigator> -->
						<!-- <navigator url="/bundle_c/pages/ledou_duihuan/ledou_duihuan_b" hover-class="none" >
							<view class="btns m-t-30" style="padding: 0 20rpx;
								height: 56rpx;
								line-height: 56rpx;
								font-size: 26rpx;
								border-radius: 32rpx;
								background-color: #F9EFE3;
								color: #AA9767;
								text-align: center;">兑换购物券</view>
						</navigator> -->
					</view>
				</view>
				<!-- <view class="money white flex">
					<view class="item">
						<view class="xs">{{ $t('userLove.totalRecharge') }}</view>
						<view style="font-size: 38rpx">{{wallet.total_recharge_amount || '0.00'}}</view>
					</view>
					<view class="item">
						<view class="xs">{{ $t('userLove.totalConsumption') }}</view>
						<view style="font-size: 38rpx">{{wallet.total_order_amount || '0.00'}}</view>
					</view>
					<template v-if="wallet.open_racharge">
						<router-link style="height: 58rpx;" class="flex primary bg-white br60 btn" size="xs"
							to="/bundle/pages/user_payment/user_payment">充值</router-link>
					</template>
</view> -->
			</view>

			<view class="nav-title">
				<view class="title">{{ $t('userLove.accountInfo') }}</view>
			</view>
			<!-- 资金明细 -->
			<view class="nav">

				<!-- <router-link class="nav-item">
					<view class="flex-col col-center">
						<image class="icon" src="../../static/icon_yezz.png"></image>
						<view class="m-t-10 sm">余额转账</view>
					</view>
				</router-link> -->
				<router-link class="nav-item flex-1" to="/bundle/pages/user_bill/user_bill?type=1">
					<view class="flex-1 flex flex-center space-between w-100">
						<!-- <image class="icon" src="../../../bundle/static/icon_zhmx.png"></image> -->
						<view class="nav-label sm flex-1">{{ $t('userLove.accountDetails') }}</view>
						<u-icon name="arrow-right" color="#5C81AB" size="24"></u-icon>
					</view>
				</router-link>
				<!-- 	<router-link class="nav-item">
					<view class="flex-col col-center">
						<image class="icon" src="../../static/icon_zzjl.png"></image>
						<view class="m-t-10 sm">转账记录</view>
					</view>
				</router-link> -->
				<!-- <router-link class="nav-item" to="/bundle/pages/recharge_record/recharge_record">
					<view class="flex-col col-center">
						<image class="icon" src="../../static/icon_czjl.png"></image>
						<view class="m-t-10 sm">充值记录</view>
					</view>
				</router-link> -->
			</view>


			<!-- 热门活动 -->
			<!-- <view class="activity">
				<view class="activity-title xl flex">
					<view class="m-r-20 bg-primary" style="width: 6rpx;height: 30rpx;"></view>
					<text>{{ $t('userLove.popularActivities') }}</text>
				</view>
				<block v-for="(item, index) in activityList" :key="item.title">
					<view class="activity-item flex row-between" :style="{backgroundColor: item.background}">
						<view>
							<view class="xl normal" style="font-weight: 500;">{{ item.title }}</view>
							<view class="muted sm m-t-10">{{ item.slogan }}</view>
							<router-link style="display: inline-block;" :to="item.href">
								<view :style="{backgroundColor: item.buttonColor}"
									class="br60 white join-btn flex row-center">{{ $t('userLove.joinNow') }}</view>
							</router-link>
						</view>
						<image style="width:274rpx; height: 210rpx;" :src="item.image"></image>
					</view>
				</block>
			</view> -->

		</view>
	</view>
</template>

<script>
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

import {
	getWallet
} from '@/api/user';
import { mapGetters } from 'vuex';
import pageTitleMixin from '@/mixins/page-title-mixin';
export default {
	mixins: [pageTitleMixin],
	data() {
		return {
			wallet: {}
		};
	},

	onShow() {
		this.getWalletFun();
	},

	computed: {
		...mapGetters(['currentLanguage']),

		// Reactive activity list that updates when language changes
		activityList() {
			return [
				{
					title: this.$t('userLove.getCoupons'),
					slogan: this.$t('userLove.dailyCoupons'),
					button: this.$t('userLove.joinNow'),
					buttonColor: "#FC597A",
					href: "/pages/get_coupon/get_coupon",
					image: "/bundle/static/img_activity_coupon.png",
					background: "rgba(252, 89, 122, 0.1)"
				},
				{
					title: this.$t('userLove.valuableProducts'),
					slogan: this.$t('userLove.latestProducts'),
					button: this.$t('userLove.joinNow'),
					buttonColor: "#FF2C3C",
					href: "/bundle/pages/goods_seckill/goods_seckill",
					image: "/bundle/static/img_activity_seckill.png",
					background: "rgba(236, 71, 37, 0.1)"
				}
			];
		}
	},

	methods: {
		getWalletFun() {
			getWallet().then(res => {
				if (res.code == 1) {
					this.wallet = res.data;
				}
			});
		}
	}
};
</script>
<style lang="scss">
.user-wallet {
	.contain {
		padding: 24rpx 24rpx 36rpx;

		.header {
			position: relative;
			padding: 28rpx 28rpx 36rpx;
			box-sizing: border-box;
			background-image: url('@/static/images/<EMAIL>');
			background-size: 100% 100%;
			background-repeat: no-repeat;
			.title{
				font-size: 24rpx;
				color: #5C81AB;
			}
			.amount{
				margin-top: 20rpx;
				font-size: 40rpx;
				color: $-color-primary;
			}

			.money {
				.item {
					flex: 1;
				}
			}

			.btns {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 202rpx;
				height: 68rpx;
				background-color: $-color-primary;
				font-weight: 400;
				font-size: 30rpx;
				color: #1D1D3B;
				line-height: 34rpx;
				text-align: center;
				font-style: normal;
			}

			.btn {
				position: absolute;
				right: 30rpx;
				top: 50rpx;
				padding: 0 51rpx;
			}
		}
		.nav-title{
			font-size: 25rpx;
			color: #fff;
			margin-top: 36rpx;
			display: flex;
			align-items: center;
			&::before{
				content: '';
				display: inline-block;
				width: 1rpx;
				height: 20rpx;
				background-color: #fff;
				margin-right: 10rpx;
			}
		}
		.nav {
			margin-top: 24rpx;
			.nav-item {
				width: 100%;
				height: 88rpx;
				display: flex;
				align-items: center;
				padding: 0 24rpx;
				background-image: url('@/static/images/<EMAIL>');
				background-size: 100% 100%;
				background-repeat: no-repeat;
				.nav-label{
					font-size: 30rpx;
					color: $-color-primary;
				}
				.icon {
					width: 52rpx;
					height: 52rpx;
				}
			}
		}
	}
}

.activity {
	padding: 40rpx 0rpx;

	.activity-title {
		font-weight: bold;
	}

	.activity-item {
		padding: 15rpx 40rpx;
		// box-shadow: 0px 0rpx 20rpx rgba(0, 0, 0, 0.16);
		margin-top: 34rpx;

		.join-btn {
			height: 52rpx;
			width: 156rpx;
			margin-top: 24rpx;
		}
	}
}
</style>
