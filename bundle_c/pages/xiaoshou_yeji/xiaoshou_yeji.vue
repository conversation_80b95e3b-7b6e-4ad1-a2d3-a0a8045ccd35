<template>
	<view class="team"><!--  :style="{height:(winHeight) + 'px'}" -->
		<u-sticky offset-top="0" h5-nav-height="0" bg-color="transparent">
			<view class="" style="width: 80%;margin: 20rpx auto 0 auto;">
				<u-subsection :list="list" :current="current" bg-color="#5c5c5c" inactive-color="#fff" @change="change"></u-subsection>
			</view>
			<view class="flex col-center row-center m-t-20 m-b-20">
				<view style="width: 50%;" class="text-center" v-if="current===0">
					<view>年冠军奖金池{{year_win_jiangjin}}</view>
					<view class="flex col-center row-between m-t-10 m-b-10">
						<view>冠军50%</view>
						<view>亚军30%</view>
						<view>季军20%</view>
					</view>
				</view>
				<view style="width: 50%;" class="text-center" v-else>
					<view>月冠军奖金池{{month_win_jiangjin}}</view>
					<view class="flex col-center row-between m-t-10 m-b-10">
						<view>冠军50%</view>
						<view>亚军30%</view>
						<view>季军20%</view>
					</view>
				</view>
			</view>
		</u-sticky>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @up="upCallback" :up="upOption" :down="{auto: false}" @down="downCallback">
			<view class="main">
				<view class="user">
					<view class="list">
						<view class="newsCard" v-for="(item,index) in dataList" :key="index" @click="clickGoChuchuang(item)">
							<view class="newsCardBox flex row-left">
								<view class="newsCardL">
									<u-avatar :size="76" :src="item.avatar"></u-avatar>
								</view>
								<view class="newsCardM">
									<view class="newsCardMTitle">{{item.nickname}}</view>
								</view>
								<view class="newsCardR">
									<view>销售业绩</view>
									{{item.saleyei}}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import { mapActions, mapMutations, mapGetters } from 'vuex'
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import MescrollMoreItemMixin from "@/components/mescroll-uni/mixins/mescroll-more-item.js";
	import { xiaofeiyejidata } from "@/api/youpin.js"
	
	export default {
		mixins: [MescrollMixin,MescrollMoreItemMixin], // 使用mixin
		data() {
			return {
				dataList:[],
				// Tabs 列表
				upOption: {
					page: {
						size: 15 // 每页数据的数量,默认10
					},
					onScroll:true,
					noMoreSize: 5, // 配置列表的总数量要大于等于5条才显示'-- END --'的提示
					empty: {
						icon: '/bundle_c/static/nodata.png',
						tip: '暂无数据', // 提示
					}
				},
				list: [
					{
						name: '年排名'
					}, 
					// {
					// 	name: '月排名'
					// }
				],
				current: 0,
				year_win_jiangjin:'',//年冠军奖金
				month_win_jiangjin:"",//月冠军奖金
			}
		},
		methods: {
			change(e){
				//console.log(e)
				this.current = e
				this.mescroll.resetUpScroll()
			},
			// 上拉加载
			upCallback(page) {
				const pageNum = page.num; // 页码, 默认从1开始
				const pageSize = page.size; // 页长, 默认每页10条
				
				xiaofeiyejidata({
					page_size: pageSize,
					page_no: pageNum,
				}).then(({
					data
				}) => {
					if (page.num == 1) this.dataList = [];
					const curPageData = this.current == 0 ? data.lists1 : data.lists;
					const curPageLen = curPageData.length;
					const hasNext = !!data.more;
					this.dataList = this.dataList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen, hasNext);
					
					this.year_win_jiangjin = data.year_win_jiangjin //年冠军奖金
					this.month_win_jiangjin = data.month_win_jiangjin//月冠军奖金
				}).catch(() => {
					this.mescroll.endErr()
				})
			},
			clickGoChuchuang(item){
				wx.openChannelsUserProfile({
				   finderUserName: item.finder_id //视频号id
				})
			},
		},
	}
</script>

<style lang="scss" scoped>
	page{
		background-color: #EFEFEF;
		height: 100%;
	}
	.team{
		position: relative;
		height: 100%;
		
		.main{
			position: relative;
			
			.user{
				padding: 24rpx 24rpx 0 24rpx;
				//background:linear-gradient(180deg, #FF5C57 0%, #FE9671 100%);
				
				.list{
					//background-color: #fff;
					.newsCard{
						padding: 0 36rpx;
						background: rgba(255, 255, 255, 0.2);
						//box-shadow: 6rpx 6rpx 10rpx linear-gradient(180deg, #5d86fa 0%, #5d86fa 100%);
						margin-bottom: 20rpx;
						border-radius: 12rpx;
						position: relative;
						border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);
						
						&:last-child{
							border-bottom: 0;
							margin-bottom: 0;
						}
						&:before{
							content: '';
							width:60rpx;
							height:70rpx;
							display: block;
							position: absolute;
							top:-10rpx;
							left: -5rpx;
							opacity: 1;
							pointer-events: none;
						}
						
						&:nth-child(1){
							background: rgba(255, 255, 255, 0.5);
							&:before{
								background: url(@/bundle_c/static/ranking/jin.png) no-repeat;
								background-size: 100% 100%;
							}
							&:after{
								content: '';
								width:60rpx;
								height:70rpx;
								display: block;
								position: absolute;
								top:-6rpx;
								left: 46rpx;
								opacity: 1;
								pointer-events: none;
								//background: url(@/bundle_c/static/ranking/guan.png) no-repeat;
								background-size: 100% 100%;
							}
						}
						&:nth-child(2){
							background: rgba(255, 255, 255, 0.4);
							&:before{
								background: url(@/bundle_c/static/ranking/yin.png) no-repeat;
								background-size: 100% 100%;
							}
							&:after{
								content: '';
								width:60rpx;
								height:70rpx;
								display: block;
								position: absolute;
								top:-6rpx;
								left: 46rpx;
								opacity: 1;
								pointer-events: none;
								//background: url(@/bundle_c/static/ranking/guan.png) no-repeat;
								background-size: 100% 100%;
							}
						}
						&:nth-child(3){
							background: rgba(255, 255, 255, 0.3);
							&:before{
								background: url(@/bundle_c/static/ranking/tong.png) no-repeat;
								background-size: 100% 100%;
							}
							&:after{
								content: '';
								width:60rpx;
								height:70rpx;
								display: block;
								position: absolute;
								top:-6rpx;
								left: 46rpx;
								opacity: 1;
								pointer-events: none;
								//background: url(@/bundle_c/static/ranking/guan.png) no-repeat;
								background-size: 100% 100%;
							}
						}
						/* &:nth-child(4),&:nth-child(5),&:nth-child(6),&:nth-child(7),&:nth-child(8),&:nth-child(9),&:nth-child(10){
							background: rgba(255, 255, 255, 0.3);
							&:before{
								//background: url(@/bundle_c/static/ranking/xing.png) no-repeat;
								background-size: 100% 100%;
							}
						} */
						
						
						.newsCardBox{
							padding: 40rpx 0;
							display: flex;
							justify-content: flex-start;
							align-items: center;
							
							.newsCardL{
								margin-right: 20rpx;
							}
							.newsCardM{
								height: 100%;
								display: flex;
								flex-direction: column;
								justify-content: flex-end;
								align-items: flex-start;
								
								.newsCardMTitle{
									font-size: 28rpx;
									line-height: 32rpx;
									font-weight: bold;
									color: #383637;
									margin-bottom: auto;
								}
								.newsCardMVip{
									margin-top: 6rpx;
									font-size: 20rpx;
									color: #919191;
									display: flex;
									justify-content: flex-start;
									align-items: center;
									
									image{
										width: 32rpx;
										height: 32rpx;
										display: block;
										margin-right: 8rpx;
									}
								}
							}
							.newsCardR{
								height: 100%;
								margin-left: auto;
								text-align: right;
								
								.txt{
									width: 100%;
									color: #333;
									text-align: right;
									/* display: flex;
									justify-content: space-between;
									align-items: center; */
									
									.ai{
										letter-spacing :3rpx;
									}
									
									&:nth-child(2){
										margin-top: 4rpx;
									}
								}
							}
						}
					}
				}
			}
		}
	}
</style>
