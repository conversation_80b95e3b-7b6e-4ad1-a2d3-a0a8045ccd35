<template>
	<view class="ziliao_guanli">
		<view class="content bg-white">
			<view class="apply-form">
				<!-- 合作类型 -->
				<view class="apply-form-item" @tap="showPop=true">
					<u-field label="合作类型" v-model="form.clabel" label-width="160" style="flex: 1;" placeholder="请选择行业类目"
						required disabled>
						<u-icon name="arrow-right" slot="right" size="28" />
					</u-field>
				</view>
				<!-- 商家名称 -->
				<view class="apply-form-item">
					<u-field label="商户简称" v-model="form.name" label-width="160" placeholder="4-20个字符" required maxlength="20" :input-attrs="{ minlength: 4 }"/>
				</view>
				<!-- 商户类型 -->
				<view class="apply-form-item" @tap="showPopType=true">
					<u-field label="商户类型" v-model="form.type" label-width="160" style="flex: 1;" placeholder="请选择商户类型"
						required disabled>
						<u-icon name="arrow-right" slot="right" size="28" />
					</u-field>
				</view>

				<!-- 联系人姓名 -->
				<view class="apply-form-item">
					<u-field label="联系人姓名" v-model="form.nickname" label-width="160" placeholder="请输入联系人姓名" required />
				</view>

				<!-- 手机号码 -->
				<view class="apply-form-item">
					<u-field label="手机号码" v-model="form.mobile" label-width="160" placeholder="请输入手机号码" required />
				</view>
				<!-- 地区 -->
				<view class="apply-form-item"  @click="showRegion = true">
					<u-field v-model="form.region" :disabled="true" label="所在地区" placeholder="请选择省、市、区" required right-icon="arrow-right">
					</u-field>
				</view>
				<!-- 地址 -->
				<view class="apply-form-item">
					<u-field v-model="form.addressObj.address" type="textarea" label="详细地址" required placeholder="请填写小区、街道、门牌号等信息"
						:field-style="{flex: 1, height: '200rpx'}" />
				</view>
				
				<!-- 验证码 -->
				<!-- <view class="apply-form-item">
					<u-field label="验证码" label-width="160" placeholder="请输入验证码" required v-model="form.code">
						<view slot="right" class="primary send-code-btn br60 flex row-center" @tap="sendSmsFun">
							<u-verification-code unique-key="store-settled" ref="uCode" @change="codeChange">
							</u-verification-code>
							<view class="xs">{{codeTips}}</view>
						</view>
					</u-field>
				</view> -->

				<!-- 上传图片 -->
				<view class="apply-form-item image">
					<u-field label="营业执照" label-width="400" :border-bottom="false"
						required disabled />
					<view>
						<u-upload ref="uUpload" :show-progress="false" :header="{token: $store.getters.token}"
							:max-count="10" width="150" height="150" :action="action" upload-text="上传图片"
							@on-success="onSuccess"  @on-remove="onRemove" />
					</view>
					<view class="muted m-t-20 m-b-30">支持jpg、png、jpeg格式的图片，最多可上传10张</view>
				</view>
				
				<!-- 上传门店LOGO -->
				<view class="apply-form-item image">
					<u-field label="上传门店LOGO" label-width="400" :border-bottom="false"
						required disabled />
					<view>
						<u-upload ref="uUpload" :show-progress="false" :header="{token: $store.getters.token}"
							:max-count="10" width="150" height="150" :action="action" upload-text="上传图片"
							@on-success="onSuccess"  @on-remove="onRemove" />
					</view>
					<view class="muted m-t-20 m-b-30">支持jpg、png、jpeg格式的图片，最多可上传10张</view>
				</view>
				<!-- 上传门店招牌 -->
				<view class="apply-form-item image">
					<u-field label="上传门店招牌" label-width="400" :border-bottom="false"
						required disabled />
					<view>
						<u-upload ref="uUpload" :show-progress="false" :header="{token: $store.getters.token}"
							:max-count="10" width="150" height="150" :action="action" upload-text="上传图片"
							@on-success="onSuccess"  @on-remove="onRemove" />
					</view>
					<view class="muted m-t-20 m-b-30">支持jpg、png、jpeg格式的图片，最多可上传10张</view>
				</view>
				
				<!-- 上传法人身份证正反面 -->
				<view class="apply-form-item image">
					<u-field label="上传法人身份证正反面" label-width="400":border-bottom="false"
						required disabled />
					<view>
						<u-upload ref="uUpload" :show-progress="false" :header="{token: $store.getters.token}"
							:max-count="10" width="150" height="150" :action="action" upload-text="上传图片"
							@on-success="onSuccess"  @on-remove="onRemove" />
					</view>
					<view class="muted m-t-20 m-b-30">支持jpg、png、jpeg格式的图片，最多可上传10张</view>
				</view>

				<!-- 同意协议 -->
				<view class="apply-form-item flex">
					<u-checkbox shape="circle" :active-color="colorConfig.primary" v-model="isAgree">
						<text class="sm">已阅读并同意</text>
					</u-checkbox>
					<router-link :to="{path: '/bundle/pages/server_explan/server_explan', query: {type: 3}}">
					<text class="primary sm">《入驻协议》</text>
					</router-link>
				</view>

				<!-- 提交申请 -->
				<view style="padding: 30rpx 20rpx 30rpx 0;">
					<button type="primary" size="lg" class="br60" @tap="onSubmit">提交申请</button>
				</view>

				<!-- 查阅记录 -->
				<router-link to="/bundle/pages/settled_recode/settled_recode">
					<view class="flex row-center muted">
						<u-icon name="order" size="32" />
						<view class="m-l-10">查看提交记录</view>
					</view>
				</router-link>
			</view>
		</view>
		<u-select v-model="showPop" mode="single-column" value-name="id" label-name="name" :list="shopCategory"
			@confirm="confirmSelect"></u-select>
		<u-select v-model="showPopType" mode="single-column" value-name="id" label-name="name" :list="shopType"
				@confirm="confirmSelectType"></u-select>
		<u-select v-model="showRegion" mode="mutil-column-auto" @confirm="regionChange" :list="lists"></u-select>
	</view>
</template>

<script>
	import {
		getShopCategory,
		shopApply
	} from "@/api/shop"
	import {
		baseURL
	} from '@/config/app'
	import {
		sendSms,
		upload
	} from '@/api/app'
	import {
		editAddress,
		getOneAddress,
		hasRegionCode,
		addAddress
	} from '@/api/user';
	import area from '@/utils/area'
	import {
		SMSType
	} from '@/utils/type'
	export default {
		data() {
			return {
				isAgree: false,
				// 表单数据
				form: {
					clabel: '',
					cid: '',
					type:'',
					type_id:'',
					name: '',
					nickname: '',
					mobile: '',
					region:'',
					addressObj: {
						province: '',
						city: '',
						district: '',
						address: '',
						is_default: false
					},
				},
				codeTips: '',
				shopCategory: [],
				shopType:[
					{id: 1, name: "线上商户"},
					{id: 2, name: "线下商户"},
				],
				showPop: false,
				showPopType:false,
				action: baseURL + '/api/file/formimage',
				fileList: [],
				uploadFile:[],
				showRegion: false,
				lists: [],
				region: '',
				addressId: '',
				defaultRegion: ['广东省', '广州市', '番禺区'],
				defaultRegionCode: '440113',
				showRegion: false,
				lists: []
			}
		},
		onLoad() {
			this.getShopCategoryFun()
			
			this.getWxAddressFun();
			this.$nextTick(() => {
				this.lists = area
			})
		},
		
		methods: {
			async getShopCategoryFun() {
				const {
					code,
					data
				} = await getShopCategory()
				if (code == 1) {
					this.shopCategory = data
				}
			},
			/* sendSmsFun() {
				if(!this.$refs.uCode.canGetCode) return
				if (!this.form.mobile) {
					this.$toast({
						title: '请填写手机号信息'
					})
					return;
				}
				sendSms({mobile: this.form.mobile, key: SMSType.SJSQYZ}).then(res => {
				    if(res.code == 1) {
				        this.$toast({title:res.msg});
				        this.$refs.uCode.start();
				    }
				})
			}, */
			codeChange(tip) {
				this.codeTips = tip
			},
			
			// 提交表单
			async onSubmit() {
				const {
					form,
					isAgree,
					fileList
				} = this
				const submitObj = {
					...form,
					license: fileList
				}
				delete submitObj.clabel
				if (!isAgree) return this.$toast({
					title: '请先同意《入驻协议》'
				})
				const {
					data,
					code,
					msg
				} = await shopApply(submitObj)
				if(code == 1) {
					this.$toast({
						title: msg
					})
					setTimeout(() => {
						this.$Router.replace({
							path: '/bundle/pages/settled_result/settled_result',
							query: {
								id: data.id
							}
						})
					},1000)
					
				}
			},
			//选择合作类型
			confirmSelect(e) {
				const {
					value,
					label
				} = e[0]
				this.form.cid = value
				this.form.clabel = label
			},
			//选择商户类型
			confirmSelectType(e){
				const {
					value,
					label
				} = e[0]
				this.form.type_id = value
				this.form.type = label
			},
			uploadChange(file, fileList){
				// console.log(file)
				// console.log(fileList)
				// this.fileList.push(fileList[0].response.data.base_uri)
				// console.log(this.fileList)
			},
			onSuccess(e) {
				this.fileList.push(e.data.base_uri)
				this.uploadFile.push(e.data)
				console.log(this.fileList)
			},
			onRemove(e) {
				this.fileList.splice(e,1)
				this.uploadFile.splice(e,1)
			},
			regionChange(region) {
				this.form.addressObj.province_id = region[0].value;
				this.form.addressObj.city_id = region[1].value;
				this.form.addressObj.district_id = region[2].value;
				this.form.region = region[0].label + " " + region[1].label + " " + region[2].label
			},
			
			getOneAddressFun() {
				getOneAddress(this.addressId).then(res => {
					if (res.code == 1) {
						let {
							city,
							province,
							district
						} = res.data;
						this.form.addressObj = res.data;
						this.form.region = `${province} ${city} ${district}`
					}
				});
			},
			
			getWxAddressFun() {
				let wxAddress = uni.getStorageSync('wxAddress');
				if (!wxAddress) return;
				wxAddress = JSON.parse(wxAddress)
				let {
					userName: contact,
					telNumber: telephone,
					provinceName: province,
					cityName: city,
					detailInfo: address
				} = wxAddress;
				let district = wxAddress.countryName || wxAddress.countyName
				hasRegionCode({
					province,
					city,
					district
				}).then(res => {
					if (res.code == 1) {
						
						if (res.data.province && res.data.city && res.data.district) {
							this.form.region = `${province} ${city} ${district}`;
							this.addressObj.province_id = res.data.province;
							this.addressObj.city_id = res.data.city;
							this.addressObj.district_id = res.data.district;
						}
						this.addressObj.contact = contact;
						this.addressObj.telephone = telephone
						this.addressObj.address = address
					}
				});
			}
		},

	}
</script>

<style lang="scss">
	page{
		height: 100% !important;
		overflow: auto !important;
	}
	.ziliao_guanli {
		padding-top: 20rpx;
		height: 100%;
		overflow-y: scroll; /* 保证垂直方向可以滚动 */

		.content {
			padding: 0 30rpx 30rpx 30rpx;

			.apply-form {
				border-radius: 8px;
				padding: 0 0 20rpx 0;

				.apply-form-item {
					::v-deep .u-field {
						padding: 30rpx 28rpx !important;
					}
					&.image{
						::v-deep .u-field {
							padding-bottom: 0 !important;
						}
					}
					.send-code-btn {
						height: 56rpx;
						width: 188rpx;
						border: 1rpx solid $-color-primary;
					}
				}

				.primary-btn {
					width: 100%;
					height: 88rpx;
					background-color: $-color-primary;
				}
			}
		}
		/deep/.upload .upload__btn {
			width: 152rpx !important;
			height: 152rpx !important;
			margin: 0 !important;
		}
		/deep/.upload__file image{
			margin: 8rpx;
			width: 100%;
			height: 100%;
		}
		.upload{
			position: relative;

			display: flex;

			
		}
		.uploadimage{
			display: flex;
			image{
				margin: 8rpx;
				width: 152rpx;
				height: 152rpx;
				// margin-right: 10rpx;
			}
		}
		// .pop-categories {
		// 	.reason-item {
		// 		padding: 24rpx 20rpx;

		// 		.reason-desc {
		// 			line-height: 46rpx;
		// 		}
		// 	}
		// }
	}
</style>
