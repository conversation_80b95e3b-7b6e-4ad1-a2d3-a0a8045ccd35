<template>
  <view class="rect-box" :style="boxStyle">
    <!-- 左上角 -->
    <u-image v-if="!borderNone" class="corner corner-left-top" :src="leftTopSrc" mode="widthFix" width="16rpx" height="16rpx"></u-image>
    <!-- 右上角 -->
    <u-image v-if="!borderNone" class="corner corner-right-top" :src="rightTopSrc" mode="widthFix" width="16rpx" height="16rpx"></u-image>
    <!-- 左下角 -->
    <u-image v-if="!borderNone" class="corner corner-left-bottom" :src="leftBottomSrc" mode="widthFix" width="16rpx" height="16rpx"></u-image>
    <!-- 右下角 -->
    <u-image v-if="!borderNone" class="corner corner-right-bottom" :src="rightBottomSrc" mode="widthFix" width="16rpx" height="16rpx"></u-image>
    
    <!-- 内容插槽 -->
    <view class="rect-box-content">
      <slot></slot>
    </view>
  </view>
</template>

<script>
import uImage from '../uview-ui/components/u-image/u-image.vue';
export default {
  components: { uImage },
  name: 'RectBox',
  props: {
    borderNone: {
      type: Boolean,
      default: false
    },
    // 左上角图片
    leftTopSrc: {
      type: String,
      default: require('@/static/images/<EMAIL>')
    },
    // 右上角图片
    rightTopSrc: {
      type: String,
      default: require('@/static/images/<EMAIL>')
    },
    // 左下角图片
    leftBottomSrc: {
      type: String,
      default: require('@/static/images/<EMAIL>')
    },
    // 右下角图片
    rightBottomSrc: {
      type: String,
      default: require('@/static/images/<EMAIL>')
    },
    // 背景色
    backgroundColor: {
      type: String,
      default: 'transparent'
    },
    // 边框颜色
    borderColor: {
      type: String,
      default: '#0292F5'
    },
    // 宽度
    width: {
      type: [String, Number],
      default: '100%'
    },
    // 高度
    height: {
      type: [String, Number],
      default: 'auto'
    },
    // 内边距
    padding: {
      type: String,
      default: '24rpx'
    },
    // 圆角大小
    borderRadius: {
      type: [String, Number],
      default: '0rpx'
    },
    // 角标大小
    cornerSize: {
      type: [String, Number],
      default: '30rpx'
    }
  },
  computed: {
    boxStyle() {
      const width = typeof this.width === 'number' ? `${this.width}rpx` : this.width;
      const height = typeof this.height === 'number' ? `${this.height}rpx` : this.height;
      const borderRadius = typeof this.borderRadius === 'number' ? `${this.borderRadius}rpx` : this.borderRadius;
      
      return {
        width,
        height,
        backgroundColor: this.backgroundColor,
        borderColor: this.borderNone ? 'transparent' : this.borderColor,
        padding: this.padding,
        borderRadius
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.rect-box {
  position: relative;
  box-sizing: border-box;
  border: 1px solid;
  overflow: hidden;
  
  .corner {
    position: absolute;
    z-index: 1;
    width: 16rpx;
    height: 16rpx;
    line-height: 0;
  }
  
  .corner-left-top {
    top: 0;
    left: 0;
  }
  
  .corner-right-top {
    top: 0;
    right: 0;
  }
  
  .corner-left-bottom {
    bottom: 0;
    left: 0;
  }
  
  .corner-right-bottom {
    bottom: 0;
    right: 0;
  }
  
  .rect-box-content {
    position: relative;
    z-index: 0;
    width: 100%;
    height: 100%;
  }
}
</style>
