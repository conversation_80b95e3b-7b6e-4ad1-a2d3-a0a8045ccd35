<template>
	<view class="">
		<!-- 消息弹框 -->
		<view class="showMessagePop_wrap">
			<view class="title">
				{{ MessagePop.title }}
			</view>
			<view class="message">
				{{ MessagePop.message }}
			</view>
			<view class="Message_btn_wrap">
				<view class="Message_btn" v-if="MessagePop.btn1_text" @click="MessagePopbtn1">
					{{ MessagePop.btn1_text }}
				</view>
				<view class="Message_btn Message_btn2" v-if="MessagePop.btn2_text" @click="MessagePopbtn2">
					{{ MessagePop.btn2_text }}
				</view>
			</view>
			<view class="btn_tip" v-if="MessagePop.btn_tip_text" @click="MessagePopbtn3">
				{{ MessagePop.btn_tip_text }}
				<u-icon name="arrow-right" color='#BFBFBF' size='24'></u-icon>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			showMessagePop:{
				type:Boolean,
				default:false,
			},
			MessagePop:{
				type:Object,
				default:{}
			}
		},
		data(){
			return{
				
			}
		},
		onLoad() {
			
		},
		onShow() {
			
		},
		methods:{
			MessagePopbtn1(){
				this.$emit('MessagePopbtn1')
			},
			MessagePopbtn2(){
				this.$emit('MessagePopbtn2')
			},
			MessagePopbtn3(){
				this.$emit('MessagePopbtn3')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.showMessagePop_wrap {
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #fff;
	
		.title {
			font-size: 28rpx;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: bold;
			color: #282828;
		}
	
		.message {
			font-size: 28rpx;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #282828;
			margin-top: 54rpx;
		}
	
		.Message_btn_wrap {
			margin-top: 60rpx;
			display: flex;
			justify-content: space-between;
			width: 100%;
	
			.Message_btn {
				width: 100%;
				border-radius: 44rpx;
				border: 2rpx solid #9F9F9F;
				line-height: 80rpx;
	
				font-size: 28rpx;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #9F9F9F;
				text-align: center;
			}
			.Message_btn:nth-of-type(n+2){
				margin-left: 62rpx;
			}
			.Message_btn2 {
				width: 100%;
				border-radius: 44rpx;
				opacity: 1;
				border: 2rpx solid #9F9F9F;
				line-height: 80rpx;
				background: linear-gradient(90deg, #282828 0%, #302722 100%);
	
				font-size: 28rpx;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #fff;
				text-align: center;
	
			}
		}
	
		.btn_tip {
			margin-top: 40rpx;
			font-size: 24rpx;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #BFBFBF;
		}
	}
</style>