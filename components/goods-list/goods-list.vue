<template>
	<view class="goods-list">
		<view v-if="type == 'waterfall'" class="goods-waterfall">
			<view v-for="(item, index) in list" :key="index" @click="ClickToLink(item)">
				<view :style="{ width: width }" class="item bg-white m-t-20">
					<view class="goods-img">
						<u-image :width="width" :height="width" :border-radius="10" :src="item.image"></u-image>
					</view>
					<view class="goods-info">
						<view class="goods-name line-2">{{ item.name }}</view>
						<view class="price mt10 row">
							<price-format :color="colorConfig.primary" class="m-r-10" :first-size="34" :second-size="26"
								:subscript-size="26" :price="item.min_price" :weight="500"></price-format>
							<price-format class="muted" :firstSize="24" :secondSize="24" :subscript-size="24"
								line-through :price="item.market_price || item.price"></price-format>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="type == 'double'" class="goods-double double flex flex-wrap row-between col-stretch p-l-20 p-r-20">
			<view class="m-t-20" v-for="(item, index) in list" :key="index" @click="ClickToLink(item)">
				<view class="item bg-white" :style="{ width: width, height: '100%' }">
					<view class="goods-img">
						<u-image :width="width" :height="width" :border-radius="10" :src="item.image"></u-image>
					</view>
					<view class="goods-info">
						<view class="goods-name line-2" :class="{ 'store-tag': item.shop_type == 1 }">{{ item.name }}
						</view>
						<view class="price mt10 row">
							<price-format :color="colorConfig.primary" class="m-r-10" :first-size="34" :second-size="26"
								:subscript-size="26" :price="item.activity_price || item.min_price"
								:weight="500"></price-format>
							<price-format class="muted" :firstSize="24" :secondSize="24" :subscript-size="24"
								line-through :price="item.market_price || item.price"></price-format>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="type === 'one' && list.length" class="goods-one m-t-20">
			<view v-for="(item, index) in list" :key="index" @click="ClickToLink(item)">
				<view class="item bg-white flex col-top">
					<view class="goods-img">
						<u-image width="200rpx" height="200rpx" :border-radius="10" :src="item.image"></u-image>
					</view>
					<view class="goods-info m-l-20 flex-1">
						<view class="goods-name line-2 m-b-10" :class="{ 'store-tag': item.shop_type == 1 }">{{
							item.name }}
						</view>
						<view class="flex row-between m-t-10">
							<view class="price m-t-10 flex">
								<price-format :color="colorConfig.primary" class="m-r-10" :first-size="34"
									:second-size="26" :subscript-size="26" :price="item.min_price || item.price"
									:weight="500"></price-format>
								<price-format class="muted" :firstSize="24" :secondSize="24" :subscript-size="24"
									line-through :price="item.market_price || item.price"></price-format>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view v-if="type.includes('row')" class="goods-row flex">
			<view class="item" v-for="(item, index) in list" :key="index" @click="ClickToLink(item)">
				<view :class="[{ 'bg-white': showBg }]">
					<view class="goods-img">
						<u-image width="240rpx" height="240rpx" :border-radius="10" :src="item.image"></u-image>
					</view>
					<view class="goods-info">
						<view class="goods-name line-1 sm">{{ item.name }}</view>
						<view class="price m-t-10 row">
							<price-format :color="colorConfig.primary" class="m-r-10" :first-size="28" :second-size="22"
								:subscript-size="22" :price="item.min_price" :weight="500"></price-format>
							<price-format class="muted" :firstSize="22" :secondSize="22" :subscript-size="22"
								line-through :price="item.market_price || item.price"></price-format>
						</view>
					</view>
					<image v-if="index < 3 && type == 'row-hot'" class="paixu"
						:src="'/static/images/No.' + index + '.png'"></image>
				</view>
			</view>
		</view>
		<view v-if="type == 'new'" class="goods-new">
			<view v-for="(item, index) in list" :key="index" @click="ClickToLink(item)">
				<view class="item flex" :class="[{ 'bg-white': showBg }]">
					<view class="goods-img">
						<u-image width="214rpx" height="214rpx" :border-radius="10" :src="item.image"></u-image>
					</view>
					<view class="goods-info flex-1 m-l-20 flex-1">
						<view class="goods-name line-2">{{ item.name }}</view>
						<view class="price m-t-20 flex row-between">
							<view class="muted xxs">{{ $t('goodsList.originalPrice') }}<price-format :first-size="24" :second-size="24"
									:subscript-size="24" :price="item.market_price"></price-format>
							</view>
							<view class="muted xxs">{{ item.sales_total }}{{ $t('goodsList.peopleBought') }}</view>
						</view>
						<view class="btn flex row-between m-t-20">
							<price-format :color="colorConfig.primary" class="m-r-10" :first-size="34" :second-size="26"
								:subscript-size="26" :price="item.min_price" :weight="500"></price-format>
							<button class="bg-primary br60 white btn" size="xs">{{ $t('goodsList.buyNow') }}</button>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="type == 'hot'" class="goods-hot">
			<view v-for="(item, index) in list" :key="index" @click="ClickToLink(item)">
				<view class="item flex bg-white m-t-20">
					<view class="goods-img">
						<u-image :lazy-load="true" width="180rpx" height="180rpx" border-radius="6rpx"
							:src="item.image" />
					</view>
					<view class="goods-info m-l-20 flex-1">
						<view class="goods-name line-2 m-b-10">{{ item.name }}</view>
						<text class="sale br60 xxs">已有{{ item.sales_total }}{{ $t('goodsList.peopleBought') }}</text>
						<view class="row-between flex m-t-10">
							<view class="price m-t-10 flex">
								<price-format :color="colorConfig.primary" class="m-r-10" :first-size="34"
									:second-size="26" :subscript-size="26" :price="item.min_price" :weight="500">
								</price-format>
								<price-format class="muted" :firstSize="24" :secondSize="24" :subscript-size="24"
									line-through :price="item.market_price"></price-format>
							</view>
							<button class="bg-primary br60 white btn" size="xs">{{ $t('goodsList.buyImmediately') }}</button>
						</view>
					</view>
					<image v-if="index < 3" class="paixu" :src="'/static/images/No.' + index + '.png'"></image>
				</view>
			</view>
		</view>
		<view v-if="type == 'activity'" class="goods-hot">
			<view v-for="(item, index) in list" :key="index" @click="ClickToLink(item)">
				<view class="item flex bg-white m-t-20">
					<view class="goods-img">
						<u-image :lazy-load="true" width="180rpx" height="180rpx" border-radius="6rpx"
							:src="item.image" />
					</view>
					<view class="goods-info m-l-20 flex-1">
						<view class="goods-name line-2 m-b-10">{{ item.name }}</view>
						<text class="views br60 xxs">{{ item.views }}浏览量</text>
						<view class="row-between flex m-t-10">
							<view class="price m-t-10 flex">
								<price-format :color="colorConfig.primary" class="m-r-10" :first-size="34"
									:second-size="26" :subscript-size="26" :price="item.price" :weight="500">
								</price-format>
								<price-format class="muted" :firstSize="24" :secondSize="24" :subscript-size="24"
									line-through :price="item.market_price"></price-format>
							</view>
							<button class="bg-primary br60 white btn" size="xs">{{ $t('goodsList.buyImmediately') }}</button>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="type == 'list-reverse'" class="goods-list-reverse">
			<view class="goods-item" v-for="(item, index) in list" :key="index" @click="ClickToLink(item)">
				<view class="goods-info">
					<view class="goods-name">
						<text>{{ item.name }}</text>
					</view>
					<view class="goods-memo">
						<!-- <text>{{ $t('goodsList.dailyIncome') }}：{{ parseInt(item.dayincome) }} U</text> -->
					</view>
					<view class="goods-price">
						<text>{{ parseInt(item.min_price) }} U</text>
					</view>
				</view>
				<view class="goods-img">
					<u-image :src="item.image" width="216rpx" height="216rpx"></u-image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
	props: {
		type: {
			type: String,
			default: 'double'
		},
		list: {
			type: Array,
			default: () => []
		},
		isBargain: {
			type: Boolean,
			default: false
		},
		// 两行时有效
		width: {
			type: String,
			default: '347rpx'
		},
		showBg: {
			type: Boolean,
			default: true
		},
	},
	data() {
		return {

		};
	},

	computed: {
		...mapGetters(['currentLanguage'])
	},
	methods: {
		ClickToLink(item) {
			this.$Router.push({
				path: '/bundle_c/pages/goods_details/goods_details',
				query: { id: item.goods_id || item.id }
			})
			if (item.product_taglink) {
				/* const link = item.product_taglink;
				const parsedLink = link.split('//')[1].split('/');
				const appId = parsedLink[0]; // 中科轻创（示例，需替换为正确的 appId）
				const path = parsedLink[1];  // 路径（MSRmeHEhQB5gh3C）
				uni.navigateToMiniProgram({
				  appId: 'wx544854a606f47067', // 替换为实际的微信小店 appId
				  path: `/pages/shop/${path}`, // 替换为小程序的实际页面路径
				  success(res) {
					console.log('跳转成功', res);
				  },
				  fail(err) {
					console.error('跳转失败', err);
				  },
				}); */
				/* uni.navigateTo({
					url:"/bundle_c/pages/webview/product"
				}) */
				//document.location = '#微信小店://中科轻创/47m3skcJXJw6nbG'
				//location.href = '#微信小店://中科轻创/47m3skcJXJw6nbG';
				/* wx.openBusinessView({
				  businessType: "weapp_shop",
				  extraData: {
					shop_id: "中科轻创", // 你的店铺 ID
					product_id: "47m3skcJXJw6nbG", // 具体商品 ID
				  },
				  success(res) {
					console.log("打开成功", res);
				  },
				  fail(err) {
					console.error("打开失败", err);
				  }
				});
*/
				/* wx.navigateToMiniProgram({
				  appId: "wx544854a606f47067", // 微信小店 AppID
				  path: "pages/productDetail/productDetail?id=47m3skcJXJw6nbG", // 确保路径正确
				  success(res) {
					console.log("跳转成功");
				  },
				  fail(err) {
					console.error("跳转失败", err);
				  }
				}); */
				/* uni.navigateToMiniProgram({
					appId: 'wx544854a606f47067',
					path: 'pages/productDetail/productDetail?id=47m3skcJXJw6nbG',
					extraData: {
					  // 这里可以传递一些额外的数据，如需要
					},
					success(res) {
					  console.log('跳转成功', res);
					},
					fail(err) {
					  console.log('跳转失败', err);
					}
				}); */

				/* wx.openBusinessView({
				  businessType: "weapp_shop",
				  extraData: {
					shop_id: "wx544854a606f47067",
					product_id: "10000175263258",
				  },
				  success(res) {
					console.log("打开成功", res);
				  },
				  fail(err) {
					console.error("打开失败", err);
				  }
				}); */
			} else {

			}
		},
	},
}
</script>

<style lang="scss" scoped>
.goods-list {

	// 自营标签
	.store-tag::before {
		content: '自营';
		font-size: 22rpx;
		color: $-color-white;
		padding: 0 10rpx;
		background: linear-gradient(267deg, #FF2C3C 0%, #F52E99 100%);
		border-radius: 6rpx;
	}

	.goods-waterfall {
		.item {
			width: 347rpx;
			border-radius: 10rpx;
			overflow: hidden;

			.goods-info {
				padding: 10rpx;

			}
		}
	}

	.goods-double {
		.item {
			width: 347rpx;
			border-radius: 10rpx;
			overflow: hidden;

			.goods-info {
				padding: 10rpx;

				.goods-name {
					height: 80rpx;
					line-height: 40rpx;
				}

			}
		}
	}

	.goods-one .item {
		padding: 20rpx;

		&:not(:last-of-type) {
			margin-bottom: 20rpx;
		}
	}

	.goods-seckill .item {
		padding: 20rpx;
	}

	.goods-new .item {
		padding: 0 20rpx 20rpx;
		border-radius: 10rpx;
	}

	.goods-row {
		.item {
			position: relative;
			width: 240rpx;
			border-radius: 16rpx;
			overflow: hidden;

			&:not(:last-of-type) {
				margin-right: 20rpx;
			}

			.goods-info {
				padding: 10rpx;
			}
		}
	}

	.goods-hot {
		.item {
			position: relative;
			padding: 30rpx 20rpx;
			border-radius: 10rpx;

			.goods-info {
				width: 450rpx;

				.sale {
					padding: 4rpx 18rpx;
					color: #F79C0C;
					background-color: rgba(247, 156, 12, .1);
				}

				.views {
					padding: 4rpx 18rpx;
					color: #ff2c3c;
					background-color: rgba(237, 83, 73, .1);
				}
			}


		}
	}

	.goods-row,
	.goods-hot {
		.paixu {
			position: absolute;
			top: 0;
			left: 20rpx;
			width: 48rpx;
			height: 60rpx;
		}
	}

	.goods-list-reverse {
		margin-top: 12rpx;
		display: flex;
		flex-direction: column;
		gap: 20rpx;

		.goods-item {
			width: 100%;
			display: flex;
			align-items: center;
			position: relative;

			.goods-info {
				display: flex;
				flex-direction: column;
				gap: 10rpx;
				flex: 1;
				min-width: 0;
				padding-left: 28rpx;

				.goods-name {
					font-size: 32rpx;
					color: $-color-normal;
					font-weight: 500;
				}

				.goods-memo {
					font-size: 24rpx;
					color: #A7CCCD;
					font-weight: 400;
				}

				.goods-price {
					font-size: 34rpx;
					color: $-color-normal;
					font-weight: 500;
					margin-top: 20rpx;
				}
			}

			.goods-img {
				width: 216rpx;
				height: 216rpx;
			}

			&::after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-image: url('@/static/images/<EMAIL>');
				background-size: 100% 100%;
				background-repeat: no-repeat;
			}
		}
	}
}
</style>
