<template>
  <view class="language-switcher">
    <u-popup v-model="showPopup" mode="bottom" border-radius="14" :mask-close-able="true">
      <view class="language-popup">
        <view class="language-popup-header">
          <text class="language-popup-title">{{ $t('settings.languageSettings') }}</text>
        </view>
        <view class="language-popup-content">
          <view
            class="language-item"
            v-for="(lang, index) in languages"
            :key="index"
            @tap="selectLanguage(lang.value)"
          >
            <view class="language-item-content">
              <text class="language-name">{{ lang.label }}</text>
              <u-icon v-if="currentLanguage === lang.value" name="checkmark" color="#04F9FC" size="32"></u-icon>
            </view>
          </view>
        </view>
        <view class="language-popup-footer">
          <u-button @click="showPopup = false" type="default">{{ $t('common.cancel') }}</u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'LanguageSwitcher',
  data() {
    return {
      showPopup: false,
      languages: [
        { label: '简体中文', value: 'zh-CN' },
        { label: 'English', value: 'en' },
        { label: '日本語', value: 'ja' },
        { label: '한국어', value: 'ko' },
        { label: 'Русский', value: 'ru' },
        { label: 'Español', value: 'es' },
        { label: 'Deutsch', value: 'de' },
        { label: 'Français', value: 'fr' }
      ]
    };
  },
  computed: {
    ...mapGetters(['currentLanguage'])
  },
  methods: {
    ...mapActions(['changeLanguage']),
    open() {
      this.showPopup = true;
    },
    selectLanguage(lang) {
      if (this.currentLanguage !== lang) {
        this.$i18n.locale = lang;
        this.changeLanguage(lang);
      }
      this.showPopup = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.language-switcher {
  width: 100%;
}

.language-popup {
  padding: 30rpx;

  &-header {
    padding-bottom: 30rpx;
    text-align: center;
    border-bottom: 1px solid #eee;

    .language-popup-title {
      font-size: 32rpx;
      font-weight: bold;
    }
  }

  &-content {
    padding: 30rpx 0;

    .language-item {
      padding: 30rpx 0;

      &-content {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .language-name {
          font-size: 28rpx;
        }
      }
    }
  }

  &-footer {
    padding-top: 30rpx;
  }
}
</style>
