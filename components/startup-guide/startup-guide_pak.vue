<template>
  <view class="startup-guide" v-if="visible">
    <view class="startup-guide__overlay" @tap="handleOverlayTap">
      <video
        id="startup-video"
        class="startup-guide__video"
        :src="videoSrc"
        :poster="posterSrc"
        :controls="false"
        :autoplay="true"
        :muted="isMuted"
        object-fit="cover"
        @ended="onVideoEnded"
        @error="onVideoError"
        @play="onVideoPlay"
        @click.stop="preventPause"
        @pause="resumePlayback"
      ></video>
      <view class="mask"></view>
      <!-- Semi-transparent Skip button in top-right corner -->
      <view class="startup-guide__skip-container">
        <view
          class="startup-guide__skip-btn"
          @click="skipVideo"
        >{{ $t('startupGuide.skip') }}</view>
      </view>

      <!-- Tap to unmute indicator (shows initially and disappears after interaction) -->
      <!-- <view class="startup-guide__unmute-hint" v-if="!hasInteracted">
        <view class="startup-guide__unmute-icon">
          <text class="iconfont icon-sound"></text>
        </view>
        <view class="startup-guide__unmute-text">{{ $t('startupGuide.tapToUnmute') }}</view>
      </view> -->
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex';
import Cache from '@/utils/cache';
import { STARTUP_GUIDE_VIEWED, APP_VERSION } from '@/config/cachekey';

export default {
  name: 'StartupGuide',
  props: {
    // Path to the video file
    videoSrc: {
      type: String,
      default: '/static/images/video.mp4'
    },
    // Path to the poster image (shown before video plays)
    posterSrc: {
      type: String,
      default: '/static/images/poster.png'
    },
    // Current app version
    appVersion: {
      type: String,
      default: '1.0.0'
    },
    // Whether to show for first-time users only
    firstTimeOnly: {
      type: Boolean,
      default: false
    },
    // Whether to show after app updates
    showAfterUpdate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      visible: false,
      hasInteracted: false,
      isMuted: false // Set to false by default to play with sound
    };
  },
  computed: {
    ...mapGetters(['currentLanguage', 'shouldShowStartupGuide'])
  },
  mounted() {
    this.checkIfShouldShow();
  },
  beforeDestroy() {
    // Make sure the tab bar is shown when the component is destroyed
    if (this.visible) {
      uni.showTabBar();
    }
  },
  methods: {
    /**
     * Check if the startup guide should be shown
     */
    checkIfShouldShow() {
      console.log('shouldShowStartupGuide', this.shouldShowStartupGuide);
      if(this.shouldShowStartupGuide){
        this.showGuide();
        return;
      }
      // const hasViewedGuide = Cache.get(STARTUP_GUIDE_VIEWED);
      // const lastAppVersion = Cache.get(APP_VERSION);

      // // Show for first-time users
      // if (!hasViewedGuide) {
      //   this.showGuide();
      //   return;
      // }

      // // Show after app updates if enabled
      // if (this.showAfterUpdate && lastAppVersion !== this.appVersion) {
      //   this.showGuide();
      //   return;
      // }

      // // Don't show if first-time only and user has viewed before
      // if (this.firstTimeOnly && hasViewedGuide) {
      //   return;
      // }
    },

    /**
     * Show the startup guide
     */
    showGuide() {
      this.visible = true;

      // Hide the tab bar to make the video truly fullscreen
      uni.hideTabBar();

      this.$nextTick(() => {
        const videoContext = uni.createVideoContext('startup-video', this);
        if (videoContext) {
          videoContext.play();
        }
      });
    },

    /**
     * Handle video play event
     */
    onVideoPlay() {
      // Try to unmute the video after it starts playing
      // This requires a user interaction somewhere on the page first
      setTimeout(() => {
        const videoContext = uni.createVideoContext('startup-video', this);
        if (videoContext) {
          // We'll try to unmute, but this might still be blocked by the browser
          // if there hasn't been user interaction
          try {
            videoContext.mute(false);
          } catch (error) {
            console.log('Could not unmute video automatically:', error);
          }
        }
      }, 1000);
    },

    /**
     * Handle video end event
     */
    onVideoEnded() {
      // Auto-close when video ends
      this.closeVideo();
    },

    /**
     * Handle video error event
     */
    onVideoError(e) {
      console.error('Video error:', e);

      // Make sure to show the tab bar in case of error
      uni.showTabBar();

      this.closeVideo();
    },

    /**
     * Skip the video and close the guide
     */
    skipVideo() {
      try {
        const videoContext = uni.createVideoContext('startup-video', this);
        if (videoContext) {
          videoContext.stop();
        }
      } catch (error) {
        console.error('Error stopping video:', error);
      }
      this.closeVideo();
    },

    /**
     * Handle tap on the overlay
     * This helps with unmuting the video after user interaction
     */
    handleOverlayTap() {
      console.log('[startup-guide] Overlay tapped');
      this.hasInteracted = true;

      // Try to unmute the video after user interaction
      const videoContext = uni.createVideoContext('startup-video', this);
      if (videoContext) {
        try {
          // First ensure the video is playing
          videoContext.play();

          // Then unmute it
          this.isMuted = false;
          videoContext.mute(false);
          console.log('[startup-guide] Video unmuted after user interaction');
        } catch (error) {
          console.log('[startup-guide] Could not unmute video:', error);
        }
      }
    },

    /**
     * Close the video guide and save that user has viewed it
     */
    closeVideo() {
      this.visible = false;

      // Show the tab bar again
      uni.showTabBar();

      // Save that user has viewed the guide
      // Cache.set(STARTUP_GUIDE_VIEWED, true);
      this.$store.commit('setShouldShowStartupGuide', false);
      Cache.set(APP_VERSION, this.appVersion);

      this.$emit('close');
    },

    /**
     * Prevent the video from being paused when clicked
     * @param {Event} e - The click event
     */
    preventPause(e) {
      console.log('[startup-guide] Video click detected, preventing pause');
      e.preventDefault();
      e.stopPropagation();

      // Ensure the video keeps playing
      const videoContext = uni.createVideoContext('startup-video', this);
      if (videoContext) {
        videoContext.play();
      }

      // Also handle unmuting if the user has interacted
      this.handleOverlayTap();
    },

    /**
     * Resume playback if the video is paused
     */
    resumePlayback() {
      console.log('[startup-guide] Video paused, resuming playback');

      // Resume playback immediately
      const videoContext = uni.createVideoContext('startup-video', this);
      if (videoContext) {
        videoContext.play();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.startup-guide {
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__video {
    width: 100%;
    height: 100vh; /* Full viewport height */
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }
  .mask{
    position: absolute;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0);
    z-index: 2;
  }
  &__skip-container {
    position: absolute;
    top: 80rpx;
    right: 40rpx;
    z-index: 3;
  }

  &__skip-btn {
    padding: 10rpx 30rpx;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    border-radius: 30rpx;
    font-size: 28rpx;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  &__unmute-hint {
    position: absolute;
    bottom: 80rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
    animation: fadeInOut 2s infinite;
  }

  &__unmute-icon {
    width: 80rpx;
    height: 80rpx;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10rpx;

    .iconfont {
      color: #fff;
      font-size: 40rpx;
    }
  }

  &__unmute-text {
    color: #fff;
    font-size: 24rpx;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 6rpx 20rpx;
    border-radius: 20rpx;
    backdrop-filter: blur(5px);
  }
}

@keyframes fadeInOut {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}
</style>
