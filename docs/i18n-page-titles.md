# Internationalization (i18n) for Page Titles

This document explains how to use the i18n page title feature to dynamically set page titles based on the user's selected language.

## Overview

The i18n page title feature allows you to:

1. Automatically set page titles based on the current language
2. Update page titles when the language changes
3. Use custom title keys for specific pages

## Implementation

The feature consists of:

1. A utility module (`utils/page-title.js`) that maps page paths to i18n translation keys
2. A mixin (`mixins/page-title-mixin.js`) that handles setting the page title in the `onShow` lifecycle hook
3. Translation keys in the locale files (`locales/en/index.js` and `locales/zh-CN/index.js`)

## How to Use

### 1. Add the Mixin to Your Page

Import the mixin and add it to your page component:

```javascript
// Import the page title mixin
import pageTitleMixin from '@/mixins/page-title-mixin';

export default {
  mixins: [pageTitleMixin],
  // ... rest of your component
};
```

### 2. Make Sure Your Page Path is Mapped

Check if your page path is mapped to a translation key in `utils/page-title.js`. If not, add it:

```javascript
const pageTitleMap = {
  // ... existing mappings
  'pages/your-page/your-page': 'pageTitles.yourPageTitle',
};
```

### 3. Add Translations for Your Page Title

Add the translation key to both locale files:

In `locales/en/index.js`:
```javascript
pageTitles: {
  // ... existing titles
  yourPageTitle: 'Your Page Title in English',
},
```

In `locales/zh-CN/index.js`:
```javascript
pageTitles: {
  // ... existing titles
  yourPageTitle: '你的页面标题（中文）',
},
```

### 4. Using Custom Title Keys

If you need to set a custom title for a specific instance of a page (e.g., a dynamic title based on page data), you can use the `setCustomPageTitle` method:

```javascript
// In your component's methods or lifecycle hooks
this.setCustomPageTitle('customTitleKey');
```

## Example

Here's a complete example of a page with i18n title support:

```javascript
<template>
  <view>
    <!-- Your page content -->
  </view>
</template>

<script>
import pageTitleMixin from '@/mixins/page-title-mixin';
import { mapGetters } from 'vuex';

export default {
  mixins: [pageTitleMixin],
  
  data() {
    return {
      // Your component data
    };
  },
  
  computed: {
    ...mapGetters(['currentLanguage']), // Required for language change detection
  },
  
  onLoad(options) {
    // If you need a dynamic title based on options
    if (options.id) {
      this.setCustomPageTitle('pageTitles.detailPage');
    }
  },
  
  // The mixin will handle setting the title in onShow
};
</script>
```

## How It Works

1. When a page is shown (`onShow` lifecycle hook), the mixin calls `setPageTitle`
2. The `setPageTitle` function gets the current page path and looks up the corresponding i18n key
3. It then uses `this.$t(key)` to get the translated title and sets it using `uni.setNavigationBarTitle`
4. When the language changes, the title is automatically updated thanks to the watcher on `currentLanguage`

## Troubleshooting

- If your page title is not being set, check if the page path is correctly mapped in `utils/page-title.js`
- If the title is not translated, check if the translation key exists in both locale files
- Make sure the mixin is correctly added to your page component
- Ensure that `currentLanguage` is included in your component's computed properties
