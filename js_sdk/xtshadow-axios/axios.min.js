!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).axios=e()}(this,function(){"use strict";var h,t,r,e;(t=h=h||{}).GET="GET",t.POST="POST",t.PUT="PUT",t.HEAD="HEAD",t.DELETE="DELETE",t.TRACE="TRACE",t.CONNECT="CONNECT",t.OPTIONS="OPTIONS",(e=r=r||{}).TEXT="text",e.ARRAYBUFFER="arraybuffer";var a=function(a){function u(t,e,o,r,n){a.call(this,t),this.config=o,this.statusCode=e,this.request=r,this.response=n,Object.setPrototypeOf(this,u.prototype)}return a&&(u.__proto__=a),(u.prototype=Object.create(a&&a.prototype)).constructor=u}(Error);function p(t,e,o,r,n){return new a(t,e,o,r,n)}function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var s=Object.prototype.toString;function i(t){return null!==t&&"object"===u(t)}function n(e,o){if(null!=e)if("object"!==u(e)&&(e=[e]),n=e,"[object Array]"===s.call(n))for(var t=0,r=e.length;t<r;t++)o.call(null,e[t],t,e);else Object.getOwnPropertyNames(e).forEach(function(t){Object.prototype.hasOwnProperty.call(e,t)&&"constructor"!==t&&o.call(null,e[t],t,e)});var n}function c(o,t,r){return n(t,function(t,e){o[e]=r&&"function"==typeof t?t.bind(r):t}),o}function f(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var o,r,n=Object.create(null);for(var a=0;a<t.length;a++){var u=t[a];for(var s in u)o=u[s],i(n[r=s]&&i(o))?n[r]=f(n[r],o):i(o)?n[r]=f({},o):n[r]=o}return n}function d(e,o){Object.keys(e).forEach(function(t){t!==o&&t.toUpperCase()===o.toUpperCase()&&(e[o]=e[t],delete e[t])})}function l(t){var e=t.header;void 0===e&&(e={});var o,r,n,a=t.method;return void 0===a&&(a=h.GET),d(o=e,"Content-Type"),d(o,"appId"),d(o,"sessionId"),n=a,(r=e=o)&&(r=f(r.common||{},r[n.toLocaleLowerCase()]||{},r),["delete","get","head","options","post","put","patch","common"].forEach(function(t){delete r[t]})),r}function y(t){var e=t.url;void 0===e&&(e="");var o,r,n=t.baseURL;return void 0===n&&(n=""),n&&!/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)&&(o=n,e=(r=e)?o.replace(/\/+$/,"")+"/"+r.replace(/^\/+/,""):o),e}function v(t){var e,o;return(e=t).cancelToken&&e.cancelToken.throwIfRequested(),(o=t).header=l(o),o.url=y(o),function(a){var t=a.url,e=a.method;void 0===e&&(e=h.GET);var u=a.data;void 0===u&&(u={});var s=a.header;void 0===s&&(s={});var i=a.responseType;void 0===i&&(i=r.TEXT);var c=a.cancelToken,f=a.validateStatus;return new Promise(function(o,r){var n=uni.request({url:t,header:s,method:e,data:u,responseType:i,success:function(t){var e={data:t.data,statusCode:t.statusCode,header:t.header};!f||f(t.statusCode)?o(e):r(p("Request failed with status code "+e.statusCode,e.statusCode,a,n,e))},fail:function(){r(p("Net Error",1001,a,n))}});c&&c.promise.then(function(t){n.abort(),r(t)})})}(t)}function o(){this.handlers=[]}function E(e,o){var r=Object.create(null);["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"].forEach(function(t){void 0!==(o=o||{})[t]?r[t]=o[t]:void 0!==e[t]&&(r[t]=e[t])});["url","method","params","data"].forEach(function(t){void 0!==(o=o||{})[t]&&(r[t]=o[t])});return["header","auth","proxy"].forEach(function(t){i((o=o||{})[t])?r[t]=f(e[t],o[t]):void 0!==o[t]?r[t]=o[t]:i(e[t])?r[t]=f(e[t]):void 0!==e[t]&&(r[t]=e[t])}),r}o.prototype.use=function(t,e){return this.handlers.push({resolved:t,rejected:e}),this.handlers.length-1},o.prototype.eject=function(t){this.handlers[t]&&(this.handlers[t]=null)},o.prototype.forEach=function(e){this.handlers.forEach(function(t){null!==t&&e(t)})};function T(t){this.defaults=t,this.interceptors={request:new o,response:new o}}T.prototype.request=function(t){t=E(this.defaults,t);var e=Promise.resolve(t),o=[{resolved:v,rejected:void 0}];for(this.interceptors.request.forEach(function(t){o.unshift(t)}),this.interceptors.response.forEach(function(t){o.push(t)});0<o.length;){var r=o.shift(),n=r.resolved,a=r.rejected;e=e.then(n,a)}return e},T.prototype.get=function(t,e){return this._requestMethodWithoutData(h.GET,t,e)},T.prototype.post=function(t,e,o){return this._requestMethodWithData(h.POST,t,e,o)},T.prototype.put=function(t,e,o){return this._requestMethodWithData(h.PUT,t,e,o)},T.prototype.delete=function(t,e){return this._requestMethodWithoutData(h.DELETE,t,e)},T.prototype.connect=function(t,e){return this._requestMethodWithoutData(h.CONNECT,t,e)},T.prototype.head=function(t,e){return this._requestMethodWithoutData(h.HEAD,t,e)},T.prototype.options=function(t,e){return this._requestMethodWithoutData(h.OPTIONS,t,e)},T.prototype.trace=function(t,e){return this._requestMethodWithoutData(h.TRACE,t,e)},T.prototype._requestMethodWithData=function(t,e,o,r){return this.request(Object.assign(r||{},{method:t,url:e,data:o}))},T.prototype._requestMethodWithoutData=function(t,e,o){return this.request(Object.assign(o||{},{method:t,url:e}))},T.prototype.getUri=function(t){return y(t=E(this.defaults,t))};function m(t){void 0===t&&(t=""),this.message=t}function b(t){var e,o=this;this.promise=new Promise(function(t){e=t}),t(function(t){o.reason||(o.reason=new m(t),e(o.reason))})}var C={baseURL:"",header:{},method:h.GET,responseType:r.TEXT,validateStatus:function(t){return 200<=t&&t<300}};function O(t){var e=new T(t),o=T.prototype.request.bind(e);return c(o,T.prototype,e),c(o,e),o}b.source=function(){var e,t=new b(function(t){e=t});return{cancel:e,token:t}},b.prototype.throwIfRequested=function(){if(this.reason)throw this.reason};var q=O(C);return q.create=function(t){return O(E(C,t))},q.CancelToken=b,q.Cancel=m,q.isCancel=function(t){return t instanceof m},q.all=function(t){return Promise.all(t)},q.spread=function(e){return function(t){return e.apply(null,t)}},q});
