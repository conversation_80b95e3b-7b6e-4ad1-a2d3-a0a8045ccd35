export default {
  pageTitles: {
    home: 'Home',
    promoterTasks: 'Promoter Tasks',
    i18nTest: 'I18n Test',
    ai: 'AI Assistant',
    gift: 'Gift',
    withdraw: 'Withdraw',
    subsidy: 'Apply Subsidy',
    bindWallet: 'Bind Wallet Address',
    materialLibrary: 'Material Library',
    agent: 'Agent',
    recharge: 'Recharge',
    userCenter: 'User Center',
    login: 'Login',
    allComments: 'All Comments',
    activityDetail: 'Activity Detail',
    flashSale: 'Flash Sale',
    memberCenter: 'Member Center',
    orderDetails: 'Order Details',
    shippingAddress: 'Shipping Address',
    addAddress: 'Add Address',
    myCoupons: 'My Coupons',
    myFavorites: 'My Favorites',
    afterSales: 'After Sales',
    applyRefund: 'Apply Refund',
    afterSalesDetail: 'After Sales Detail',
    enterTrackingNumber: 'Enter Tracking Number',
    userProfile: 'User Profile',
    myWallet: 'My Wallet',
    userRecharge: 'Recharge',
    contactCustomerService: 'Customer Service',
    servicePolicy: 'Service Policy',
    merchantSettlement: 'Merchant Settlement',
    applicationRecord: 'Application Record',
    productReviews: 'Product Reviews',
    productReviewsList: 'Reviews List',
    logisticsDetails: 'Logistics Details',
    distributionPromotion: 'Distribution Promotion',
    monthlyBill: 'Monthly Bill',
    monthlyBillDetail: 'Monthly Bill Detail',
    distributionOrders: 'Distribution Orders',
    withdrawalRecord: 'Withdrawal Record',
    withdrawalResult: 'Withdrawal Result',
    invitePoster: 'Invite Poster',
    downloadApp: 'Download App',
    accountDetails: 'Account Details',
    growthValue: 'Growth Value',
    rechargeRecord: 'Recharge Record',
    myFans: 'My Fans',
    commissionDetails: 'Commission Details',
    bargainActivity: 'Bargain Activity',

    // Bundle_c pages
    tutorial: 'Tutorial',
    beginnerTutorial: 'Beginner Tutorial',
    dividend: 'Dividend',
    wechatStore: 'WeChat Store',
    managementBonus: 'Management Bonus',
    accountBalance: 'Account Balance',
    contributionValue: 'Contribution Value',
    pendingSettlement: 'Pending Settlement',
    shoppingVouchers: 'Shopping Vouchers',
    myPoints: 'My Earnings',
    storeLoan: 'Store Loan',
    registerAccount: 'Register Account',
    forgotPassword: 'Forgot Password',
    setTransactionPassword: 'Set Transaction Password',
    productSearch: 'Product Search',
    myOrders: 'My Orders',
    dataManagement: 'Data Management',
    bankCardList: 'Bank Card List',
    addBankCard: 'Add Bank Card',
    myProducts: 'My Products',
    myShowcase: 'My Showcase',
    promoterSalesRanking: 'Promoter Sales Ranking',
    eliteTeam: 'Elite Team',
    mallNews: 'System Notice',
    newsDetails: 'News Details',
    productDetails: 'Refund Details',
    confirmOrder: 'Confirm Order',
    orderPayment: 'Order Payment',
    payResult: 'Pay Result',
  },
  startupGuide: {
    skip: 'Skip',
    tapToUnmute: 'Tap to unmute',
  },
  userIntegral: {
    totalAssets: 'Total Assets (units)',
    recharge: 'Recharge',
    gift: 'Gift',
    accountInfo: 'Account Information',
    accountDetails: 'Account Details',
    popularActivities: 'Popular Activities',
    getCoupons: 'Get Coupons',
    dailyCoupons: 'Daily coupons available',
    valuableProducts: 'Valuable Products Flash Sale',
    latestProducts: 'Latest products on sale',
    joinNow: 'Join Now',
    totalRecharge: 'Total Recharge (yuan)',
    totalConsumption: 'Total Consumption (units)',
    todayQuestions: 'Questions Asked Today',
  },
  giveaway: {
    receiverId: 'Receiver ID',
    enterReceiverId: 'Please enter receiver ID',
    giftAmount: 'Gift Amount',
    enterGiftAmount: 'Please enter gift amount',
    availableBalance: 'Available Balance',
    giftAll: 'Gift All',
    gift: 'Gift',
    pleaseEnterGiftAddress: 'Please enter gift address',
    pleaseEnterGiftAmount: 'Please enter gift amount',
    giftSuccess: 'Gift successful',
  },
  inviteFans: {
    myInviteCode: 'My Invite Code',
    inviteLink: 'Invite Link',
    clickToCopy: 'Click to Copy',
    saveToAlbum: 'Save to Album',
    longPressSaveToAlbum: 'Long press to save to album',
    saveSuccess: 'Save successful',
    saveFailed: 'Save failed',
    pleaseLongPressToSave: 'Please long press the image to save',
  },
  team: {
    teamPerformance: 'Team Performance',
    directReferrals: 'Direct Referrals',
    team: 'Team',
    myTeam: 'My Team',
    validUser: 'Valid User',
    noData: 'No data',
    teamCount: 'Team Count',
    directCount: 'Direct Count',
    effectiveCount: 'Effective Count',
    teamIdTotal: 'Total Team IDs',
    teamEffectiveId: 'Effective Team IDs',
    inviteIdTotal: 'Total Invite IDs',
    inviteEffectiveId: 'Effective Invite IDs',
    userLevels: {
      registeredUser: 'Normaler Benutzer',
      promoter: 'Promoter',
      teamLeader: 'Team Leader',
      junior: 'Junior',
      intermediate: 'Intermediate',
      senior: 'Senior',
      partner: 'Partner'
    }
  },
  userProfile: {
    tapToChangeAvatar: 'Tap to change avatar',
    updateWechatAvatar: 'Update WeChat avatar',
    nickname: 'Nickname',
    gender: 'Gender',
    phoneNumber: 'Username',
    notBound: 'Not bound',
    changePhoneNumber: 'Change phone number',
    bindPhoneNumber: 'Bind phone number',
    loginPassword: 'Login Password',
    tapToSet: 'Tap to set',
    videoChannelId: 'Video Channel ID',
    pleaseBindVideoChannel: 'Please bind video channel',
    serviceAgreement: 'Service Agreement',
    privacyPolicy: 'Privacy Policy',
    qualificationInfo: 'Qualification Info',
    languageSettings: 'Language Settings',
    aboutUs: 'About Us',
    logout: 'Logout',
    modifyUsername: 'Modify Username',
    pleaseEnterNickname: 'Please enter nickname',
    confirm: 'Confirm',
    modifyVideoChannelId: 'Modify Video Channel ID',
    pleaseEnterVideoChannelId: 'Please enter video channel ID',
    camera: 'Camera',
    album: 'Album',
    cancel: 'Cancel',
    prompt: 'Prompt',
    cameraPermissionDesc: 'Camera permission is used for taking photos, recording videos, etc.',
    albumPermissionDesc: 'Storage permission is used to access your gallery and store images.',
    iKnow: 'I know',
    male: 'Male',
    female: 'Female',
  },

  login: {
    phoneNumberLogin: 'Username Login',
    wechatAuthorization: 'WeChat Authorization',
    moreOperationsAfterLogin: 'Login to perform more operations',
    phoneNumber: 'Username',
    enterAccountOrPhoneNumber: 'Please enter username',
    password: 'Password',
    enterPassword: 'Please enter password',
    forgotPassword: 'Forgot Password',
    verificationCode: 'Verification Code',
    enterVerificationCode: 'Please enter verification code',
    graphicCaptcha: 'Graphic Verification Code',
    enterGraphicCaptcha: 'Please enter graphic verification code',
    refreshCaptcha: 'Refresh',
    smsLogin: 'SMS Login',
    accountLogin: 'Account Login',
    registerAccount: 'Register Account',
    agreementPrefix: 'agree',
    serviceAgreement: 'Service Agreement',
    and: 'and',
    privacyAgreement: 'Privacy Agreement',
    login: 'Login',
    rememberMe: 'Remember me',
    pleaseAgreeToTerms: 'Please check "I have read and agree to the Service Agreement and Privacy Agreement"',
    pleaseEnterAccount: 'Please enter username',
    pleaseEnterPassword: 'Please enter password',
    pleaseEnterPhoneNumber: 'Please enter username',
    pleaseEnterVerificationCode: 'Please enter verification code',
    loggingIn: 'Logging in...',
    confirmDelete: 'Confirm Delete',
    deleteAccountConfirm: 'Are you sure you want to delete account',
  },
  register: {
    phoneNumber: 'Username',
    enterPhoneNumber: '6-digit alphanumeric combination',
    enterVerificationCode: 'Please enter verification code',
    graphicCaptcha: 'Graphic Verification Code',
    enterGraphicCaptcha: 'Please enter graphic verification code',
    refreshCaptcha: 'Refresh',
    setPassword: 'Set Password',
    passwordRequirements: '6-20 characters with numbers + letters or symbols',
    confirmPassword: 'Confirm Password',
    agreementPrefix: 'agree',
    serviceAgreement: 'Service Agreement',
    and: 'and',
    privacyAgreement: 'Privacy Agreement',
    registerNow: 'Register Now',
    pleaseAgreeToTerms: 'Please check "I have read and agree to the Service Agreement and Privacy Agreement"',
    pleaseEnterPhoneNumber: 'Please enter username',
    pleaseSetPassword: 'Please set password',
    passwordsDoNotMatch: 'The two passwords do not match',
    pleaseEnterPhoneInfo: 'Please enter account information',
    haveAccount: 'Already have an account? Download the APP',
    inviteCode: 'Invite Code',
    enterInviteCode: 'Please enter invite code (optional)',
  },
  mescroll: {
    textInOffset: 'Pull down to refresh',
    textOutOffset: 'Release to update',
    textLoading: 'Loading...',
    textSuccess: 'Loading successful',
    textError: 'Loading failed',
    textNoMore: '',
    empty: 'Empty',
  },
  goodsList: {
    originalPrice: 'Original price',
    peopleBought: 'people bought',
    buyNow: 'Buy Now',
    buyImmediately: 'Buy Immediately',
    dailyIncome: 'Daily income',
  },
  topup: {
    rechargeChain: 'Recharge Chain',
    tronChain: 'Tron Chain',
    binanceChain: 'Binance Chain',
    pushaddress: ' topup Address',
    network: 'Network',
    address: 'Address',
    copy: 'Copy',
    rechargeInstructions: 'Recharge Instructions',
    minimumRecharge: 'Minimum Recharge',
    arrivalTime: 'Arrival Time',
    aboutOneMinute: 'About 10 minute',
    uploadVoucher: 'Upload Voucher',
    uploadImage: 'Upload Image',
    submit: 'Submit',
    copySuccess: 'Copy successful',
    pleaseUploadVoucher: 'Please upload voucher',
    uploadSuccess: 'Upload successful',
    rechargeAmount: 'Recharge Amount',
    enterRechargeAmount: 'Please enter recharge amount',
    limitRechargeAmount: 'The recharge amount cannot be less than 30U',
    viewUploadExample: 'View upload voucher example',
  },
  withdrawal: {
    withdrawalAddress: 'Withdrawal Address',
    enterWithdrawalAddress: 'Please enter address',
    withdrawalAmount: 'Withdrawal Amount',
    enterWithdrawalAmount: 'Please enter withdrawal amount',
    availableBalance: 'Available Balance',
    withdrawAll: 'Withdraw All',
    withdraw: 'Withdraw',
    pleaseEnterWithdrawalAddress: 'Please enter withdrawal address',
    pleaseEnterWithdrawalAmount: 'Please enter withdrawal amount',
    withdrawalSuccess: 'Withdrawal successful',
    prompt: 'Prompt',
    pleaseBindWalletFirst: 'Please bind wallet address first',
  },
  subsidy: {
    subsidyAmount: 'Subsidy Amount',
    enterSubsidyAmount: 'Please enter subsidy amount',
    applySubsidy: 'Apply Subsidy',
    pleaseEnterSubsidyAmount: 'Please enter subsidy amount',
    applySuccess: 'Application successful',
    applyFailed: 'Application failed',
  },
  bindWallet: {
    walletAddress: 'Binance Chain Withdrawal Address',
    enterWalletAddress: 'Please enter wallet address',
    bindWallet: 'Bind Wallet',
    pleaseEnterWalletAddress: 'Please enter wallet address',
    bindSuccess: 'Binding successful',
    bindFailed: 'Binding failed',
  },
  agent: {
    title: 'Agent',
    territory: 'Territory',
    territoryDesc: 'Join us and enjoy more privileges',
    communitySubsidies: 'Community subsidies',
    communitySubsidiesDesc: 'Receive community subsidies',
    notOpenYet: 'Not open yet',
  },
  download: {
    title: 'Download Chat GPT Decentralized Wallet',
    description: 'Trading made easy in your hands',
    downloadButton: 'Download Now',
  },
  forgetPwd: {
    phoneNumber: 'Username',
    enterPhoneNumber: 'Please enter username',
    smsVerificationCode: 'SMS Verification Code',
    enterVerificationCode: 'Please enter verification code',
    resetPassword: 'Reset Password',
    confirmPassword: 'Confirm Password',
    passwordRequirements: '6-20 characters with numbers + letters or symbols',
    enterPasswordAgain: 'Please enter password again',
    confirm: 'Confirm',
    pleaseEnterPhoneNumber: 'Please enter username',
    pleaseEnterVerificationCode: 'Please enter verification code',
    pleaseEnterResetPassword: 'Please enter reset password',
    pleaseEnterConfirmPassword: 'Please enter confirm password',
    passwordsDoNotMatch: 'The two passwords do not match',
    pleaseEnterPhoneInfo: 'Please enter account information',
    originalPassword: 'Original Password',
    enterOriginalPassword: 'Please enter original password',
  },
  userBill: {
    all: 'All',
    income: 'Income',
    expenditure: 'Expenditure',
    noData: 'No data',
  },
  userLove: {
    myBalance: 'My Balance',
    totalAssets: 'Total Assets (units)',
    recharge: 'Recharge',
    withdraw: 'Withdraw',
    accountInfo: 'Account Information',
    accountDetails: 'Account Details',
    popularActivities: 'Popular Activities',
    getCoupons: 'Get Coupons',
    dailyCoupons: 'Daily coupons available',
    valuableProducts: 'Valuable Products Flash Sale',
    latestProducts: 'Latest products on sale',
    joinNow: 'Join Now',
    totalRecharge: 'Total Recharge (yuan)',
    totalConsumption: 'Total Consumption (units)',
  },
  userOrder: {
    all: 'All',
    pendingPayment: 'Pending Payment',
    pendingDelivery: 'Pending Delivery',
    pendingReceipt: 'Pending Receipt',
    completed: 'Completed',
    closed: 'Closed',
    noOrders: 'No orders yet~',
    refundRent: 'Refund Rent',
    actualPayment: 'Actual Payment',
    originalPrice: 'Original Price',
    status: 'Status',
    paymentSuccess: 'Payment successful',
    paymentFailed: 'Payment failed',
    orderStatus: {
      pendingPayment: 'Pending Payment',
      pendingDelivery: 'Pending Delivery',
      pendingReceipt: 'Pending Receipt',
      completed: 'Completed',
      closed: 'Order Closed'
    }
  },
  payResult: {
    paymentSuccess: 'Payment Successful',
    orderNumber: 'Order Number',
    paymentTime: 'Payment Time',
    paymentMethod: 'Payment Method',
    paymentAmount: 'Payment Amount',
    viewOrder: 'View Order',
    returnToHome: 'Return to Home',
  },
  payment: {
    remainingTime: 'Payment remaining time',
    selectPaymentMethod: 'Please select a payment method',
    noPaymentMethod: 'No payment method available',
    quickPay: 'Quick Pay',
    addBankCard: 'Add bank card',
    ledouAmount: 'Ledou amount',
    selectDeduction: 'Select deduction',
    cashNeeded: 'Cash needed',
    payNow: 'Pay Now',
    prompt: 'Prompt',
    enterTransactionPassword: 'Please enter transaction password',
    confirm: 'Confirm',
    paymentFailed: 'Payment failed',
    paymentSuccess: 'Payment successful',
    pleaseSelectLedouDeduction: 'Please select Ledou deduction',
    pleaseBindBankCard: 'Please bind a bank card',
    pleaseEnterTransactionPassword: 'Please enter transaction password',
    name: 'Name',
    cardNumber: 'Card number',
    reservedPhoneNumber: 'Reserved phone number',
  },
  payPassword: {
    originalPassword: 'Original Transaction Password',
    enterOriginalPassword: 'Please enter original transaction password',
    transactionPassword: 'Transaction Password',
    enterTransactionPassword: 'Please enter transaction password',
    confirmTransactionPassword: 'Confirm Transaction Password',
    enterConfirmTransactionPassword: 'Please enter transaction password again',
    confirm: 'Confirm',
    pleaseEnterOriginalPassword: 'Please enter original transaction password',
    pleaseEnterTransactionPassword: 'Please enter transaction password',
    pleaseConfirmTransactionPassword: 'Please confirm transaction password',
    passwordsDoNotMatch: 'The two passwords do not match',
  },
  confirmOrder: {
    actualPayment: 'Actual Payment',
    originalPrice: 'Original Price',
    total: 'Total',
    submitOrder: 'Submit Order',
    selectDeliveryMethod: 'Please select a delivery method',
    readAndAgree: 'Please check "I have read and agree to the Purchase Agreement"',
    confirmOrderPrompt: 'Confirm Order',
    confirmOrderContent: 'Are you sure you want to place this order?',
    outOfStock: 'Sorry, out of stock',
  },
  goodsDetails: {
    productIntro: 'Product Introduction',
    setPayPasswordFirst: 'Please set a payment password first',
    outOfStockOrRemoved: 'This product has been removed or does not exist, please browse other products',
    immediateRental: 'Immediate Rental',
    addToCart: 'Add to Cart',
    customerService: 'Customer Service',
    favorite: 'Favorite',
    shoppingCart: 'Shopping Cart',
    stock: 'Stock',
    sales: 'Sales',
    views: 'Views',
    selected: 'Selected',
    default: 'Default',
    userReviews: 'User Reviews',
    positiveRate: 'Positive Rate',
    viewAll: 'View All',
    noReviews: 'No Reviews Yet',
    earnFromFriendOrder: 'Earn from friend\'s order',
    maxEarnings: 'Maximum earnings from friend\'s order',
  },
  ai: {
    thinking: 'AI is thinking...',
    askQuestion: 'Feel free to ask any questions',
    error: 'Error occurred',
    send: 'Send',
  },
  index: {
    bindSuperior: 'Bind Superior',
    enterSuperiorInviteCode: 'Please enter superior invite code',
    confirmBinding: 'Confirm Binding',
    latest: 'Latest',
    eliteTeam: 'Elite Team',
    viewMore: 'View More',
    thisWeekStar: 'This Week Star',
    openShowcase: 'Open Showcase',
    showcaseSelection: 'Showcase Selection',
    bindShowcase: 'Bind Showcase',
    upgradePromoter: 'Upgrade Promoter',
    notYetOpen: 'Not yet open',
    pleaseEnterSuperiorID: 'Please enter superior ID',
  },
  newsList: {
    all: 'All',
    noData: 'No data',
    publishTime: 'Publish time:',
    views: 'views',
    systemNotice: 'System Notice',
    helpCenter: 'Help Center',
  },
  newsDetails: {
    publishTime: 'Publish time:',
    views: 'views',
    helpDetail: 'Help Detail',
    noticeDetail: 'Notice Detail',
  },
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    back: 'Back',
    next: 'Next',
    submit: 'Submit',
    loading: 'Loading',
    success: 'Success',
    fail: 'Failed',
    copy: 'Copy',
    copySuccess: 'Copy successful',
    changePassword: 'Change Password',
  },
  appUpdate: {
    newVersion: 'New Version',
    updatePrompt: 'A new version is available. Update now?',
    downloading: 'Downloading update',
    downloadComplete: 'Update Downloaded',
    installPrompt: 'Update downloaded. Install and restart now?',
    installFailed: 'Installation failed, please try again',
    downloadFailed: 'Download failed, please try again',
    updateTip: 'Update Notification',
    restartPrompt: 'New version is ready. Restart now?',
    copied: 'Copied',
    language: 'Language',
    languageName: 'English',
    switchLanguage: 'Switch Language',
    login: 'Login',
    logout: 'Logout',
    register: 'Register',
    settings: 'Settings',
    profile: 'Profile',
    changePassword: 'Change Password',
    pleaseEnter: 'Please enter',
    pleaseSelect: 'Please select',
    testTranslations: 'Test Translations',
  },
  user: {
    userInfo: 'User Info',
    nickname: 'Nickname',
    avatar: 'Avatar',
    userId: 'User ID',
    level: 'Level',
    loginToExperience: 'Login to experience more features',
    clickToLogin: 'Click to login',
    teamInfo: 'Team Information',
    settings: 'Settings',
    myRental: 'My Rental',
    myTeam: 'My Team',
    shareWithFriends: 'Share with Friends',
    balance: 'Balance',
    rentalDeposit: 'Rental Deposit',
    earnings: 'Earnings',
    bindSuperior: 'Bind Superior',
    pleaseEnterSuperiorId: 'Please enter superior ID',
    confirmBinding: 'Confirm Binding',
    bindingSuccess: 'Binding successful',
    changePayPassword: 'Change Payment Password',
    userLevel: {
      v1: 'V1',
      v2: 'V2',
      v3: 'V3',
      v4: 'V4',
      v5: 'V5',
      validUser: 'Valid User',
      registeredUser: 'Normaler Benutzer',
    },
    partnerLevel: {
      none: 'None',
      junior: 'Junior Partner',
      intermediate: 'Intermediate Partner',
      senior: 'Senior Partner',
      founding: 'Founding Partner',
    }
  },
  settings: {
    languageSettings: 'Language Settings',
    currentLanguage: 'Current Language',
    selectLanguage: 'Select Language',
  }
};
