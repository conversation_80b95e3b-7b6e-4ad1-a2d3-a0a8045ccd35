export default {
  pageTitles: {
    home: 'Inicio',
    promoterTasks: 'Tareas del Promotor',
    i18nTest: 'Prueba de Internacionalización',
    ai: 'Asistente IA',
    gift: 'Regalo',
    withdraw: 'Retirar',
    materialLibrary: 'Biblioteca de Materiales',
    agent: 'Agente',
    recharge: '<PERSON><PERSON><PERSON>',
    userCenter: 'Centro de Usuario',
    login: 'Iniciar Sesión',
    allComments: 'Todos los Comentarios',
    activityDetail: 'Detalle de Actividad',
    flashSale: 'Venta Flash',
    memberCenter: 'Centro de Miembros',
    orderDetails: 'Detalles del Pedido',
    shippingAddress: 'Dirección de Envío',
    addAddress: '<PERSON>ñadir Direcci<PERSON>',
    myCoupons: 'Mis Cupones',
    myFavorites: 'Mis Favoritos',
    afterSales: 'Servicio Postventa',
    applyRefund: 'Solicitar Reembolso',
    afterSalesDetail: 'Detalle de Servicio Postventa',
    enterTrackingNumber: 'Introducir Número de Seguimiento',
    userProfile: 'Perfil de Usuario',
    myWallet: '<PERSON> Cartera',
    userRecharge: 'Recarga de Usuario',
    contactCustomerService: 'Servicio al Cliente',
    servicePolicy: 'Política de Servicio',
    merchantSettlement: 'Liquidación de Comerciante',
    applicationRecord: 'Registro de Aplicación',
    productReviews: 'Reseñas de Productos',
    productReviewsList: 'Lista de Reseñas',
    logisticsDetails: 'Detalles de Logística',
    distributionPromotion: 'Promoción de Distribución',
    monthlyBill: 'Factura Mensual',
    monthlyBillDetail: 'Detalle de Factura Mensual',
    distributionOrders: 'Pedidos de Distribución',
    withdrawalRecord: 'Registro de Retiro',
    withdrawalResult: 'Resultado de Retiro',
    invitePoster: 'Póster de Invitación',
    downloadApp: 'Descargar Aplicación',
    accountDetails: 'Detalles de la Cuenta',
    growthValue: 'Valor de Crecimiento',
    rechargeRecord: 'Registro de Recarga',
    myFans: 'Mis Seguidores',
    commissionDetails: 'Detalles de Comisión',
    bargainActivity: 'Actividad de Regateo',
    tutorial: 'Tutorial',
    beginnerTutorial: 'Tutorial para Principiantes',
    dividend: 'Dividendo',
    wechatStore: 'Tienda WeChat',
    managementBonus: 'Bono de Gestión',
    accountBalance: 'Saldo de la Cuenta',
    contributionValue: 'Valor de Contribución',
    pendingSettlement: 'Liquidación Pendiente',
    shoppingVouchers: 'Vales de Compra',
    myPoints: 'Mis Puntos',
    storeLoan: 'Préstamo de Tienda',
    registerAccount: 'Registrar Cuenta',
    forgotPassword: 'Olvidé mi Contraseña',
    setTransactionPassword: 'Establecer Contraseña de Transacción',
    productSearch: 'Búsqueda de Productos',
    myOrders: 'Mis Pedidos',
    dataManagement: 'Gestión de Datos',
    bankCardList: 'Lista de Tarjetas Bancarias',
    addBankCard: 'Añadir Tarjeta Bancaria',
    myProducts: 'Mis Productos',
    myShowcase: 'Mi Escaparate',
    promoterSalesRanking: 'Ranking de Ventas de Promotores',
    eliteTeam: 'Equipo de Élite',
    mallNews: 'Noticias del Centro Comercial',
    newsDetails: 'Detalles de Noticias',
    productDetails: 'Detalles de Reembolso',
    confirmOrder: 'Confirmar Pedido',
    orderPayment: 'Pago del Pedido',
    payResult: 'Resultado del Pago',
  },
  startupGuide: {
    skip: 'Omitir',
    tapToUnmute: 'Toca para activar el sonido',
  },
  team: {
    teamPerformance: 'Rendimiento del Equipo',
    directReferrals: 'Referencias Directas',
    team: 'Equipo',
    myTeam: 'Mi Equipo',
    validUser: 'Usuario Válido',
    noData: 'Sin datos',
    userLevels: {
      registeredUser: 'Usuario Registrado',
      promoter: 'Promotor',
      teamLeader: 'Líder de Equipo',
      junior: 'Junior',
      intermediate: 'Intermedio',
      senior: 'Senior',
      partner: 'Socio'
    }
  },
  user: {
    userInfo: 'Información de Usuario',
    nickname: 'Apodo',
    avatar: 'Avatar',
    userId: 'ID de Usuario',
    level: 'Nivel',
    loginToExperience: 'Inicia sesión para experimentar más funciones',
    clickToLogin: 'Haz clic para iniciar sesión',
    teamInfo: 'Información del Equipo',
    settings: 'Configuración',
    myRental: 'Mi Alquiler',
    myTeam: 'Mi Equipo',
    shareWithFriends: 'Compartir con Amigos',
    balance: 'Saldo',
    rentalDeposit: 'Depósito de Alquiler',
    earnings: 'Ganancias',
    bindSuperior: 'Vincular Superior',
    pleaseEnterSuperiorId: 'Por favor, introduce el ID del superior',
    confirmBinding: 'Confirmar Vinculación',
    bindingSuccess: 'Vinculación Exitosa',
    userLevel: {
      v1: 'V1',
      v2: 'V2',
      v3: 'V3',
      v4: 'V4',
      v5: 'V5',
      validUser: 'Miembro Válido',
      registeredUser: 'Usuario Normal',
    },
    partnerLevel: {
      none: 'Ninguno',
      junior: 'Socio Junior',
      intermediate: 'Socio Intermedio',
      senior: 'Socio Senior',
      founding: 'Socio Fundador',
    }
  },
  settings: {
    languageSettings: 'Configuración de Idioma',
    currentLanguage: 'Idioma Actual',
    selectLanguage: 'Seleccionar Idioma',
  },
  team: {
    teamPerformance: 'Rendimiento del Equipo',
    directReferrals: 'Referencias Directas',
    team: 'Equipo',
    myTeam: 'Mi Equipo',
    validUser: 'Usuario Válido',
    noData: 'Sin datos',
    teamCount: 'Número de Equipo',
    directCount: 'Número de Referencias Directas',
    effectiveCount: 'Número de Efectivos',
    teamIdTotal: 'Total de IDs de Equipo',
    teamEffectiveId: 'IDs Efectivos de Equipo',
    inviteIdTotal: 'Total de IDs de Invitación',
    inviteEffectiveId: 'IDs Efectivos de Invitación',
    userLevels: {
      registeredUser: 'Usuario Registrado',
      promoter: 'Promotor',
      teamLeader: 'Líder de Equipo',
      junior: 'Junior',
      intermediate: 'Intermedio',
      senior: 'Senior',
      partner: 'Socio'
    }
  },
  login: {
    phoneNumberLogin: 'Iniciar Sesión con Teléfono',
    wechatAuthorization: 'Autorización de WeChat',
    moreOperationsAfterLogin: 'Inicia sesión para realizar más operaciones',
    phoneNumber: 'Número de Teléfono',
    enterAccountOrPhoneNumber: 'Por favor, introduce cuenta/número de teléfono',
    password: 'Contraseña',
    enterPassword: 'Por favor, introduce contraseña',
    forgotPassword: 'Olvidé mi Contraseña',
    verificationCode: 'Código de Verificación',
    enterVerificationCode: 'Por favor, introduce código de verificación',
    smsLogin: 'Iniciar Sesión con SMS',
    accountLogin: 'Iniciar Sesión con Cuenta',
    registerAccount: 'Registrar Cuenta',
    agreementPrefix: 'acepto',
    serviceAgreement: 'Acuerdo de Servicio',
    and: 'y',
    privacyAgreement: 'Acuerdo de Privacidad',
    login: 'Iniciar Sesión',
    rememberMe: 'Recordar mis datos',
    pleaseAgreeToTerms: 'Por favor, marca "He leído y acepto el Acuerdo de Servicio y el Acuerdo de Privacidad"',
    pleaseEnterAccount: 'Por favor, introduce cuenta/número de teléfono',
    pleaseEnterPassword: 'Por favor, introduce contraseña',
    pleaseEnterPhoneNumber: 'Por favor, introduce número de teléfono',
    pleaseEnterVerificationCode: 'Por favor, introduce código de verificación',
    loggingIn: 'Iniciando sesión...',
    confirmDelete: 'Confirmar Eliminación',
    deleteAccountConfirm: '¿Estás seguro de que quieres eliminar la cuenta',
  },
  register: {
    phoneNumber: 'Número de Teléfono',
    enterPhoneNumber: 'Combinación alfanumérica de 6 dígitos',
    enterVerificationCode: 'Por favor, introduce código de verificación',
    setPassword: 'Establecer Contraseña',
    passwordRequirements: '6-20 caracteres con números + letras o símbolos',
    confirmPassword: 'Confirmar Contraseña',
    agreementPrefix: 'acepto',
    serviceAgreement: 'Acuerdo de Servicio',
    and: 'y',
    privacyAgreement: 'Acuerdo de Privacidad',
    registerNow: 'Registrarse Ahora',
    pleaseAgreeToTerms: 'Por favor, marca "He leído y acepto el Acuerdo de Servicio y el Acuerdo de Privacidad"',
    pleaseEnterPhoneNumber: 'Por favor, introduce número de teléfono',
    pleaseSetPassword: 'Por favor, establece una contraseña',
    passwordsDoNotMatch: 'Las contraseñas no coinciden',
    pleaseEnterPhoneInfo: 'Por favor, introduce información de teléfono',
  },
  userOrder: {
    all: 'Todos',
    pendingPayment: 'Pendiente de Pago',
    pendingDelivery: 'Pendiente de Envío',
    pendingReceipt: 'Pendiente de Recepción',
    completed: 'Completado',
    closed: 'Cerrado',
    noOrders: 'No hay pedidos todavía~',
    refundRent: 'Reembolso de Alquiler',
    actualPayment: 'Pago Real',
    originalPrice: 'Precio Original',
    status: 'Estado',
    paymentSuccess: 'Pago exitoso',
    paymentFailed: 'Pago fallido',
    orderStatus: {
      pendingPayment: 'Pendiente de Pago',
      pendingDelivery: 'Pendiente de Envío',
      pendingReceipt: 'Pendiente de Recepción',
      completed: 'Completado',
      closed: 'Pedido Cerrado'
    }
  },
  newsList: {
    all: 'Todos',
    noData: 'Sin datos',
    publishTime: 'Tiempo de publicación:',
    views: 'vistas',
    systemNotice: 'Aviso del Sistema',
    helpCenter: 'Centro de Ayuda',
  },
  newsDetails: {
    publishTime: 'Tiempo de publicación:',
    views: 'vistas',
    helpDetail: 'Detalle de Ayuda',
    noticeDetail: 'Detalle de Aviso',
  },
  common: {
    confirm: 'Confirmar',
    cancel: 'Cancelar',
    save: 'Guardar',
    delete: 'Eliminar',
    edit: 'Editar',
    back: 'Atrás',
    next: 'Siguiente',
    submit: 'Enviar',
    loading: 'Cargando',
    success: 'Éxito',
    fail: 'Fallo',
    copy: 'Copiar',
    copied: 'Copiado',
    language: 'Idioma',
    languageName: 'Español',
    switchLanguage: 'Cambiar Idioma',
    login: 'Iniciar Sesión',
    logout: 'Cerrar Sesión',
    register: 'Registrarse',
    settings: 'Configuración',
    profile: 'Perfil',
    changePassword: 'Cambiar Contraseña',
    pleaseEnter: 'Por favor, introduce',
    pleaseSelect: 'Por favor, selecciona',
    testTranslations: 'Prueba de Traducciones',
  }
};
