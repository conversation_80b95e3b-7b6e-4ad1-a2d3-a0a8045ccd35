import Vue from 'vue';
import VueI18n from 'vue-i18n';
import enLocale from './en';
import zhCNLocale from './zh-CN';
import jaLocale from './ja';
import koLocale from './ko';
import ruLocale from './ru';
import esLocale from './es';
import deLocale from './de';
import frLocale from './fr';
import Cache from '@/utils/cache';

Vue.use(VueI18n);

const messages = {
  en: {
    ...enLocale
  },
  'zh-CN': {
    ...zhCNLocale
  },
  ja: {
    ...jaLocale
  },
  ko: {
    ...koLocale
  },
  ru: {
    ...ruLocale
  },
  es: {
    ...esLocale
  },
  de: {
    ...deLocale
  },
  fr: {
    ...frLocale
  }
};

// Get the browser language or use the cached language preference
const getBrowserLanguage = () => {
  // First check if there's a saved language preference
  const cachedLanguage = Cache.get('LANGUAGE');
  if (cachedLanguage) {
    return cachedLanguage;
  }

  // If no saved preference, try to detect browser language
  // const language = (navigator.language || navigator.browserLanguage).toLowerCase();
  // const locales = Object.keys(messages);

  // for (const locale of locales) {
  //   if (language.indexOf(locale.toLowerCase()) > -1) {
  //     return locale;
  //   }
  // }
  // Default to English if no match found
  return 'en';
};

const i18n = new VueI18n({
  locale: getBrowserLanguage(),
  fallbackLocale: 'en',
  messages,
  silentTranslationWarn: true
});

export default i18n;
