export default {
  pageTitles: {
    home: 'Главная',
    promoterTasks: 'Задачи промоутера',
    i18nTest: 'Тест интернационализации',
    ai: 'ИИ-ассистент',
    gift: 'Подарок',
    withdraw: 'Вывод средств',
    materialLibrary: 'Библиотека материалов',
    agent: '<PERSON>г<PERSON>нт',
    recharge: 'Пополнение',
    userCenter: 'Центр пользователя',
    login: 'Вход',
    allComments: 'Все комментарии',
    activityDetail: 'Детали активности',
    flashSale: 'Распродажа',
    memberCenter: 'Центр участника',
    orderDetails: 'Детали заказа',
    shippingAddress: 'Адрес доставки',
    addAddress: 'Добавить адрес',
    myCoupons: 'Мои купоны',
    myFavorites: 'Избранное',
    afterSales: 'Послепродажное обслуживание',
    applyRefund: 'Запросить возврат',
    afterSalesDetail: 'Детали послепродажного обслуживания',
    enterTrackingNumber: 'Введите номер отслеживания',
    userProfile: 'Профиль пользователя',
    myWallet: 'Мой кошелек',
    userRecharge: 'Пополнение пользователя',
    contactCustomerService: 'Служба поддержки',
    servicePolicy: 'Политика обслуживания',
    merchantSettlement: 'Расчет с продавцом',
    applicationRecord: 'Запись заявки',
    productReviews: 'Отзывы о продукте',
    productReviewsList: 'Список отзывов',
    logisticsDetails: 'Детали логистики',
    distributionPromotion: 'Распространение промоакций',
    monthlyBill: 'Ежемесячный счет',
    monthlyBillDetail: 'Детали ежемесячного счета',
    distributionOrders: 'Заказы распространения',
    withdrawalRecord: 'Запись вывода средств',
    withdrawalResult: 'Результат вывода средств',
    invitePoster: 'Пригласительный постер',
    downloadApp: 'Скачать приложение',
    accountDetails: 'Детали аккаунта',
    growthValue: 'Значение роста',
    rechargeRecord: 'Запись пополнения',
    myFans: 'Мои подписчики',
    commissionDetails: 'Детали комиссии',
    bargainActivity: 'Торговая активность',
    tutorial: 'Руководство',
    beginnerTutorial: 'Руководство для начинающих',
    dividend: 'Дивиденд',
    wechatStore: 'Магазин WeChat',
    managementBonus: 'Бонус управления',
    accountBalance: 'Баланс аккаунта',
    contributionValue: 'Значение вклада',
    pendingSettlement: 'Ожидающий расчет',
    shoppingVouchers: 'Покупочные ваучеры',
    myPoints: 'Мои баллы',
    storeLoan: 'Кредит магазина',
    registerAccount: 'Регистрация аккаунта',
    forgotPassword: 'Забыли пароль',
    setTransactionPassword: 'Установить пароль транзакции',
    productSearch: 'Поиск продукта',
    myOrders: 'Мои заказы',
    dataManagement: 'Управление данными',
    bankCardList: 'Список банковских карт',
    addBankCard: 'Добавить банковскую карту',
    myProducts: 'Мои продукты',
    myShowcase: 'Моя витрина',
    promoterSalesRanking: 'Рейтинг продаж промоутеров',
    eliteTeam: 'Элитная команда',
    mallNews: 'Системное уведомление',
    newsDetails: 'Детали новостей',
    productDetails: 'Детали возврата',
    confirmOrder: 'Подтверждение заказа',
    orderPayment: 'Оплата заказа',
    payResult: 'Результат оплаты',
  },
  startupGuide: {
    skip: 'Пропустить',
    tapToUnmute: 'Нажмите, чтобы включить звук',
  },
  userIntegral: {
    totalAssets: 'Общие активы (единицы)',
    recharge: 'Пополнение',
    gift: 'Подарок',
    accountInfo: 'Информация об аккаунте',
    accountDetails: 'Детали аккаунта',
    popularActivities: 'Популярные активности',
    getCoupons: 'Получить купоны',
    dailyCoupons: 'Ежедневные купоны доступны',
    valuableProducts: 'Распродажа ценных товаров',
    latestProducts: 'Последние товары в продаже',
    joinNow: 'Присоединиться сейчас',
    totalRecharge: 'Общее пополнение (юань)',
    totalConsumption: 'Общее потребление (единицы)',
    todayQuestions: 'Вопросов задано сегодня',
  },
  giveaway: {
    receiverId: 'ID получателя',
    enterReceiverId: 'Пожалуйста, введите ID получателя',
    giftAmount: 'Сумма подарка',
    enterGiftAmount: 'Пожалуйста, введите сумму подарка',
    availableBalance: 'Доступный баланс',
    giftAll: 'Подарить все',
    gift: 'Подарок',
    pleaseEnterGiftAddress: 'Пожалуйста, введите адрес подарка',
    pleaseEnterGiftAmount: 'Пожалуйста, введите сумму подарка',
    giftSuccess: 'Подарок успешно отправлен',
  },
  inviteFans: {
    myInviteCode: 'Мой код приглашения',
    inviteLink: 'Ссылка приглашения',
    clickToCopy: 'Нажмите, чтобы скопировать',
    saveToAlbum: 'Сохранить в альбом',
    longPressSaveToAlbum: 'Долгое нажатие для сохранения в альбом',
    saveSuccess: 'Сохранение успешно',
    saveFailed: 'Сохранение не удалось',
    pleaseLongPressToSave: 'Пожалуйста, долго нажмите на изображение для сохранения',
  },
  team: {
    teamPerformance: 'Производительность команды',
    directReferrals: 'Прямые рефералы',
    team: 'Команда',
    myTeam: 'Моя команда',
    validUser: 'Действительный пользователь',
    noData: 'Нет данных',
    teamCount: 'Количество команды',
    directCount: 'Количество прямых рефералов',
    effectiveCount: 'Количество действительных',
    teamIdTotal: 'Общее количество ID команды',
    teamEffectiveId: 'Действительные ID команды',
    inviteIdTotal: 'Общее количество ID приглашений',
    inviteEffectiveId: 'Действительные ID приглашений',
    userLevels: {
      registeredUser: 'Зарегистрированный пользователь',
      promoter: 'Промоутер',
      teamLeader: 'Лидер команды',
      junior: 'Младший',
      intermediate: 'Средний',
      senior: 'Старший',
      partner: 'Партнер'
    }
  },
  user: {
    userInfo: 'Информация о пользователе',
    nickname: 'Никнейм',
    avatar: 'Аватар',
    userId: 'ID пользователя',
    level: 'Уровень',
    loginToExperience: 'Войдите, чтобы получить больше функций',
    clickToLogin: 'Нажмите для входа',
    teamInfo: 'Информация о команде',
    settings: 'Настройки',
    myRental: 'Моя аренда',
    myTeam: 'Моя команда',
    shareWithFriends: 'Поделиться с друзьями',
    balance: 'Баланс',
    rentalDeposit: 'Депозит аренды',
    earnings: 'Заработок',
    bindSuperior: 'Привязать руководителя',
    pleaseEnterSuperiorId: 'Пожалуйста, введите ID руководителя',
    confirmBinding: 'Подтвердить привязку',
    bindingSuccess: 'Привязка успешна',
    userLevel: {
      v1: 'V1',
      v2: 'V2',
      v3: 'V3',
      v4: 'V4',
      v5: 'V5',
      validUser: 'Действительный участник',
      registeredUser: 'Обычный пользователь',
    },
    partnerLevel: {
      none: 'Нет',
      junior: 'Младший партнер',
      intermediate: 'Средний партнер',
      senior: 'Старший партнер',
      founding: 'Основатель-партнер',
    }
  },
  settings: {
    languageSettings: 'Настройки языка',
    currentLanguage: 'Текущий язык',
    selectLanguage: 'Выбрать язык',
  },
  userProfile: {
    tapToChangeAvatar: 'Нажмите, чтобы изменить аватар',
    updateWechatAvatar: 'Обновить аватар WeChat',
    nickname: 'Никнейм',
    gender: 'Пол',
    phoneNumber: 'Номер телефона',
    notBound: 'Не привязан',
    changePhoneNumber: 'Изменить номер телефона',
    bindPhoneNumber: 'Привязать номер телефона',
    loginPassword: 'Пароль для входа',
    tapToSet: 'Нажмите, чтобы установить',
    videoChannelId: 'ID видеоканала',
    pleaseBindVideoChannel: 'Пожалуйста, привяжите видеоканал',
    serviceAgreement: 'Соглашение об обслуживании',
    privacyPolicy: 'Политика конфиденциальности',
    qualificationInfo: 'Информация о квалификации',
    languageSettings: 'Настройки языка',
    aboutUs: 'О нас',
    logout: 'Выход',
    modifyUsername: 'Изменить имя пользователя',
    pleaseEnterNickname: 'Пожалуйста, введите никнейм',
    confirm: 'Подтвердить',
    modifyVideoChannelId: 'Изменить ID видеоканала',
    pleaseEnterVideoChannelId: 'Пожалуйста, введите ID видеоканала',
    camera: 'Камера',
    album: 'Альбом',
    cancel: 'Отмена',
    prompt: 'Подсказка',
    cameraPermissionDesc: 'Разрешение камеры используется для фотографирования, записи видео и т.д.',
    albumPermissionDesc: 'Разрешение хранилища используется для доступа к вашей галерее и хранения изображений',
    iKnow: 'Я знаю',
    male: 'Мужской',
    female: 'Женский',
  },
  forgetPwd: {
    phoneNumber: 'Номер телефона',
    enterPhoneNumber: 'Пожалуйста, введите номер телефона',
    smsVerificationCode: 'SMS-код подтверждения',
    enterVerificationCode: 'Пожалуйста, введите код подтверждения',
    resetPassword: 'Сбросить пароль',
    passwordRequirements: '6-20 символов с цифрами + буквами или символами',
    confirmPassword: 'Подтвердить пароль',
    enterPasswordAgain: 'Пожалуйста, введите пароль еще раз',
    confirm: 'Подтвердить',
    pleaseEnterPhoneNumber: 'Пожалуйста, введите номер телефона',
    pleaseEnterVerificationCode: 'Пожалуйста, введите код подтверждения',
    pleaseEnterResetPassword: 'Пожалуйста, введите новый пароль',
    pleaseEnterConfirmPassword: 'Пожалуйста, введите подтверждение пароля',
    passwordsDoNotMatch: 'Два пароля не совпадают',
    pleaseEnterPhoneInfo: 'Пожалуйста, введите информацию о номере телефона',
  },
  login: {
    phoneNumberLogin: 'Вход по номеру телефона',
    wechatAuthorization: 'Авторизация WeChat',
    moreOperationsAfterLogin: 'Войдите, чтобы выполнить больше операций',
    phoneNumber: 'Номер телефона',
    enterAccountOrPhoneNumber: 'Пожалуйста, введите аккаунт/номер телефона',
    password: 'Пароль',
    enterPassword: 'Пожалуйста, введите пароль',
    forgotPassword: 'Забыли пароль',
    verificationCode: 'Код подтверждения',
    enterVerificationCode: 'Пожалуйста, введите код подтверждения',
    smsLogin: 'Вход по SMS',
    accountLogin: 'Вход по аккаунту',
    registerAccount: 'Зарегистрировать аккаунт',
    agreementPrefix: 'согласен',
    serviceAgreement: 'Соглашение об обслуживании',
    and: 'и',
    privacyAgreement: 'Соглашение о конфиденциальности',
    login: 'Вход',
    rememberMe: 'Запомнить меня',
    pleaseAgreeToTerms: 'Пожалуйста, отметьте "Я прочитал и согласен с Соглашением об обслуживании и Соглашением о конфиденциальности"',
    pleaseEnterAccount: 'Пожалуйста, введите аккаунт/номер телефона',
    pleaseEnterPassword: 'Пожалуйста, введите пароль',
    pleaseEnterPhoneNumber: 'Пожалуйста, введите номер телефона',
    pleaseEnterVerificationCode: 'Пожалуйста, введите код подтверждения',
    loggingIn: 'Вход...',
    confirmDelete: 'Подтвердить удаление',
    deleteAccountConfirm: 'Вы уверены, что хотите удалить аккаунт',
  },
  register: {
    phoneNumber: 'Номер телефона',
    enterPhoneNumber: '6-значная буквенно-цифровая комбинация',
    enterVerificationCode: 'Пожалуйста, введите код подтверждения',
    setPassword: 'Установить пароль',
    passwordRequirements: '6-20 символов с цифрами + буквами или символами',
    confirmPassword: 'Подтвердить пароль',
    agreementPrefix: 'согласен',
    serviceAgreement: 'Соглашение об обслуживании',
    and: 'и',
    privacyAgreement: 'Соглашение о конфиденциальности',
    registerNow: 'Зарегистрироваться сейчас',
    pleaseAgreeToTerms: 'Пожалуйста, отметьте "Я прочитал и согласен с Соглашением об обслуживании и Соглашением о конфиденциальности"',
    pleaseEnterPhoneNumber: 'Пожалуйста, введите номер телефона',
    pleaseSetPassword: 'Пожалуйста, установите пароль',
    passwordsDoNotMatch: 'Два пароля не совпадают',
    pleaseEnterPhoneInfo: 'Пожалуйста, введите информацию о номере телефона',
  },
  mescroll: {
    textInOffset: 'Потяните вниз для обновления',
    textOutOffset: 'Отпустите для обновления',
    textLoading: 'Загрузка...',
    textSuccess: 'Загрузка успешна',
    textError: 'Ошибка загрузки',
    textNoMore: '',
    empty: 'Пусто',
  },
  goodsList: {
    originalPrice: 'Исходная цена',
    peopleBought: 'человек купили',
    buyNow: 'Купить сейчас',
    buyImmediately: 'Купить немедленно',
    dailyIncome: 'Ежедневный доход',
  },
  topup: {
    rechargeChain: 'Цепочка пополнения',
    tronChain: 'Цепочка Tron',
    binanceChain: 'Цепочка Binance',
    pushaddress: 'Адрес пополнения',
    network: 'Сеть',
    address: 'Адрес',
    copy: 'Копировать',
    rechargeInstructions: 'Инструкции по пополнению',
    minimumRecharge: 'Минимальное пополнение',
    arrivalTime: 'Время прибытия',
    aboutOneMinute: 'Около 10 минуты',
    uploadVoucher: 'Загрузить ваучер',
    uploadImage: 'Загрузить изображение',
    submit: 'Отправить',
    copySuccess: 'Копирование успешно',
    pleaseUploadVoucher: 'Пожалуйста, загрузите ваучер',
    uploadSuccess: 'Загрузка успешна',
  },
  withdrawal: {
    withdrawalAddress: 'Адрес вывода',
    enterWithdrawalAddress: 'Пожалуйста, введите адрес вывода',
    withdrawalAmount: 'Сумма вывода',
    enterWithdrawalAmount: 'Пожалуйста, введите сумму вывода',
    availableBalance: 'Доступный баланс',
    withdrawAll: 'Вывести все',
    withdraw: 'Вывести',
    pleaseEnterWithdrawalAddress: 'Пожалуйста, введите адрес вывода',
    pleaseEnterWithdrawalAmount: 'Пожалуйста, введите сумму вывода',
    withdrawalSuccess: 'Вывод успешен',
  },
  userBill: {
    all: 'Все',
    income: 'Доход',
    expenditure: 'Расход',
    noData: 'Нет данных',
  },
  userLove: {
    myBalance: 'Мой баланс',
    totalAssets: 'Общие активы (единицы)',
    recharge: 'Пополнение',
    withdraw: 'Вывод',
    accountInfo: 'Информация об аккаунте',
    accountDetails: 'Детали аккаунта',
    popularActivities: 'Популярные активности',
    getCoupons: 'Получить купоны',
    dailyCoupons: 'Ежедневные купоны доступны',
    valuableProducts: 'Распродажа ценных товаров',
    latestProducts: 'Последние товары в продаже',
    joinNow: 'Присоединиться сейчас',
    totalRecharge: 'Общее пополнение (юань)',
    totalConsumption: 'Общее потребление (единицы)',
  },
  userOrder: {
    all: 'Все',
    pendingPayment: 'Ожидает оплаты',
    pendingDelivery: 'Ожидает доставки',
    pendingReceipt: 'Ожидает получения',
    completed: 'Завершено',
    closed: 'Закрыто',
    noOrders: 'У вас еще нет заказов~',
    refundRent: 'Возврат аренды',
    actualPayment: 'Фактический платеж',
    originalPrice: 'Исходная цена',
    status: 'Статус',
    paymentSuccess: 'Оплата успешна',
    paymentFailed: 'Оплата не удалась',
    orderStatus: {
      pendingPayment: 'Ожидает оплаты',
      pendingDelivery: 'Ожидает доставки',
      pendingReceipt: 'Ожидает получения',
      completed: 'Завершено',
      closed: 'Заказ закрыт'
    }
  },
  payResult: {
    paymentSuccess: 'Оплата успешна',
    orderNumber: 'Номер заказа',
    paymentTime: 'Время оплаты',
    paymentMethod: 'Способ оплаты',
    paymentAmount: 'Сумма оплаты',
    viewOrder: 'Просмотреть заказ',
    returnToHome: 'Вернуться на главную',
  },
  payment: {
    remainingTime: 'Оставшееся время оплаты',
    selectPaymentMethod: 'Пожалуйста, выберите способ оплаты',
    noPaymentMethod: 'Нет доступных способов оплаты',
    quickPay: 'Быстрая оплата',
    addBankCard: 'Добавить банковскую карту',
    ledouAmount: 'Сумма Ledou',
    selectDeduction: 'Выбрать вычет',
    cashNeeded: 'Необходимая наличность',
    payNow: 'Оплатить сейчас',
    prompt: 'Подсказка',
    enterTransactionPassword: 'Пожалуйста, введите пароль транзакции',
    confirm: 'Подтвердить',
    paymentFailed: 'Оплата не удалась',
    paymentSuccess: 'Оплата успешна',
    pleaseSelectLedouDeduction: 'Пожалуйста, выберите вычет Ledou',
    pleaseBindBankCard: 'Пожалуйста, привяжите банковскую карту',
    pleaseEnterTransactionPassword: 'Пожалуйста, введите пароль транзакции',
    name: 'Имя',
    cardNumber: 'Номер карты',
    reservedPhoneNumber: 'Зарезервированный номер телефона',
  },
  confirmOrder: {
    actualPayment: 'Фактический платеж',
    originalPrice: 'Исходная цена',
    total: 'Всего',
    submitOrder: 'Отправить заказ',
    selectDeliveryMethod: 'Пожалуйста, выберите способ доставки',
    readAndAgree: 'Пожалуйста, отметьте "Я прочитал и согласен с договором покупки"',
    confirmOrderPrompt: 'Подтверждение заказа',
    confirmOrderContent: 'Вы уверены, что хотите разместить этот заказ?',
    outOfStock: 'Извините, нет в наличии',
  },
  goodsDetails: {
    productIntro: 'Введение в продукт',
    setPayPasswordFirst: 'Пожалуйста, сначала установите пароль для оплаты',
    outOfStockOrRemoved: 'Этот товар был удален или не существует. Пожалуйста, посмотрите другие товары',
    immediateRental: 'Немедленная аренда',
    addToCart: 'Добавить в корзину',
    customerService: 'Обслуживание клиентов',
    favorite: 'Избранное',
    shoppingCart: 'Корзина',
    stock: 'Запас',
    sales: 'Продажи',
    views: 'Просмотры',
    selected: 'Выбрано',
    default: 'По умолчанию',
    userReviews: 'Отзывы пользователей',
    positiveRate: 'Положительный рейтинг',
    viewAll: 'Просмотреть все',
    noReviews: 'Пока нет отзывов',
    earnFromFriendOrder: 'Заработок от заказа друга',
    maxEarnings: 'Максимальный заработок от заказа друга',
  },
  ai: {
    thinking: 'ИИ думает...',
    askQuestion: 'Свободно задавайте вопросы',
    error: 'Произошла ошибка',
    send: 'Отправить',
  },
  index: {
    bindSuperior: 'Привязать руководителя',
    enterSuperiorInviteCode: 'Пожалуйста, введите код приглашения руководителя',
    confirmBinding: 'Подтвердить привязку',
    latest: 'Последние',
    eliteTeam: 'Элитная команда',
    viewMore: 'Посмотреть больше',
    thisWeekStar: 'Звезда этой недели',
    openShowcase: 'Открыть витрину',
    showcaseSelection: 'Выбор витрины',
    bindShowcase: 'Привязать витрину',
    upgradePromoter: 'Повысить до промоутера',
    notYetOpen: 'Еще не открыто',
    pleaseEnterSuperiorID: 'Пожалуйста, введите ID руководителя',
  },
  newsList: {
    all: 'Все',
    noData: 'Нет данных',
    publishTime: 'Время публикации:',
    views: 'просмотров',
    systemNotice: 'Системное уведомление',
    helpCenter: 'Центр помощи',
  },
  newsDetails: {
    publishTime: 'Время публикации:',
    views: 'просмотров',
    helpDetail: 'Детали помощи',
    noticeDetail: 'Детали уведомления',
  },
  common: {
    confirm: 'Подтвердить',
    cancel: 'Отмена',
    save: 'Сохранить',
    delete: 'Удалить',
    edit: 'Редактировать',
    back: 'Назад',
    next: 'Далее',
    submit: 'Отправить',
    loading: 'Загрузка',
    success: 'Успех',
    fail: 'Неудача',
    copy: 'Копировать',
    copySuccess: 'Копирование успешно',
    copied: 'Скопировано',
    language: 'Язык',
    languageName: 'Русский',
    switchLanguage: 'Переключить язык',
    login: 'Вход',
    logout: 'Выход',
    register: 'Регистрация',
    settings: 'Настройки',
    profile: 'Профиль',
    changePassword: 'Изменить пароль',
    pleaseEnter: 'Пожалуйста, введите',
    pleaseSelect: 'Пожалуйста, выберите',
    testTranslations: 'Тест переводов',
  }
};
