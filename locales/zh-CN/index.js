export default {
  pageTitles: {
    home: '首页',
    promoterTasks: '推客任务',
    i18nTest: '国际化测试',
    ai: 'AI助手',
    gift: '赠送',
    withdraw: '提现',
    subsidy: '申请补贴',
    bindWallet: '绑定钱包地址',
    materialLibrary: '素材库',
    agent: '代理',
    recharge: '充值',
    userCenter: '个人中心',
    login: '登录',
    allComments: '全部评论',
    activityDetail: '活动详情',
    flashSale: '限时秒杀',
    memberCenter: '会员中心',
    orderDetails: '订单详情',
    shippingAddress: '收货地址',
    addAddress: '添加地址',
    myCoupons: '我的优惠券',
    myFavorites: '我的收藏',
    afterSales: '售后服务',
    applyRefund: '申请退款',
    afterSalesDetail: '售后详情',
    enterTrackingNumber: '填写物流单号',
    userProfile: '个人资料',
    myWallet: '我的钱包',
    userRecharge: '用户充值',
    contactCustomerService: '联系客服',
    servicePolicy: '服务政策',
    merchantSettlement: '商家入驻',
    applicationRecord: '申请记录',
    productReviews: '商品评价',
    productReviewsList: '评价列表',
    logisticsDetails: '物流详情',
    distributionPromotion: '分销推广',
    monthlyBill: '月度账单',
    monthlyBillDetail: '账单详情',
    distributionOrders: '分销订单',
    withdrawalRecord: '提现记录',
    withdrawalResult: '提现结果',
    invitePoster: '邀请海报',
    downloadApp: '下载应用',
    accountDetails: '账户明细',
    growthValue: '成长值',
    rechargeRecord: '充值记录',
    myFans: '我的粉丝',
    commissionDetails: '佣金明细',
    bargainActivity: '砍价活动',

    // Bundle_c pages
    tutorial: '教程',
    beginnerTutorial: '新手教程',
    dividend: '分红',
    wechatStore: '微信小店',
    managementBonus: '我的管理奖',
    accountBalance: '我的账户余额',
    contributionValue: '我的贡献值',
    pendingSettlement: '我的待结算',
    shoppingVouchers: '我的购物券',
    myPoints: '我的收益',
    storeLoan: '门店贷款',
    registerAccount: '注册账号',
    forgotPassword: '忘记密码',
    setTransactionPassword: '设置交易密码',
    productSearch: '商品搜索',
    myOrders: '我的订单',
    dataManagement: '资料管理',
    bankCardList: '银行卡列表',
    addBankCard: '添加银行卡',
    myProducts: '我的商品',
    myShowcase: '我的橱窗',
    promoterSalesRanking: '推客月销售排行榜',
    eliteTeam: '精英团队',
    mallNews: '系统公告',
    newsDetails: '资讯详情',
    productDetails: '租赁详情',
    confirmOrder: '确认订单',
    orderPayment: '订单支付',
    payResult: '支付结果',
  },
  startupGuide: {
    skip: '跳过',
    tapToUnmute: '点击取消静音',
  },
  userIntegral: {
    totalAssets: '总资产(个)',
    recharge: '充值',
    gift: '赠送',
    accountInfo: '账户信息',
    accountDetails: '账户明细',
    popularActivities: '热门活动',
    getCoupons: '领取优惠券',
    dailyCoupons: '每日优惠券抢不停',
    valuableProducts: '超值商品 限时秒杀',
    latestProducts: '最新商品秒杀中',
    joinNow: '立即参与',
    totalRecharge: '累计充值(元)',
    totalConsumption: '累计消费(个)',
    todayQuestions: '今日已提问',
  },
  giveaway: {
    receiverId: '接收人ID',
    enterReceiverId: '请输入接收人ID',
    giftAmount: '赠送数量',
    enterGiftAmount: '请输入赠送数量',
    availableBalance: '可用余额',
    giftAll: '全部赠送',
    gift: '赠送',
    pleaseEnterGiftAddress: '请输入赠送地址',
    pleaseEnterGiftAmount: '请输入赠送数量',
    giftSuccess: '赠送成功',
  },
  inviteFans: {
    myInviteCode: '我的邀请码',
    inviteLink: '邀请链接',
    clickToCopy: '点击复制',
    saveToAlbum: '保存到相册',
    longPressSaveToAlbum: '长按保存到相册',
    saveSuccess: '保存成功',
    saveFailed: '保存失败',
    pleaseLongPressToSave: '请长按图片保存',
  },
  team: {
    teamPerformance: '团队业绩',
    directReferrals: '直推',
    team: '团队',
    myTeam: '我的团队',
    validUser: '有效用户',
    noData: '暂无数据',
    teamCount: '团队人数',
    directCount: '直推人数',
    effectiveCount: '有效人数',
    teamIdTotal: '团队ID总数',
    teamEffectiveId: '团队有效ID',
    inviteIdTotal: '邀请ID总数',
    inviteEffectiveId: '邀请有效ID',
    userLevels: {
      registeredUser: '普通用户',
      promoter: '推客',
      teamLeader: '团长',
      junior: '初级',
      intermediate: '中级',
      senior: '高级',
      partner: '联创'
    }
  },
  userProfile: {
    tapToChangeAvatar: '点击修改头像',
    updateWechatAvatar: '更新微信头像',
    nickname: '昵称',
    gender: '性别',
    phoneNumber: '账户名',
    notBound: '未绑定',
    changePhoneNumber: '更换手机号',
    bindPhoneNumber: '绑定手机号',
    loginPassword: '登录密码',
    tapToSet: '点击设置',
    videoChannelId: '视频号ID',
    pleaseBindVideoChannel: '请绑定视频号',
    serviceAgreement: '服务协议',
    privacyPolicy: '隐私政策',
    qualificationInfo: '资质信息',
    languageSettings: '语言设置',
    aboutUs: '关于我们',
    logout: '退出登录',
    modifyUsername: '修改用户名',
    pleaseEnterNickname: '请输入昵称',
    confirm: '确定',
    modifyVideoChannelId: '修改视频号id',
    pleaseEnterVideoChannelId: '请输入视频号id',
    camera: '拍摄',
    album: '从相册选择',
    cancel: '取消',
    prompt: '提示',
    cameraPermissionDesc: '相机权限使用说明，用于拍照、录制视频等场景。',
    albumPermissionDesc: '存储权限使用说明，用于获取你的图库信息以及图像的存储。',
    iKnow: '知道了',
    male: '男',
    female: '女',
  },

  login: {
    phoneNumberLogin: '账户名快捷登录',
    wechatAuthorization: '微信授权登录',
    moreOperationsAfterLogin: '登录后才可进行更多操作哦',
    phoneNumber: '账户名',
    enterAccountOrPhoneNumber: '请输入账户名',
    password: '密码',
    enterPassword: '请输入密码',
    forgotPassword: '忘记密码',
    verificationCode: '验证码',
    enterVerificationCode: '请输入验证码',
    graphicCaptcha: '图形验证码',
    enterGraphicCaptcha: '请输入图形验证码',
    refreshCaptcha: '刷新',
    smsLogin: '短信验证码登录',
    accountLogin: '账号登录',
    registerAccount: '注册账号',
    agreementPrefix: '已阅读并同意',
    serviceAgreement: '《服务协议》',
    and: '和',
    privacyAgreement: '《隐私协议》',
    login: '登录',
    rememberMe: '记住账号密码',
    pleaseAgreeToTerms: '请先勾选"已阅读并同意《服务协议》和《隐私协议》"',
    pleaseEnterAccount: '请输入账户名',
    pleaseEnterPassword: '请输入密码',
    pleaseEnterPhoneNumber: '请输入账户名',
    pleaseEnterVerificationCode: '请输入验证码',
    loggingIn: '登录中...',
    confirmDelete: '确认删除',
    deleteAccountConfirm: '确定要删除账号',
  },
  register: {
    phoneNumber: '账户名',
    enterPhoneNumber: '6位数字母+数字组合',
    enterVerificationCode: '请输入验证码',
    graphicCaptcha: '图形验证码',
    enterGraphicCaptcha: '请输入图形验证码',
    refreshCaptcha: '刷新',
    setPassword: '设置密码',
    passwordRequirements: '6-20位数字+字母或符号组合',
    confirmPassword: '确认密码',
    agreementPrefix: '已阅读并同意',
    serviceAgreement: '《服务协议》',
    and: '和',
    privacyAgreement: '《隐私协议》',
    registerNow: '立即注册',
    pleaseAgreeToTerms: '请先勾选"已阅读并同意《服务协议》和《隐私协议》"',
    pleaseEnterPhoneNumber: '请填写账户名',
    pleaseSetPassword: '请设置密码',
    passwordsDoNotMatch: '两次密码输入不一致',
    pleaseEnterPhoneInfo: '请填写账户信息～',
    haveAccount: '已有帐号，前往下载APP',
    inviteCode: '邀请码',
    enterInviteCode: '请输入邀请码（可选）',
  },
  mescroll: {
    textInOffset: '下拉刷新',
    textOutOffset: '释放更新',
    textLoading: '加载中 ...',
    textSuccess: '加载成功',
    textError: '加载失败',
    textNoMore: '',
    empty: '空空如也',
  },
  goodsList: {
    originalPrice: '原价',
    peopleBought: '人购买',
    buyNow: '去购买',
    buyImmediately: '立即抢购',
    dailyIncome: '日收益',
  },
  topup: {
    rechargeChain: '充值链',
    tronChain: '波场链',
    binanceChain: '币安链',
    pushaddress: '充值地址',
    network: '网络',
    address: '地址',
    copy: '复制',
    rechargeInstructions: '充值说明',
    minimumRecharge: '最小充币额',
    arrivalTime: '充币到账时间',
    aboutOneMinute: '约 10 分钟',
    uploadVoucher: '上传凭证',
    uploadImage: '上传图片',
    submit: '提交',
    copySuccess: '复制成功',
    pleaseUploadVoucher: '请上传凭证',
    uploadSuccess: '上传成功',
    rechargeAmount: '充值数量',
    enterRechargeAmount: '请输入充值数量',
    limitRechargeAmount: '充值数量不能少于30U',
    viewUploadExample: '查看上传凭证示例',
  },
  withdrawal: {
    withdrawalAddress: '提现地址',
    enterWithdrawalAddress: '请输入地址',
    withdrawalAmount: '提现数量',
    enterWithdrawalAmount: '请输入提现数量',
    availableBalance: '可用余额',
    withdrawAll: '全部提现',
    withdraw: '提现',
    pleaseEnterWithdrawalAddress: '请输入提现地址',
    pleaseEnterWithdrawalAmount: '请输入提现数量',
    withdrawalSuccess: '提现成功',
    prompt: '提示',
    pleaseBindWalletFirst: '请先绑定钱包地址',
  },
  subsidy: {
    subsidyAmount: '补贴金额',
    enterSubsidyAmount: '请输入补贴金额',
    applySubsidy: '申请补贴',
    pleaseEnterSubsidyAmount: '请输入补贴金额',
    applySuccess: '申请成功',
    applyFailed: '申请失败',
  },
  bindWallet: {
    walletAddress: '币安链提现地址',
    enterWalletAddress: '请输入钱包地址',
    bindWallet: '绑定钱包',
    pleaseEnterWalletAddress: '请输入钱包地址',
    bindSuccess: '绑定成功',
    bindFailed: '绑定失败',
  },
  agent: {
    title: '代理',
    territory: '区域代理',
    territoryDesc: '加入我们，享受更多特权',
    communitySubsidies: '社区补贴',
    communitySubsidiesDesc: '获取社区补贴',
    notOpenYet: '暂未开放',
  },
  download: {
    title: '下载chat GPT去中心化钱包',
    description: '交易掌上轻松实现',
    downloadButton: '本地下载',
  },
  forgetPwd: {
    phoneNumber: '账户名',
    enterPhoneNumber: '请输入账户名',
    smsVerificationCode: '短信验证码',
    enterVerificationCode: '请输入验证码',
    resetPassword: '重置密码',
    confirmPassword: '确认密码',
    passwordRequirements: '6-20位数字+字母或符号组合',
    enterPasswordAgain: '请再次输入密码',
    confirm: '确认',
    pleaseEnterPhoneNumber: '请输入账户名',
    pleaseEnterVerificationCode: '请输入验证码',
    pleaseEnterResetPassword: '请输入重置密码',
    pleaseEnterConfirmPassword: '请输入确认密码',
    passwordsDoNotMatch: '两次密码输入不一致',
    pleaseEnterPhoneInfo: '请填写账户信息',
    originalPassword: '原密码',
    enterOriginalPassword: '请输入原密码',
  },
  userBill: {
    all: '全部',
    income: '收入',
    expenditure: '支出',
    noData: '暂无数据',
  },
  userLove: {
    myBalance: '我的余额',
    totalAssets: '总资产(个)',
    recharge: '充值',
    withdraw: '提现',
    accountInfo: '账户信息',
    accountDetails: '账户明细',
    popularActivities: '热门活动',
    getCoupons: '领取优惠券',
    dailyCoupons: '每日优惠券抢不停',
    valuableProducts: '超值商品 限时秒杀',
    latestProducts: '最新商品秒杀中',
    joinNow: '立即参与',
    totalRecharge: '累计充值(元)',
    totalConsumption: '累计消费(个)',
  },
  userOrder: {
    all: '全部',
    pendingPayment: '待付款',
    pendingDelivery: '待发货',
    pendingReceipt: '待收货',
    completed: '已完成',
    closed: '已关闭',
    noOrders: '暂无订单~',
    refundRent: '退租金',
    actualPayment: '实付款',
    originalPrice: '原价',
    status: '状态',
    paymentSuccess: '支付成功',
    paymentFailed: '支付失败',
    orderStatus: {
      pendingPayment: '待支付',
      pendingDelivery: '待发货',
      pendingReceipt: '待收货',
      completed: '已完成',
      closed: '订单已关闭'
    }
  },
  payResult: {
    paymentSuccess: '订单支付成功',
    orderNumber: '订单编号',
    paymentTime: '付款时间',
    paymentMethod: '支付方式',
    paymentAmount: '支付金额',
    viewOrder: '查看订单',
    returnToHome: '返回首页',
  },
  payment: {
    remainingTime: '支付剩余时间',
    selectPaymentMethod: '请选择支付方式',
    noPaymentMethod: '暂无支付方式',
    quickPay: '快钱支付',
    addBankCard: '添加银行卡',
    ledouAmount: '乐豆数量',
    selectDeduction: '选择抵扣',
    cashNeeded: '需现金',
    payNow: '立即支付',
    prompt: '提示',
    enterTransactionPassword: '请输入交易密码',
    confirm: '确定',
    paymentFailed: '支付失败',
    paymentSuccess: '支付成功',
    pleaseSelectLedouDeduction: '请选择乐豆抵扣',
    pleaseBindBankCard: '请绑定银行卡',
    pleaseEnterTransactionPassword: '请输入交易密码',
    name: '姓名',
    cardNumber: '卡号',
    reservedPhoneNumber: '预留手机号',
  },
  payPassword: {
    originalPassword: '原交易密码',
    enterOriginalPassword: '请输入原交易密码',
    transactionPassword: '交易密码',
    enterTransactionPassword: '请输入交易密码',
    confirmTransactionPassword: '确认交易密码',
    enterConfirmTransactionPassword: '请再次输入交易密码',
    confirm: '确认',
    pleaseEnterOriginalPassword: '请填写原交易密码',
    pleaseEnterTransactionPassword: '请填写交易密码',
    pleaseConfirmTransactionPassword: '请确认交易密码',
    passwordsDoNotMatch: '两次密码输入不一致',
  },
  confirmOrder: {
    actualPayment: '实付款',
    originalPrice: '原价',
    total: '合计',
    submitOrder: '提交订单',
    selectDeliveryMethod: '请选择配送方式',
    readAndAgree: '请先勾选"已阅读并同意《购买协议》"',
    confirmOrderPrompt: '温馨提示',
    confirmOrderContent: '是否确认下单?',
    outOfStock: '抱歉,库存不足',
  },
  goodsDetails: {
    productIntro: '产品介绍',
    setPayPasswordFirst: '请先设置交易密码',
    outOfStockOrRemoved: '该商品已下架或不存在，去逛逛别的吧~',
    immediateRental: '立即租赁',
    addToCart: '加入购物车',
    customerService: '客服',
    favorite: '收藏',
    shoppingCart: '购物车',
    stock: '库存',
    sales: '销量',
    views: '浏览量',
    selected: '已选',
    default: '默认',
    userReviews: '用户评价',
    positiveRate: '好评率',
    viewAll: '查看全部',
    noReviews: '暂无评价',
    earnFromFriendOrder: '好友下单最高可赚',
    maxEarnings: '好友下单最高可赚',
  },
  ai: {
    thinking: 'AI正在思考中...',
    askQuestion: '有问题，尽管问',
    error: '发生错误',
    send: '发送',
  },
  index: {
    bindSuperior: '绑定上级',
    enterSuperiorInviteCode: '请输入上级邀请码',
    confirmBinding: '确认绑定',
    latest: '最新',
    eliteTeam: '精英团队',
    viewMore: '查看更多',
    thisWeekStar: '本周新星',
    openShowcase: '开通橱窗',
    showcaseSelection: '橱窗选品',
    bindShowcase: '绑定橱窗',
    upgradePromoter: '升级推客',
    notYetOpen: '暂未开放',
    pleaseEnterSuperiorID: '请输入上级ID',
  },
  newsList: {
    all: '全部',
    noData: '暂无数据',
    publishTime: '发布时间:',
    views: '人浏览',
    systemNotice: '系统公告',
    helpCenter: '帮助中心',
  },
  newsDetails: {
    publishTime: '发布时间：',
    views: '人浏览',
    helpDetail: '帮助详情',
    noticeDetail: '公告详情',
  },
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    back: '返回',
    next: '下一步',
    submit: '提交',
    loading: '加载中',
    success: '成功',
    fail: '失败',
    copy: '复制',
    copySuccess: '复制成功',
    changePassword: '修改密码',
  },
  appUpdate: {
    newVersion: '发现新版本',
    updatePrompt: '有新的版本可用，是否立即更新？',
    downloading: '下载更新中',
    downloadComplete: '更新已下载',
    installPrompt: '更新已下载完成，安装并重启应用？',
    installFailed: '安装失败，请重试',
    downloadFailed: '下载失败，请重试',
    updateTip: '更新提示',
    restartPrompt: '新版本已经准备好，是否重启应用？',
    copied: '已复制',
    language: '语言',
    languageName: '简体中文',
    switchLanguage: '切换语言',
    login: '登录',
    logout: '退出登录',
    register: '注册',
    settings: '设置',
    profile: '个人资料',
    changePassword: '修改密码',
    pleaseEnter: '请输入',
    pleaseSelect: '请选择',
    testTranslations: '测试翻译',
  },
  user: {
    userInfo: '用户信息',
    nickname: '昵称',
    avatar: '头像',
    userId: '会员ID',
    level: '等级',
    loginToExperience: '登录体验更多功能',
    clickToLogin: '点击登录',
    teamInfo: '团队信息',
    settings: '设置',
    myRental: '我的租赁',
    myTeam: '我的团队',
    shareWithFriends: '分享好友',
    balance: '余额',
    rentalDeposit: '租赁押金',
    earnings: '收益',
    bindSuperior: '绑定上级',
    pleaseEnterSuperiorId: '请输入上级ID',
    confirmBinding: '确认绑定',
    bindingSuccess: '绑定成功',
    changePayPassword: '修改支付密码',
    userLevel: {
      v1: 'V1',
      v2: 'V2',
      v3: 'V3',
      v4: 'V4',
      v5: 'V5',
      validUser: '有效会员',
      registeredUser: '普通用户',
    },
    partnerLevel: {
      none: '无',
      junior: '初级合伙人',
      intermediate: '中级合伙人',
      senior: '高级合伙人',
      founding: '联创合伙人',
    }
  },
  settings: {
    languageSettings: '语言设置',
    currentLanguage: '当前语言',
    selectLanguage: '选择语言',
  }
};
