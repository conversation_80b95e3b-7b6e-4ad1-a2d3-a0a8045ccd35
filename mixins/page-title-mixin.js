/**
 * Mixin to handle page title internationalization
 * 
 * Usage:
 * 1. Import this mixin in your page component:
 *    import pageTitleMixin from '@/mixins/page-title-mixin';
 * 
 * 2. Add it to your component's mixins:
 *    mixins: [pageTitleMixin],
 * 
 * 3. The page title will be automatically set in the onShow lifecycle hook
 *    based on the mapping in utils/page-title.js
 * 
 * 4. If you need to set a custom title, you can do so by calling:
 *    this.setCustomPageTitle('customTitleKey');
 */

import { setPageTitle } from '@/utils/page-title';

export default {
  data() {
    return {
      customPageTitleKey: null
    };
  },
  
  onShow() {
    // Set the page title based on the current route and i18n
    setPageTitle(this, this.customPageTitleKey);
  },
  
  methods: {
    /**
     * Set a custom page title using an i18n key
     * @param {string} titleKey - The i18n key for the title
     */
    setCustomPageTitle(titleKey) {
      this.customPageTitleKey = titleKey;
      setPageTitle(this, titleKey);
    }
  },
  
  watch: {
    // Watch for language changes and update the title
    currentLanguage: {
      handler() {
        setPageTitle(this, this.customPageTitleKey);
      }
    }
  }
};
