<template>
	<view class="agent">
		<view class="agent-title flex">{{ $t('agent.title') }}</view>
		<view class="agent-list">
			<view class="agent-item flex" @tap="handleTapItem(1)">
				<view class="agent-item-memo">
					<view class="agent-item-title">{{ $t('agent.territory') }}</view>
					<view class="agent-item-memo-text">
						{{ $t('agent.territoryDesc') }}
					</view>

				</view>
				<view class="agent-item-image">
					<image class="agent-item-image-img" src="/static/images/ter.png" mode="heightFix"></image>
				</view>
			</view>
			<view class="agent-item flex" @tap="handleTapItem(2)">

				<view class="agent-item-memo">
					<view class="agent-item-title">{{ $t('agent.communitySubsidies') }}</view>
					<view class="agent-item-memo-text">
						{{ $t('agent.communitySubsidiesDesc') }}
					</view>

				</view>
				<view class="agent-item-image" style="padding-right: 30rpx;">
					<image class="agent-item-image-img" src="/static/images/com.png" mode="heightFix"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { mapGetters } from 'vuex'
import pageTitleMixin from '@/mixins/page-title-mixin'

export default {
	mixins: [pageTitleMixin],
	data() {
		return {}
	},
	onLoad() {

	},
	created() {

	},
	computed: {
		...mapGetters(['currentLanguage'])
	},
	methods: {
		handleTapItem(type) {
			if (type === 1) {
				uni.showToast({
					title: this.$t('agent.notOpenYet'),
					icon: 'none'
				})
				return
			}
			uni.navigateTo({
				url: '/pages/subsidy/subsidy'
			})
		}
	},
}
</script>

<style lang="scss" scoped>
.agent {
	padding: 32rpx 24rpx;
	padding-top: calc(env(safe-area-inset-top) + 32rpx);

	.agent-title {
		font-size: 26rpx;
		font-weight: bold;
		color: #fff;
		text-align: center;

		&::before {
			content: '';
			display: block;
			width: 2rpx;
			height: 20rpx;
			background-color: #fff;
			margin-right: 10rpx;
		}
	}

	.agent-list {
		margin-top: 24rpx;
		gap: 20rpx;

		.agent-item {
			background-image: url('@/static/images/<EMAIL>');
			background-size: 100% 100%;
			background-repeat: no-repeat;
			padding: 24rpx 24rpx;
			justify-content: space-between;
			margin-bottom: 24rpx;
			.agent-item-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #04F9FC;
				margin-bottom: 10rpx;
			}

			.agent-item-memo {
				.agent-item-memo-text {
					font-size: 24rpx;
					color: #6CCCCE;
					margin-right: 10rpx;
				}


			}

			.agent-item-image {
				height: 130rpx;

				.agent-item-image-img {
					width: 100%;
					height: 100%;
				}
			}
		}
	}

}
</style>
