<template>
	<view class="ai">
		<startup-guide :app-version="appVersion" :show-after-update="true" />
		<view class="bg">
			<u-image class="bg-img" :src="bggif" width="100%" height="100%"></u-image>
		</view>
		<!-- <view :class="['ai-logo', { 'has-list': list.length > 0 }]">
			<u-image src="@/static/images/<EMAIL>" width="100%" height="100%"></u-image>
		</view> -->
		<!-- 会话列表 -->
		<view class="ai-list">
			<view :class="['ai-list-item', item.type]" v-for="(item, index) in list" :key="index">
				<view class="ai-list-item-avatar">
					<u-image :src="item.avatar" width="60" height="60"></u-image>
				</view>
				<view class="ai-list-item-content">
					<view class="ai-list-item-content-text">
						<RectBox :borderNone="item.type !== 'assistant'">
							<u-icon v-if="item.status === 'error'" name="info" color="#FF0000" size="24"
								style="margin-right: 10rpx;border-radius: 100%;border: 1px solid #FF0000;padding: 3rpx;"></u-icon>
							<rich-text v-if="item.type === 'assistant'"
								:nodes="formatContent(item.content)"></rich-text>
							<text v-else>{{ item.content }}</text>
						</RectBox>
					</view>
				</view>
			</view>
			<view :class="['ai-list-item', 'ai-list-item-loading']" v-if="sendLoading">
				<view class="ai-list-item-avatar">
					<u-image :src="assistantAvatar" width="60" height="60"></u-image>
				</view>
				<view class="ai-list-item-content">
					<RectBox :borderNone="true">
						<view class="ai-list-item-content-text flex font-size-28">
							<u-loading mode="flower" size="40" style="margin-right: 10rpx;"></u-loading>{{ $t('ai.thinking')}}
						</view>
					</RectBox>
				</view>
			</view>
		</view>
		<!-- AI会话输入框 -->
		<view class="ai-input">
			<u-input v-model="inputContent" type="textarea" :disabled="sendLoading" :clearable="false" :fixed="true"
				height="206" :placeholder="$t('ai.askQuestion')" :custom-style="{ color: '#04F9FC' }"
				placeholder-style="color: #5C81AB" confirm-type="send" @confirm="handleConfirm" />
			<button :class="['send', { 'show': !!inputContent.length }]" @click="handleConfirm(inputContent)">{{
				$t('ai.send') }}</button>
		</view>
		<!-- income -->
		<!-- <view class="income">
			<u-image class="income-gif" :src="incomeGif" width="100%" height="100%"></u-image>
		 </view> -->
	</view>
</template>

<script>
import { mapActions, mapMutations, mapGetters } from 'vuex'
import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
import MescrollMoreItemMixin from "@/components/mescroll-uni/mixins/mescroll-more-item.js";
import pageTitleMixin from '@/mixins/page-title-mixin'
import { completions } from '@/api/user'
import uImage from '../../components/uview-ui/components/u-image/u-image.vue';
import RectBox from '../../components/common/rect-box.vue';
import aigif from '@/static/images/aigif.gif'
import incomegif from '@/static/images/income.gif'

// This page has been internationalized with i18n support

let systemInfo = uni.getSystemInfoSync()
export default {
	components: { uImage, RectBox },
	mixins: [MescrollMixin, MescrollMoreItemMixin, pageTitleMixin], // 使用mixin
	data() {
		return {
			inputContent: '',
			sendLoading: false,
			assistantAvatar: '/static/images/<EMAIL>',
			incomeAudio: "https://api.aichatgpt.net.cn/uploads/mp3/income.mp3",
			// incomeGif: '/static/images/income.gif',
			list: [],
			appVersion: '1.0.1',
			bggif: aigif,
		}
	},
	onShow() {
		try {
			const now = new Date();
			const todayDateStr = now.toISOString().split('T')[0];
			const lastCacheDate = uni.getStorageSync('ai_list_cache_date');
			if (lastCacheDate !== todayDateStr) {
				uni.removeStorageSync('ai_list');
				uni.setStorageSync('ai_list_cache_date', todayDateStr);
			}
		} catch (error) {}
		this.getUser();
		this.list = uni.getStorageSync('ai_list') || []
	},
	watch: {
		list: {
			handler(newVal) {
				//针对会话记录做缓存
				uni.setStorageSync('ai_list', newVal)
			},
		}
	},
	methods: {
		...mapActions(['getUser']),
		//ai 容器滚动到底部
		scrollToBottom() {
			uni.pageScrollTo({
				scrollTop: 9999999,
				duration: 300
			})
		},
		// 格式化AI返回的内容
		formatContent(content) {
			if (!content) return '';

			// 处理代码块
			content = this.formatCodeBlocks(content);

			// 处理列表
			content = this.formatLists(content);

			// 处理链接
			content = this.formatLinks(content);

			// 处理粗体和斜体
			content = this.formatTextStyles(content);

			// 处理换行
			content = content.replace(/\n/g, '<br>');

			return content;
		},
		// 格式化代码块
		formatCodeBlocks(content) {
			// 处理多行代码块 ```code```
			content = content.replace(/```([\s\S]*?)```/g, (match, code) => {
				// 获取代码语言（如果有）
				const firstLine = code.trim().split('\n')[0];
				let language = '';
				let codeContent = code;

				if (firstLine && !firstLine.includes('=') && !firstLine.includes(';') && !firstLine.includes('{') && !firstLine.includes('}')) {
					language = firstLine;
					codeContent = code.substring(firstLine.length);
				}

				// 转义HTML特殊字符
				codeContent = codeContent
					.replace(/&/g, '&amp;')
					.replace(/</g, '&lt;')
					.replace(/>/g, '&gt;')
					.replace(/"/g, '&quot;')
					.replace(/'/g, '&#39;');

				return `<div class="code-block"><div class="code-header">${language}</div><pre><code>${codeContent}</code></pre></div>`;
			});

			// 处理行内代码 `code`
			content = content.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

			return content;
		},
		// 格式化列表
		formatLists(content) {
			// 处理无序列表
			content = content.replace(/^[\s]*?[-*+][\s]+(.*?)$/gm, '<li>$1</li>');
			content = content.replace(/<li>.*?<\/li>(?:\s*<li>.*?<\/li>)+/g, (match) => {
				return `<ul>${match}</ul>`;
			});

			// 处理有序列表
			content = content.replace(/^[\s]*?(\d+)\.[\s]+(.*?)$/gm, '<li>$2</li>');
			content = content.replace(/<li>.*?<\/li>(?:\s*<li>.*?<\/li>)+/g, (match) => {
				if (!match.startsWith('<ul>')) {
					return `<ol>${match}</ol>`;
				}
				return match;
			});

			return content;
		},
		// 格式化链接
		formatLinks(content) {
			// 处理链接 [text](url)
			content = content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="ai-link">$1</a>');

			// 处理纯URL
			content = content.replace(/(https?:\/\/[^\s<]+)/g, (match) => {
				if (match.indexOf('<a href') === -1) {
					return `<a href="${match}" class="ai-link">${match}</a>`;
				}
				return match;
			});

			return content;
		},
		// 格式化文本样式（粗体、斜体）
		formatTextStyles(content) {
			// 处理粗体 **text**
			content = content.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

			// 处理斜体 *text*
			content = content.replace(/\*([^*]+)\*/g, '<em>$1</em>');

			return content;
		},
		playWelcomeAudio() {
			try {
				// 停止之前的音频（如果存在）
				if (this.audioContext) {
					this.audioContext.stop();
					this.audioContext.destroy();
				}

				// 创建新的音频上下文
				this.audioContext = uni.createInnerAudioContext();
				this.audioContext.src = this.incomeAudio;
				this.audioContext.autoplay = true;
				this.audioContext.loop = false;

				// 音频播放完成后销毁上下文
				this.audioContext.onEnded(() => {
					if (this.audioContext) {
						this.audioContext.destroy();
						this.audioContext = null;
					}
				});

				// 音频播放错误处理
				this.audioContext.onError((error) => {
					console.log('音频播放错误:', error);
					if (this.audioContext) {
						this.audioContext.destroy();
						this.audioContext = null;
					}
				});

				// 开始播放
				this.audioContext.play();
			} catch (error) {
				console.log('播放欢迎音频失败:', error);
			}
		},
		handleConfirm(value) {
			this.list.push({
				avatar: this.userInfo.avatar,
				content: value,
				type: 'user'
			})
			setTimeout(() => {
				this.sendLoading = true
				this.$nextTick(() => {
					this.scrollToBottom()
				})
			}, 100)
			completions({
				problem: value
			}).then(res => {
				this.sendLoading = false
				if (res.code == 1) {
					this.inputContent = ''
					this.list.push({
						avatar: this.assistantAvatar,
						content: res.data.content,
						type: 'assistant'
					})
					if(res.data.is_income == 1){
						this.bggif = incomegif;
						this.playWelcomeAudio();
						setTimeout(() => {
							this.bggif = aigif;
						}, 5000)
					}
					this.$nextTick(() => {
						this.scrollToBottom();
					})
				} else {
					//失败时，将用户输入的内容标记为错误
					this.list[this.list.length - 1].status = 'error'
					this.$toast({
						title: res.msg || this.$t('ai.error')
					})
				}
			})
		}
	},
	computed: {
		...mapGetters(["userInfo", "currentLanguage"]),
	}
}
</script>

<style lang="scss" scoped>
.ai {
	position: relative;

	.income {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 99999;
	}

	.bg {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;

		.bg-img {
			width: 100%;
			height: 100%;
		}
	}

	.ai-logo {
		width: 286rpx;
		height: 286rpx;
		position: absolute;
		top: 30%;
		left: 50%;
		transform: translateX(-50%);

		&.has-list {
			position: unset;
			width: 128rpx;
			height: 128rpx;
			margin: 72rpx auto;
			transform: unset;
		}
	}

	.ai-list {
		position: relative;
		z-index: 1;
		padding: 0 24rpx;
		padding-bottom: 390rpx;
		padding-top: 100rpx;

		.ai-list-item {
			width: 100%;
			display: flex;
			align-items: flex-start;
			margin-top: 44rpx;

			&.user {
				flex-direction: row-reverse;

				.ai-list-item-avatar {
					margin-right: 0;
					margin-left: 20rpx;
				}

				.ai-list-item-content {
					background-color: #0292F5;
					background-image: none;

					.ai-list-item-content-text {
						color: #fff;
						display: flex;
						align-items: center;

						/* 用户消息中的链接颜色调整 */
						.ai-link {
							color: #ffffff;
							text-decoration: underline;
						}

						/* 用户消息中的代码块颜色调整 */
						.code-block {
							border-color: rgba(255, 255, 255, 0.3);
							background-color: rgba(255, 255, 255, 0.1);

							.code-header {
								background-color: rgba(255, 255, 255, 0.2);
								color: #fff;
							}

							pre code {
								color: #fff;
							}
						}

						.inline-code {
							background-color: rgba(255, 255, 255, 0.2);
							color: #fff;
							border-color: rgba(255, 255, 255, 0.3);
						}
					}
				}
			}

			.ai-list-item-avatar {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				overflow: hidden;
				margin-right: 20rpx;
			}

			.ai-list-item-content {
				width: 622rpx;
				// padding: 28rpx;
				background-color: none;
				// background-image: url('@/static/images/<EMAIL>');
				// background-size: 100% 100%;
				// background-repeat: no-repeat;
				// border: 1rpx solid #0292F5;

				.ai-list-item-content-text {
					font-size: 30rpx;
					color: $-color-primary;

					/* 格式化内容样式 */
					.code-block {
						margin: 20rpx 0;
						background-color: #f8f8f8;
						border-radius: 8rpx;
						overflow: hidden;
						border: 1px solid #e0e0e0;

						.code-header {
							background-color: #e8e8e8;
							padding: 10rpx 20rpx;
							font-size: 24rpx;
							color: #666;
							font-weight: bold;
						}

						pre {
							margin: 0;
							padding: 20rpx;
							overflow-x: auto;

							code {
								font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
								font-size: 24rpx;
								line-height: 1.6;
								white-space: pre;
							}
						}
					}

					.inline-code {
						background-color: #f0f0f0;
						padding: 4rpx 8rpx;
						border-radius: 4rpx;
						font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
						font-size: 24rpx;
						color: #d14;
						border: 1px solid #e0e0e0;
					}

					ul,
					ol {
						padding-left: 40rpx;
						margin: 16rpx 0;
					}

					li {
						margin-bottom: 10rpx;
					}

					.ai-link {
						color: #0366d6;
						text-decoration: none;
						word-break: break-all;

						&:hover {
							text-decoration: underline;
						}
					}

					strong {
						font-weight: bold;
					}

					em {
						font-style: italic;
					}
				}
			}
		}
	}

	.ai-input {
		width: 702rpx;
		padding: 0 24rpx;
		position: fixed;
		z-index: 999;
		bottom: calc(env(safe-area-inset-bottom) + 115rpx);
		left: 50%;
		transform: translateX(-50%);
		background-image: url('@/static/images/<EMAIL>');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		background-color: rgba(0, 0, 0, 0.1);
		backdrop-filter: blur(10px);
		overflow: hidden;

		.send {
			background-color: $-color-primary;
			width: 110rpx;
			height: 60rpx;
			position: absolute;
			right: 20rpx;
			top: 20rpx;
			margin: auto;
			border-radius: 0;
			line-height: 60rpx;
			transition: all 0.3s ease;
			transform: translate3d(0, 30%, 0);
			opacity: 0;

			&.show {
				transform: translate3d(0, 0, 0);
				opacity: 1;
			}
		}
	}
}
</style>
