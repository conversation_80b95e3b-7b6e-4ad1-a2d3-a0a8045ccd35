<template>
	<view class="giveaway">

		<view class="acount-login">
			<!-- <view class="input-item">
				<view class="input-label flex normal">{{ $t('topup.rechargeChain') }}</view>
				<view class="input-content">
					<view class="topup-coin">
						<view class="topup-coin-item flex row-between">
							<view class="topup-coin-item-title">{{ $t('topup.rechargeChain') }}</view>
							<view class="topup-coin-item-content">
								<u-radio-group v-model="chain" shape="circle">
									<u-radio shape="circle" :active-color="colorConfig.primary" :name="1"
										activeColor="#04F9FC" activeIconColor="#1D1D3B">{{ $t('topup.tronChain')
										}}</u-radio>
									<u-radio shape="circle" :active-color="colorConfig.primary" :name="2"
										activeColor="#04F9FC" activeIconColor="#1D1D3B">{{ $t('topup.binanceChain')
										}}</u-radio>
								</u-radio-group>
							</view>
						</view>
					</view>
				</view>
			</view> -->
			<view class="input-item">
				<view class="input-label flex normal">{{ $t('bindWallet.walletAddress') }}</view>
				<view class="input-content">
					<view class="input">
						<u-input v-model="wallet_address" style="width: 100%;" :custom-style="{
							'height': '88rpx'
						}" :placeholder="$t('bindWallet.enterWalletAddress')" placeholder-style="color: #90BAEB;"
							:input-border="false" />
					</view>
				</view>
			</view>
			<view class="login-wrap">
				<button size="lg" class="login-btn" type="primary" @click="handleBindWallet">{{
					$t('bindWallet.bindWallet') }}</button>
			</view>
		</view>
	</view>
</template>

<script>
import {
	mapMutations,
	mapActions,
	mapGetters
} from 'vuex'
import {
	bindWallet
} from '@/api/user'
import {
	isWeixinClient,
	currentPage,
	client,
	trottle
} from '@/utils/tools'
import Cache from "@/utils/cache"
import pageTitleMixin from '@/mixins/page-title-mixin'

export default {
	mixins: [pageTitleMixin],
	data() {
		return {
			wallet_address: '',
			chain: 1,
			submitLoading: false
		};
	},

	onLoad(option) {
		// 页面加载时的逻辑
	},

	methods: {
		handleBindWallet() {
			if (!this.wallet_address) {
				uni.showToast({
					title: this.$t('bindWallet.pleaseEnterWalletAddress'),
					icon: 'none'
				})
				return
			}

			if (this.submitLoading) {
				return
			}

			this.submitLoading = true

			// 调用绑定钱包地址的API
			bindWallet({
				chain: this.chain,
				wallet_address: this.wallet_address
			}).then(res => {
				this.submitLoading = false
				if (res.code === 1) {
					this.wallet_address = ''
					uni.showToast({
						title: this.$t('bindWallet.bindSuccess'),
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			}).catch(err => {
				this.submitLoading = false
				uni.showToast({
					title: this.$t('bindWallet.bindFailed'),
					icon: 'none'
				})
			})
		}
	},
	computed: {
		...mapGetters(['currentLanguage'])
	}
};
</script>
<style lang="scss">
page {
	background-color: #fff;
	text-align: center;
	padding: 0;

	.giveaway {
		padding-top: 100rpx;
		display: flex;
		flex-direction: column;

		/deep/ .u-radio__icon-wrap {
			margin-left: 20rpx;
		}

		/deep/ .u-radio__label {
			color: #fff;
			font-size: 26rpx;
			margin-right: 0;
		}

		.topup-coin {
			padding: 0 24rpx;
			background-image: url('@/static/images/<EMAIL>');
			background-size: 100% 100%;
			background-repeat: no-repeat;

			.topup-coin-item {
				height: 98rpx;
				border-bottom: 1px dashed #90BAEB;

				&.address {
					height: auto;
					padding: 28rpx 0;

					.topup-coin-item-content {
						margin-left: 90rpx;
					}
				}

				&:last-child {
					border-bottom: none;
				}

				.topup-coin-item-title {
					min-width: 0;
					flex: 1;
					font-size: 26rpx;
					color: #A6CFFF;
					word-break: break-all;
				}

				.topup-coin-item-content {
					.topup-coin-item-button {
						width: 128rpx;
						height: 64rpx;
						line-height: 64rpx;
						background-color: $-color-primary;
						color: #1D1D3B;
						padding: 0 10rpx;
						border-radius: 0rpx;
					}
				}
			}
		}

		.acount-login {

			display: flex;
			flex-direction: column;
			align-items: center;
			box-sizing: border-box;
			min-height: 0;

			.input-item {
				margin-bottom: 32rpx;
				width: 702rpx;

				.input-label {
					flex: none;
					margin-bottom: 24rpx;
					color: #fff;
					font-size: 26rpx;

					.required {
						color: #F93E3E;
						margin-right: 5rpx;
						vertical-align: middle;
					}

					&:before {
						content: '';
						display: inline-block;
						height: 20rpx;
						width: 2rpx;
						margin-right: 10rpx;
						background-color: #fff;
					}
				}
			}

			.input {
				background-image: url('@/static/images/<EMAIL>');
				background-size: 100% 100%;
				background-repeat: no-repeat;
				padding: 0 24rpx;
			}
		}

		.login-wrap {
			position: fixed;
			display: flex;
			flex-direction: column;
			align-items: center;
			bottom: 0;
			width: 100%;
			background-color: #011750;
			padding-top: 18rpx;

			.login-btn {
				border-radius: 0rpx;
				width: 702rpx;
				margin: 20rpx 0 50rpx;
				color: #1D1D3B;
				font-size: 34rpx;
			}
		}
	}
}
</style>
