<template>
	<view class="daren">
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @up="upCallback" :up="upOption" :down="{auto: false}" @down="downCallback">
			<view class="main">
				<view class="user">
					<view class="list">
						<view class="newsCard" v-for="(item,index) in dataList" :key="index">
							<view class="newsCardBox flex row-left col-center" @click="clickLook(item)">
								<view class="newsCardL flex-col row-center col-center" 
									style="width: 100rpx;height: 100rpx;position: relative;background-color: #f1f1f1;border-radius: 6rpx;overflow: hidden;" >
									<image :src="item.avatar" mode="widthFix" style="width: 100%;display: block;"></image>
								</view>
								<view class="newsCardM p-10 flex-col row-left col-top">
									<view class="newsCardMTitle">
										<text>{{item.nickname}}</text>
									</view>
								</view>
								<view v-if="is_jump_shipin==1" class="txt" style="position: absolute;top: 50%;right:0;color: #fff;transform: translateY(-50%);
									background-color: #FF2C3C;border-radius: 60rpx;font-size: 24rpx;line-height: 46rpx;padding: 0 15rpx;">
									<view>去完成</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
		<u-popup v-model="isShow" width="90%" mode="center" length="70%" border-radius="12" :closeable="true" z-index="9999" 
			close-icon-color="#fff" close-icon-size="40" @open="open" @close="close">
			<view class="bg-white text-center" style="padding:40rpx;">
				<view class="lg text-center m-b-30 bold" style="color: #555;">完成任务</view>
				<view class="m-b-30" style="color: #555;">步骤一：单击进入推客视频号</view>
				<view class="primary m-b-30 text-center" @click="clickGoChuchuang">《{{shipingName}}》</view>
				<view class="m-b-30" style="color: #555;">步骤二：关注视频号{{shipingName}}</view>
				<view class="m-b-5" style="color: #555;">步骤三：在推客橱窗消费一单</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import { mapActions, mapMutations, mapGetters } from 'vuex'
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import MescrollMoreItemMixin from "@/components/mescroll-uni/mixins/mescroll-more-item.js";
	import {showcaseList} from "@/api/youpin.js"
	
	let systemInfo = uni.getSystemInfoSync()
	export default {
		mixins: [MescrollMixin,MescrollMoreItemMixin], // 使用mixin
		data() {
			return {
				top:'',
				status: 'nomore',
				iconType: 'flower',
				loadText: {
					loadmore: '轻轻上拉',
					loading: '努力加载中',
					nomore: '没有更多数据了'
				},
				dataList:[],
				// Tabs 列表
				upOption: {
					page: {
						size: 15 // 每页数据的数量,默认10
					},
					onScroll:true,
					noMoreSize: 5, // 配置列表的总数量要大于等于5条才显示'-- END --'的提示
					empty: {
						icon: '@/bundle_c/static/nodata.png',
						tip: '暂无数据', // 提示
					}
				},
				isShow: false, //弹框
				orderNumber:'', //订单号
				shipingName:'' ,//视频号名称
				shipingId:'', //视频号id
				isClick: true,
				is_jump_shipin:0,
			}
		},
		onShow() {
			this.getUser();
			this.is_jump_shipin = uni.getStorageSync('is_jump_shipin');
		},
		methods: {
			...mapActions([ 'getUser']),
			// 上拉加载
			upCallback(page) {
				const pageNum = page.num; // 页码, 默认从1开始
				const pageSize = page.size; // 页长, 默认每页10条
				
				showcaseList({
					page_size: pageSize,
					page_no: pageNum,
				}).then(({
					data
				}) => {
					if (page.num == 1) this.dataList = [];
					const curPageData = data.list;
					const curPageLen = curPageData.length;
					const hasNext = !!data.more;
					this.dataList = this.dataList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen, hasNext);
				}).catch(() => {
					this.mescroll.endErr()
				})
			},
			//跳转到视频号
			clickLook(item){
				if(this.is_jump_shipin == 0) return false
				if(this.isClick){
					this.isClick = false
					setTimeout(()=>{
						this.isClick = true
					},1200)
					//这里点去完成的时候，判断一下如果没有绑定橱窗，先去绑定橱窗
					// if(this.userInfo.is_bind_showcase != 1){
					// 	this.$toast({
					// 		title: '请先去绑定视频号',
					// 		duration: 1000,
					// 	}, {
					// 		tab: 2,
					// 		url: "/bundle/pages/user_profile/user_profile"
					// 	});
					// 	/* this.$Router.push({
					// 		//path: '/bundle_c/pages/chuchuang/chuchuang',
					// 		path: '/bundle/pages/user_profile/user_profile',
					// 	}) */
					// 	return false
					// }
					//this.isShow = true
					this.shipingName = item.nickname //视频号名称
					this.shipingId = item.shop_id //视频号id
					this.clickGoChuchuang()
				}
			},
			onPageScroll(res) {
				this.top = res.scrollTop;
			},
			//跳转到视频号
			clickGoChuchuang(){
				wx.openChannelsUserProfile({
				   finderUserName: this.shipingId //视频号id
				})
			},
			open(){
				uni.hideTabBar()
			},
			close(){
				uni.showTabBar()
			},
		},
		computed: {
			...mapGetters(["userInfo"]),
		}
	}
</script>

<style lang="scss" scoped>
	page{
	}
	.daren{
		position: relative;
		
		.main{
			padding: 24rpx 0 0 0;
			position: relative;
			z-index: 3;
			
			.user{
				padding: 0 24rpx 24rpx 24rpx;
				border-radius: 24rpx;
				
				.list{
					border-radius: 24rpx;
					background-color: #fff;
					.newsCard{
						padding: 0 36rpx;
						
						&:last-child .newsCardBox{
							border-bottom: 0;
						}
						
						.newsCardBox{
							padding: 40rpx 0;
							border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);
							position: relative;
							
							.newsCardL{
								margin-right: 8rpx;
							}
							.newsCardM{
								
								.newsCardMTitle{
									font-size: 30rpx;
									line-height: 32rpx;
									color: #383637;
								}
								.newsCardMTime{
									font-size: 30rpx;
									line-height: 32rpx;
								}
								.newsCardMVip{
									margin-top: 6rpx;
									font-size: 20rpx;
									color: #919191;
									display: flex;
									justify-content: flex-start;
									align-items: center;
									
									image{
										width: 32rpx;
										height: 32rpx;
										display: block;
										margin-right: 8rpx;
									}
								}
							}
							.newsCardR{
								height: 100%;
								margin-left: auto;
								display: flex;
								flex-direction: column;
								justify-content: space-between;
								align-items: flex-end;
								
								.txt{
									color: #333;
									text-align: right;
									
									.ai{
										letter-spacing :3rpx;
									}
									
									&:nth-child(2){
										margin-top: 4rpx;
									}
								}
							}
						}
					}
				}
			}
		}
	}
</style>
