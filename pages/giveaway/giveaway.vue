<template>

	<view class="giveaway">
		<view class="acount-login">
			<view class="input-item">
				<view class="input-label flex normal">{{ $t('giveaway.receiverId') }}</view>
				<view class="input-content">
					<view class="input">
						<u-input v-model="address" style="width: 100%;" :custom-style="{
							'height': '88rpx'
						}" :placeholder="$t('giveaway.enterReceiverId')" placeholder-style="color: #90BAEB;" :input-border="false" />
					</view>
				</view>
			</view>
			<view class="input-item">
				<view class="input-label flex normal">{{ $t('giveaway.giftAmount') }}</view>
				<view class="input-content">
					<view class="input">
						<u-input v-model="amount" style="width: 100%;" :custom-style="{
							'height': '88rpx'
						}" :placeholder="$t('giveaway.enterGiftAmount')" placeholder-style="color: #90BAEB;" :input-border="false" />
					</view>
				</view>
			</view>
			<view class="flex" style="width: 702rpx;">
				<view class="lighter">{{ $t('giveaway.availableBalance') }}：{{ wallet.user_integral || 0 }} USDT</view>
				<view class="primary m-l-24" @click="handleGiveawayAll">{{ $t('giveaway.giftAll') }}</view>
			</view>
			<view class="login-wrap">
				<button size="lg" class="login-btn" type="primary" @click="handleGiveaway">{{ $t('giveaway.gift') }}</button>
			</view>
		</view>
	</view>
</template>

<script>
import {
	mapMutations,
	mapActions,
	mapGetters
} from 'vuex'
import {
	accountLogin,
	codeLogin,
	sendSms,
	wxpLogin,
	smsCodeLogin,
	opLogin,
	authLogin
} from '@/api/app';
import {
	bindSuperior, getWxMnpMobile, getMobileByMnp,
	applyWithdraw,
	getWallet
} from '@/api/user'
import wechath5 from '@/utils/wechath5'
import {
	isWeixinClient,
	currentPage,
	client,
	trottle
} from '@/utils/tools'
import {
	SMSType
} from "@/utils/type"
import Cache from "@/utils/cache"
import {
	BACK_URL,
	INVITE_CODE
} from '@/config/cachekey'
import {
	getWxCode,
	getUserProfile
} from '@/utils/login'
import pageTitleMixin from '@/mixins/page-title-mixin'
const loginType = {
	ACCOUNT_LOGIN: 0,
	SMS_CODE_LOGIN: 1,
}
export default {
	mixins: [pageTitleMixin],
	data() {
		return {
			isAgreement: false,
			password: '',
			address: '',
			amount: '',
			code: '',
			isWeixin: '',
			loginType: 0,
			smsCode: '',
			codeTips: '',
			telephone: "",
			text: '',
			phoneNew: '',
			wallet: {},
			submitLoading: false
		};
	},

	onLoad(option) {
		this.queryWallet()
	},
	onUnload() {

	},
	methods: {
		queryWallet() {
			getWallet().then(res => {
				this.wallet = res.data
			})
		},
		handleGiveaway() {
			if (!this.address) {
				uni.showToast({
					title: this.$t('giveaway.pleaseEnterGiftAddress'),
					icon: 'none'
				})
				return
			}
			if (!this.amount) {
				uni.showToast({
					title: this.$t('giveaway.pleaseEnterGiftAmount'),
					icon: 'none'
				})
				return
			}
			if (this.submitLoading) {
				return
			}
			this.submitLoading = true
			applyWithdraw({
				lx: 2,
				sn: this.address,
				money: this.amount
			}).then(res => {
				this.submitLoading = false
				if (res.code === 1) {
					this.queryWallet()
					this.address = ''
					this.amount = ''
					uni.showToast({
						title: this.$t('giveaway.giftSuccess'),
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			})
		},
		handleGiveawayAll() {
			this.amount = this.wallet.user_money
		}
	},
	computed: {
		...mapGetters(['currentLanguage'])
	}
};
</script>
<style lang="scss">
page {
	background-color: #fff;
	text-align: center;
	padding: 0;

	.giveaway {
		display: flex;
		flex-direction: column;



		.acount-login {
			padding-top: 100rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			box-sizing: border-box;
			min-height: 0;

			.logo {

				height: 80rpx;
				margin-bottom: 100rpx;
			}

			.input-item {
				margin-bottom: 32rpx;
				width: 702rpx;

				.input-label {
					flex: none;
					margin-bottom: 24rpx;
					color: #fff;
					font-size: 26rpx;

					.required {
						color: #F93E3E;
						margin-right: 5rpx;
						vertical-align: middle;
					}

					&:before {
						content: '';
						display: inline-block;
						height: 20rpx;
						width: 2rpx;
						margin-right: 10rpx;
						background-color: #fff;
					}
				}
			}

			.input {
				background-image: url('@/static/images/<EMAIL>');
				background-size: 100% 100%;
				background-repeat: no-repeat;
				padding: 0 24rpx;
			}

			.sms-btn {
				border: 1px solid $-color-primary;
				width: 176rpx;
				height: 60rpx;
				box-sizing: border-box;
			}

			.wx-login {
				margin-top: 60rpx;
				margin-bottom: 60rpx;

				.image {
					margin-top: 40rpx;
					width: 80rpx;
					height: 80rpx;
				}
			}
		}

		.login-wrap {
			position: fixed;
			display: flex;
			flex-direction: column;
			align-items: center;
			bottom: 0;
			width: 100%;
			background-color: #011750;
			padding-top: 18rpx;

			.login-btn {
				border-radius: 0rpx;
				width: 702rpx;
				margin: 20rpx 0 50rpx;
				color: #1D1D3B;
				font-size: 34rpx;
			}
		}
	}
}
</style>
