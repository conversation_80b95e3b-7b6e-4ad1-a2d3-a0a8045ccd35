<template>
  <view class="i18n-test">
    <view class="header">
      <view class="title">{{ $t('settings.languageSettings') }}</view>
    </view>

    <view class="content">
      <view class="section">
        <view class="section-title">{{ $t('settings.currentLanguage') }}</view>
        <view class="language-info">
          {{ currentLanguage === 'zh-CN' ? '简体中文' : 'English' }}
        </view>
      </view>

      <view class="section">
        <view class="section-title">{{ $t('settings.selectLanguage') }}</view>
        <view class="language-list">
          <view
            class="language-item"
            v-for="(lang, index) in languages"
            :key="index"
            @tap="selectLanguage(lang.value)"
          >
            <view class="language-item-content">
              <text class="language-name">{{ lang.label }}</text>
              <u-icon v-if="currentLanguage === lang.value" name="checkmark" color="#04F9FC" size="32"></u-icon>
            </view>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">{{ $t('common.testTranslations') }}</view>
        <view class="translation-list">
          <view class="translation-item">
            <view class="translation-key">common.confirm</view>
            <view class="translation-value">{{ $t('common.confirm') }}</view>
          </view>
          <view class="translation-item">
            <view class="translation-key">common.cancel</view>
            <view class="translation-value">{{ $t('common.cancel') }}</view>
          </view>
          <view class="translation-item">
            <view class="translation-key">user.userInfo</view>
            <view class="translation-value">{{ $t('user.userInfo') }}</view>
          </view>
          <view class="translation-item">
            <view class="translation-key">user.myTeam</view>
            <view class="translation-value">{{ $t('user.myTeam') }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';

export default {
  data() {
    return {
      languages: [
        { label: '简体中文', value: 'zh-CN' },
        { label: 'English', value: 'en' },
        { label: '日本語', value: 'ja' },
        { label: '한국어', value: 'ko' },
        { label: 'Русский', value: 'ru' },
        { label: 'Español', value: 'es' },
        { label: 'Deutsch', value: 'de' },
        { label: 'Français', value: 'fr' }
      ]
    };
  },
  computed: {
    ...mapGetters(['currentLanguage'])
  },
  methods: {
    ...mapActions(['changeLanguage']),
    selectLanguage(lang) {
      if (this.currentLanguage !== lang) {
        this.$i18n.locale = lang;
        this.changeLanguage(lang);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.i18n-test {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30rpx;

  .header {
    margin-bottom: 30rpx;

    .title {
      font-size: 36rpx;
      font-weight: bold;
    }
  }

  .content {
    .section {
      background-color: #fff;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 30rpx;

      &-title {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 20rpx;
      }

      .language-info {
        font-size: 32rpx;
        padding: 20rpx 0;
      }

      .language-list {
        .language-item {
          padding: 20rpx 0;
          border-bottom: 1px solid #eee;

          &:last-child {
            border-bottom: none;
          }

          &-content {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            .language-name {
              font-size: 28rpx;
            }
          }
        }
      }

      .translation-list {
        .translation-item {
          padding: 20rpx 0;
          border-bottom: 1px solid #eee;

          &:last-child {
            border-bottom: none;
          }

          .translation-key {
            font-size: 24rpx;
            color: #999;
            margin-bottom: 10rpx;
          }

          .translation-value {
            font-size: 28rpx;
          }
        }
      }
    }
  }
}
</style>
