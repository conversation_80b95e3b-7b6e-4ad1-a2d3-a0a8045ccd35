<template>
	<view class="index index-bg">
		<!-- Startup Guide Video Component -->
		<!-- <startup-guide
			:app-version="appVersion"
			:show-after-update="true"
			@close="onStartupGuideClose"
		/> -->
		<u-sticky offset-top="0" h5-nav-height="0" bg-color="transparent">
			<view class="index-bg">
				<u-navbar :is-fixed="false" :border-bottom="false" :background="{ background: 'transparent' }"
					:is-back="false">
					<view class="flex-1 flex row" style="position: relative;">
						<u-image src="@/static/images/<EMAIL>" width="48rpx" height="48rpx"></u-image>
					</view>
				</u-navbar>
			</view>
		</u-sticky>
		<!-- 轮播 -->
		<ad-swipers :pid="7" height="320rpx" radius="0" class="m-t-20"></ad-swipers>
		<!-- 资讯 -->
		<router-link v-if="homeData.headlines" class="" :to="{path: '/bundle_c/pages/news_list/news_list'}">
			<view class="information flex m-t-20">
				<view>
					<u-icon name="volume-up" color="#0292F5" size="46"></u-icon>
				</view>
				<!-- <text class="gap-line"></text> -->
				<view class="news flex-1 flex">
					<view class="shade"></view>
					<swiper class="flex-1" autoplay="true" style="height: 76rpx;" vertical="true" circular="true"
						:interval="3000">
						<swiper-item v-for="(item, index) in homeData.headlines" :key="index" class="flex">
							<view class="flex-none">
								<u-tag v-if="item.is_new" shape="circle" :text="$t('index.latest')" size="mini" type="primary"
									mode="plain" />
							</view>
							<view class="text-swiper m-l-10 line-1">{{item.title}}</view>
						</swiper-item>
					</swiper>
				</view>
				<!-- <u-icon name="arrow-right"></u-icon> -->
			</view>
		</router-link>
		<!-- 导航入口 -->
		<!-- <view class="nav m-t-20 m-b-20" v-if="newNavList.length">
			<swiper :style="'height:' + navSwiperH + 'rpx;'" @change="swiperChange">
				<swiper-item v-for="(items, index) in newNavList" :key="index">
					<view class="nav-list flex flex-wrap">
						<view v-for="(item, index2) in items" :key="index2"
							class="nav-item m-t-30" @tap="menuJump(item)">
							<view class="flex-col col-center">
								<image class="nav-icon m-b-15" :src="item.image"></image>
								<view class="name xs">{{item.name}}</view>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
			<view class="dots" v-if="newNavList.length > 1">
				<view v-for="(item, index) in newNavList" :key="index"
					:class="'dot ' + (index == currentSwiper ? 'active' : '')"></view>
			</view>
		</view> -->
		<!-- <view class="group m-t-20 m-b-20" v-if="homeData.eliteteam && homeData.eliteteam.length>0">
			<u-section :title="$t('index.eliteTeam')" :sub-title="$t('index.viewMore')" :show-line="false" font-size="32" @click="ClickMore"></u-section>
			<view class="cont">
				<view class="qiandao">
					<scroll-view class="scroll-view" scroll-x="true" @scroll="scroll" >
						<view class="recordScroll" :style="{width: (180*homeData.eliteteam.length + 20*(homeData.eliteteam.length-1)) + 'rpx'}">
							<view class="image-text flex-col row-between" v-for="(item,index) in homeData.eliteteam" :key="index">
								<view class="image">
									<image :src="item.image" mode="widthFix"/>
								</view>
								<text class="name m-t-20 m-b-10">{{item.title}}</text>
								<text class="position">{{item.position}}</text>
								<view :class="['tips',item.cname.name==='本周新星'?'type1':'type2']">{{item.cname.name === '本周新星' ? $t('index.thisWeekStar') : item.cname.name}}</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</view> -->
		<!-- 排行榜banner图 -->
		<!-- <router-link to="/bundle_c/pages/xiaoshou_yeji/xiaoshou_yeji">
			<view style="width: 100%;" class="p-l-20 p-r-20 m-b-20">
				<image :src="homeData.daren_sale_img" mode="widthFix" style="width: 100%;display: block;"></image>
			</view>
		</router-link> -->
		<!-- 商品列表 -->
		<goods-column ref="mescrollItem" :autoGetData="true" v-if="is_open_shop==1"></goods-column>
		<!-- 版权信息 -->
		<!-- <view style="width: 100%;">
			<view style="width: 100%;" v-for="(item,index) in homeData.index_img.list"  :key="index">
				<image :src="item" style="width: 100%;" mode="widthFix"></image>
			</view>
		</view> -->

		<!-- <view class="text-center m-t-50 p-b-50 xs muted" v-if="appConfig.copyright_info">
			<view>{{ appConfig.copyright_info }}</view>
			<view>{{ appConfig.icp_number }}</view>
		</view> -->

		<u-popup mode="center" v-model="show" border-radius="14" :mask-close-able="false" z-index="999" :closeable="false" @open="open" @close="close">
			<view class="popContent" style="background-color: #fff;padding: 48rpx;width:666rpx;">
				<view class="popTitle m-b-30" style="font-size: 30rpx;
				color: #333333;
				font-weight: 600;
				text-align: center;">{{ $t('index.bindSuperior') }}</view>
				<view class="input m-b-30">
					<u-input :customStyle="{color:'#333333'}" v-model="invite_code" type="number" class="input" :border="true" height="76" border-color="#999999" :placeholder="$t('index.enterSuperiorInviteCode')"/>
				</view>
				<u-button class="binding" type="error" hover-class="none" @click="ClickBinding" style="height: 80rpx;
					line-height: 80rpx;
					">{{ $t('index.confirmBinding') }}</u-button>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		mapGetters,
		mapActions,
		mapMutations
	} from 'vuex'
	import {
		getMenu,
		getHome,
		getHotGoods,
		getLevelOneList
	} from "@/api/store"
	import {
		pullUser
	} from "@/api/app"
	import {
		getRect,
		setTabbar,
		arraySlice,
		menuJump
	} from '@/utils/tools'
	import Cache from '@/utils/cache'
	import {
		bindSuperior
	} from '@/api/user'
import StartupGuide from '@/components/startup-guide/startup-guide'
import pageTitleMixin from '@/mixins/page-title-mixin'
	export default {
		components: {
			StartupGuide
		},
		mixins: [pageTitleMixin],
		data() {
			return {
				offPrompt: true,
				active: 0,
				navBg: 0,
				goodsList: [],
				homeData: {},
				enable: true,
				showCateList: [],
				class_color:'#fff',
				newNavList: [],
				navSwiperH:'',
				invite_code:'',
				isClick:true,//点击事件除重
				show:false,
				is_open_shop:0,//商品列表状态开关,
				appVersion: '1.0.1', // Update this when releasing new versions
			}
		},
		async onLoad(options) {
			await this.getMenuFun()
			await this.getHomeFun()
		},
		onShow() {
			this.getUser().then(res => {
				if(Cache.get('TOKEN')){
					if(this.userInfo.first_leader == null || this.userInfo.first_leader == ''){
						this.show = true
					}else{
						this.show = false
					}
				}else{
					this.show = false
				}
			})
		},
		methods: {
			...mapActions(['getCartNum', 'getUser']),
			...mapMutations(['login']),

			/**
			 * Handle startup guide close event
			 */
			onStartupGuideClose() {
				console.log('Startup guide closed');
				// You can add any additional logic here when the guide is closed
			},

			// 获取菜单
			async getMenuFun() {
				const {
					code,
					data
				} = await getMenu({
					type: 1
				});
				if (code == 1) {
					if (data.length <= 5) {
						this.navSwiperH = 200
					} else {
						this.navSwiperH = 340
					}
					this.newNavList = arraySlice(data)
				}
			},
			// 获取首页数据
			async getHomeFun() {
				const {
					code,
					data
				} = await getHome({ city_id: this.cityInfo.id });
				if (code == 1) {
					this.homeData = data
					uni.setStorageSync('is_jump_shipin', this.homeData.is_jump_shipin);
					uni.setStorageSync('is_open_shop', this.homeData.is_open_shop);
					this.is_open_shop = this.homeData.is_open_shop
				}
			},
			menuJump(item){
				if(item.name == 'VIP'){
					this.$emit('fartherVip')
					return;
				}
				if (!item.link) {
					let id = ''
					let translatedName = item.name

					// Handle translation for specific menu items
					switch (item.name){
						case '开通橱窗':
							id = '10'
							translatedName = this.$t('index.openShowcase')
							break;
						case '橱窗选品':
							id = '11'
							translatedName = this.$t('index.showcaseSelection')
							break;
						case '绑定橱窗':
							id = '12'
							translatedName = this.$t('index.bindShowcase')
							break;
						case '升级推客':
							id = '13'
							translatedName = this.$t('index.upgradePromoter')
							break;
					}

					this.$Router.push({
						path: '/bundle_c/pages/jiaocheng/jiaocheng_chu',
						query: {
							name: translatedName,
							id: id,
						}
					})
					//this.$toast({title: this.$t('index.notYetOpen')})
					return;
				}
				menuJump(item)
			},
			//绑定
			ClickBinding(){
				if(!this.isClick) return false
				this.isClick = false
				if(this.invite_code){
					bindSuperior({code: this.invite_code}).then((res)=>{
						if(res.code == 1){
							this.invite_code = '' //重置 //重置
							this.$toast({ title: res.msg, icon:'success'})
							this.show = false
							this.getUser()
						}else{
							this.$toast({ title: res.msg})
						}
					})
					this.isClick = true
				}else{
					this.$toast({title: this.$t('index.pleaseEnterSuperiorID')})
					this.isClick = true
				}
			},
			open(){
				uni.hideTabBar()
			},
			close(){
				uni.showTabBar()
			},
			scroll(e){

			},
			ClickMore(){
				this.$Router.push({
					path: '/bundle_c/pages/jingying_team/jingying_team',
					query:{
						list: this.homeData.eliteteam
					}
				})
			},
		},
		computed: {
			...mapGetters(["userInfo",'sysInfo', 'inviteCode', 'appConfig', 'cityInfo', 'currentLanguage']),
		},
	}
</script>

<style lang="scss">
.popContent{
    .binding{
        background-color: $-color-primary;
		color: #010101;
    }
}
	page {
		padding: 0;
	}
	.u-navbar{
		padding: 0;
	}
	::v-deep .u-navbar{
		padding: 0 !important;
	}
	.index {
		padding: 0 24rpx;
		min-height: calc(100vh - var(--window-bottom));

		.u-navbar {

			::v-deep .u-search {
				padding: 0 30rpx;
			}
		}

		.nav {
			position: relative;
			border-radius:20rpx;

			.nav-item {
				width: 25%;

				.nav-icon {
					width: 100rpx;
					height: 100rpx;
				}
			}

			.dots {
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				bottom: 20rpx;
				display: flex;

				.dot {
					width: 10rpx;
					height: 6rpx;
					border-radius: 6rpx;
					margin-right: 10rpx;
					background-color: rgba(255, 44, 60, 0.4);

					&.active {
						width: 20rpx;
						background-color: $-color-primary;
					}
				}

			}

		}
		.information {
			height: 76rpx;
			box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.06);
			border-radius: 20rpx;

			.news {
				position: relative;

				.shade {
					position: absolute;
					width: 100%;
					height: 100%;
					z-index: 100;
				}
			}

			.icon-toutiao {
				width: 114rpx;
				height: 34rpx;
			}

			.gap-line {
				height: 28rpx;
				width: 1px;
				background-color: #DCDDDC;
				margin: 0 30rpx;
			}
			.text-swiper{
				color: #0292F5;
			}
		}
		.group{
			padding: 24rpx;
			background-color: #fff;
			border-radius: 18rpx;

			&:last-child{
				margin-bottom:0;
			}

			.top{
				margin-bottom: 24rpx;

				.title{
					font-size: 32rpx;
					color: #000;
					font-weight: 700;
				}
				.u-btn {
					background-color: #f0f0f0;
					width: 100upx;
					height: 50upx;
					font-size: 24rpx;

					&::after{
						border: none;
					}
				}
			}
			.cont{
				.qiandao{
					width: 100%;
					margin-top: 25rpx;

					.scroll-view{

					}

					.recordScroll{
						height: 350rpx;

						.image-text{
							width: 180rpx;
							height: 100%;
							margin-right: 20rpx;
							float: left;
							text-align: center;
							position: relative;

							&:last-child{
								margin-right: 0;
							}
							.image{
								width: 100%;
								height: 82%;
								border-radius: 12rpx;
								font-size: 26rpx;
								overflow: hidden;
								position: relative;
								background-color: #000;

								image{
									width: 100%;
									height: 100%;
									position: absolute;
									top:50%;
									left: 50%;
									transform: translate(-50%,-50%);
								}
							}
							.name{
								color: #333;
								font-size: 30rpx;
							}
							.position{
								color: #989896;
								font-size: 24rpx;
							}

							.tips{
								position: absolute;
								top:8rpx;
								right: 8rpx;
								border-radius: 50rpx;
								padding: 0 10rpx;
								height: 30rpx;
								line-height: 30rpx;
								color: #fff;
								font-size: 16rpx;

								&.type1{
									background: linear-gradient(to right, #FA986E 0%, #F97A4C 100%);
								}
								&.type2{
									background: linear-gradient(to right, #E48DE2 0%, #C259E5 100%);
								}
							}
						}
					}


				}
			}
		}
	}
</style>
