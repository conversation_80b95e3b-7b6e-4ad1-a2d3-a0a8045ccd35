<template>

	<view class="login" @click="showAccountDropdown = false">
		<!-- #ifdef MP-WEIXIN -->
		<view class="mpwx-login flex-col col-center row-center">
			<view class="avatar">
				<open-data type="userAvatarUrl"></open-data>
			</view>
			<view class="user-name mt20">
				<open-data type="userNickName"></open-data>
			</view>
			<!-- <view class="title xxl m-t-20 bold">微信授权登录</view> -->


			<button size="lg" class="white br60 flex row-center btn" open-type="getPhoneNumber"
				@getphonenumber="getPhoneNumber">{{ $t('login.phoneNumberLogin') }}</button>


			<!-- <button size="lg" class="white br60 flex row-center btn" @click="mnpLoginFun">
				<image class="m-r-10 image" src="/bundle_c/images/icon_wechat.png"></image>
				<text>微信一键授权</text>
			</button> -->
			<view class="muted m-t-20">{{ $t('login.moreOperationsAfterLogin') }}</view>
		</view>
		<!--  #endif -->
		<!-- #ifdef H5 || APP-PLUS -->
		<view class="acount-login">
			<image class="logo" :src="appConfig.shop_login_logo" mode="heightFix"></image>
			<view v-if="loginType == 0">
				<view class="input-item">
					<view class="input-label flex normal"><text class="required">*</text>{{ $t('login.phoneNumber') }}</view>
					<view class="input-content">
						<view class="input">
							<u-input v-model="mobile" style="width: 100%;" :custom-style="{
								'height': '88rpx'
							}" :placeholder="$t('login.enterAccountOrPhoneNumber')" placeholder-style="color: #90BAEB;" :input-border="false"
							@focus="showAccountDropdown = true"
							@input="showAccountDropdown = true"
							@blur="hideDropdown" />
						</view>
						<!-- 历史账号下拉框 -->
						<view class="account-dropdown" v-if="showAccountDropdown && filteredAccounts.length > 0" @click.stop>
							<scroll-view scroll-y style="max-height: 600rpx;">
								<view
									v-for="(account, index) in filteredAccounts"
									:key="index"
									class="account-item"
								>
									<view class="account-content" @tap.stop="selectAccount(account)">
										<text>{{ account.username }}</text>
										<view class="password-dot">········</view>
									</view>
									<view class="delete-icon" @tap.stop="deleteAccount(account)">
										<u-icon name="close-circle-fill" color="#ff4757" size="32"></u-icon>
									</view>
								</view>
							</scroll-view>
						</view>
					</view>
				</view>
				<view class="input-item">
					<view class="input-label flex normal"><text class="required">*</text>{{ $t('login.password') }}</view>
					<view class="input-content">
						<view class="input flex">
							<u-input v-model="password" :custom-style="{
								'height': '88rpx'
							}" type="password" :password-icon="true" :placeholder="$t('login.enterPassword')" placeholder-style="color: #90BAEB;"
								:input-border="false" />
							<!-- <navigator style="width: 132rpx;border-left: 1rpx solid #90BAEB;color: #90BAEB;margin-left: 20rpx;padding-left: 10rpx;"
								url="/bundle_c/pages/forget_pwd/forget_pwd" hover-class="none">{{ $t('login.forgotPassword') }}</navigator> -->
						</view>
					</view>
				</view>
			</view>
			<view v-if="loginType == 1">
				<view class="input-item">
					<view class="input-label flex normal"><text class="required">*</text>{{ $t('login.phoneNumber') }}</view>
					<view class="input-content">
						<view class="input">
							<u-input v-model="telephone" style="width: 100%;" :custom-style="{
								'height': '88rpx'
							}" :placeholder="$t('login.phoneNumber')" placeholder-style="color: #90BAEB;" :input-border="false" />
						</view>
					</view>
				</view>
				<view class="input-item">
					<view class="input-label flex normal"><text class="required">*</text>{{ $t('login.graphicCaptcha') }}</view>
					<view class="captcha-container">
						<image class="captcha-image" :src="captchaImage" @tap="refreshCaptcha"></image>
						<view class="refresh-btn" @tap="refreshCaptcha">{{ $t('login.refreshCaptcha') }}</view>
					</view>
					<view class="input-content">
						<u-input v-model="graphicCode" :custom-style="{
							'height': '88rpx'
						}" :placeholder="$t('login.enterGraphicCaptcha')" placeholder-style="color: #90BAEB;" :input-border="false" />
					</view>
				</view>
				<view class="input-item">
					<view class="input-label flex normal"><text class="required">*</text>{{ $t('login.verificationCode') }}</view>
					<view class="input-content">
						<view class="input flex">
							<u-input v-model="smsCode" :custom-style="{
								'height': '88rpx'
							}" :placeholder="$t('login.enterVerificationCode')" placeholder-style="color: #90BAEB;" :input-border="false" />
							<view class="flex">
								<view class="sms-btn primary flex row-center" @click="$sendSms">
									<!-- 获取验证码 -->
									<u-verification-code unique-key="login" ref="uCode" @change="codeChange">
									</u-verification-code>
									<view class="xs">{{ codeTips }}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="flex row-between" style="width: 702rpx;">
				<view v-if="loginType == 0" class="remember-me flex row-start">
					<u-checkbox v-model="rememberMe" shape="circle" style="margin-right: 10rpx;">
						<text class="sm" style="color:#5C81AB">{{ $t('login.rememberMe') }}</text>
					</u-checkbox>
				</view>
				<!-- <view class="lighter" @click="changeLoginType">{{ loginType == 0 ? $t('login.smsLogin') : $t('login.accountLogin') }}</view> -->
				<navigator class="lighter" url="/bundle_c/pages/register/register" hover-class="none">{{ $t('login.registerAccount') }}
				</navigator>
			</view>
			<!-- <view v-if="loginType == 0" class="remember-me flex row-start" style="width: 702rpx; margin-top: 20rpx;">
				<u-checkbox v-model="rememberMe" shape="circle" style="margin-right: 10rpx;">
					<text class="sm" style="color:#5C81AB">{{ $t('login.rememberMe') }}</text>
				</u-checkbox>
			</view> -->
			<view class="login-wrap">
				<view class="sm flex row-center">
					<u-checkbox v-model="isAgreement" shape="circle">
						<div class="flex">
							{{ $t('login.agreementPrefix') }}
							<router-link to="/bundle/pages/server_explan/server_explan?type=0" style="margin: 0 5rpx;">
								<view class="primary">{{ $t('login.serviceAgreement') }}</view>
							</router-link>
							{{ $t('login.and') }}
							<router-link to="/bundle/pages/server_explan/server_explan?type=1" style="margin: 0 5rpx;">
								<view class="primary">{{ $t('login.privacyAgreement') }}</view>
							</router-link>
						</div>
					</u-checkbox>
				</view>
				<button size="lg" class="login-btn" type="primary" @click="loginFun">{{ $t('login.login') }}</button>
			</view>

			<!--  #endif -->
		</view>
	</view>
</template>

<script>
import {
	mapMutations,
	mapActions,
	mapGetters
} from 'vuex'
import {
	accountLogin,
	sendSms,
	smsCodeLogin,
	opLogin,
	authLogin,
	getCaptcha
} from '@/api/app';
import {
	bindSuperior, getWxMnpMobile, getMobileByMnp
} from '@/api/user'
import wechath5 from '@/utils/wechath5'
import {
	isWeixinClient,
	currentPage,
	client,
	trottle
} from '@/utils/tools'
import {
	SMSType
} from "@/utils/type"
import Cache from "@/utils/cache"
import {
	BACK_URL,
	INVITE_CODE,
	REMEMBER_LOGIN,
	REMEMBER_USERNAME,
	REMEMBER_PASSWORD,
	SAVED_ACCOUNTS
} from '@/config/cachekey'
import {
	getWxCode,
	getUserProfile
} from '@/utils/login'
import pageTitleMixin from '@/mixins/page-title-mixin'
const loginType = {
	ACCOUNT_LOGIN: 0,
	SMS_CODE_LOGIN: 1,
}
export default {
	mixins: [pageTitleMixin],
	data() {
		return {
			isAgreement: false,
			password: '',
			mobile: '',
			code: '',
			isWeixin: '',
			loginType: 0,
			smsCode: '',
			codeTips: '',
			telephone: "",
			text: '',
			phoneNew: '',
			rememberMe: false,
			showAccountDropdown: false, // 控制历史账号下拉框的显示
			savedAccounts: [], // 保存的历史账号列表
			graphicCode: '', // 图形验证码输入
			captchaImage: '', // 图形验证码图片
			uniqid: '' // 图形验证码唯一ID
		};
	},

	async onLoad() {
		//this.mnpLoginFun = trottle(this.mnpLoginFun)
		this.appWxLogin = trottle(this.appWxLogin)
		this.getCodeUrl = trottle(this.getCodeUrl)

		// 加载保存的账号列表
		this.loadSavedAccounts();

		// Load saved credentials if remember me was checked
		if (Cache.get(REMEMBER_LOGIN)) {
			this.rememberMe = true;
			this.mobile = Cache.get(REMEMBER_USERNAME) || '';
			this.password = Cache.get(REMEMBER_PASSWORD) || '';
		}

		// 获取图形验证码
		// this.refreshCaptcha();

		// H5微信登录
		// #ifdef H5
		this.oaAutoLogin()
		// #endif
	},
	onUnload() {

	},
	methods: {
		...mapMutations(['login']),
		...mapActions(['getUser']),

		async getPhoneNumber(e) {
			const wxCode = await getWxCode()
			const {
				encryptedData,
				iv
			} = e.detail;
			const params = {
				code: wxCode,
				encrypted_data: encryptedData,
				iv
			}
			if (encryptedData) {
				const {
					data,
					code,
					msg
				} = await getMobileByMnp(params)
				if (code == 1) {
					this.phoneNew = data.mobile
					this.mnpLoginFun()
				}
			}
		},
		codeChange(tip) {
			this.codeTips = tip
		},
		getCodeUrl() {
			wechath5.getWxUrl()
		},
		// 公众号微信登录
		async oaLogin(code) {
			const data = await wechath5.authLogin(code)
			this.loginHandle(data)
		},
		oaAutoLogin() {
			// H5微信登录
			const {
				code
			} = this.$Route.query
			this.isWeixin = isWeixinClient()
			if (this.isLogin) {
				// 已经登录 => 首页
				uni.switchTab({
					url: '/pages/index/index'
				})
				return
			}
			if (code) {
				// 带有code => 登录
				return this.oaLogin(code)
			}
			if (this.isWeixin && this.isOaWxAutoLogin) {
				// 开启自动授权登录
				this.getCodeUrl()
			}
		},
		// 小程序登录
		async mnpLoginFun() {
			//if(!this.isAgreement) return this.$toast({ title: this.$t('login.pleaseAgreeToTerms') })
			/* const {
				userInfo: {
					avatarUrl,
					nickName,
					gender
				}
			} = getUserProfile() */
			uni.showLoading({
				title: '登录中...',
				mask: true
			});
			const wxCode = await getWxCode()
			authLogin({
				code: wxCode,
				//nickname: avatarUrl,
				//headimgurl: avatarUrl,
				mobile: this.phoneNew,
			}).then(res => {
				if (res.code == 1) {
					this.loginHandle(res.data)
				} else {
					this.$toast({
						title: res.msg
					})
				}
			})
			/* const {
				code,
				data,
				msg
			} = authLogin({
				code: wxCode,
				//nickname: avatarUrl,
				//headimgurl: avatarUrl,
				mobile: this.phoneNew,
			})
			console.log('00');
			if (code == 1) {
				console.log(1);
				this.loginHandle(data)
			} else {
				this.$toast({
					title: msg
				})
			} */
		},
		// 加载保存的账号列表
		loadSavedAccounts() {
			const savedAccounts = Cache.get(SAVED_ACCOUNTS);
			if (savedAccounts) {
				try {
					this.savedAccounts = JSON.parse(savedAccounts);
				} catch (e) {
					console.error('Failed to parse saved accounts:', e);
					this.savedAccounts = [];
				}
			}
		},

		// 保存账号到历史记录
		saveAccountToHistory(username, password) {
			// 检查账号是否已存在
			const existingIndex = this.savedAccounts.findIndex(account => account.username === username);

			// 如果账号已存在，更新密码
			if (existingIndex !== -1) {
				this.savedAccounts[existingIndex].password = password;
			} else {
				// 否则添加新账号
				this.savedAccounts.push({
					username,
					password
				});
			}

			// 限制保存的账号数量，最多保存100个
			if (this.savedAccounts.length > 100) {
				this.savedAccounts = this.savedAccounts.slice(-100);
			}

			// 保存到缓存
			Cache.set(SAVED_ACCOUNTS, JSON.stringify(this.savedAccounts));
		},

		// 隐藏下拉框
		hideDropdown() {
			// 使用全局 setTimeout 而不是 Vue 实例方法
			setTimeout(() => {
				this.showAccountDropdown = false;
			}, 300);
		},

		// 选择历史账号
		selectAccount(account) {
			console.log('选择账号:', account);
			// 使用 nextTick 确保在 DOM 更新后再设置值
			this.$nextTick(() => {
				this.mobile = account.username;
				this.password = account.password;
				// 延迟关闭下拉框，确保点击事件完全处理
				setTimeout(() => {
					this.showAccountDropdown = false;
				}, 50);
			});
		},

		// 删除历史账号
		deleteAccount(account) {
			uni.showModal({
				title: this.$t('login.confirmDelete'),
				content: `${this.$t('login.deleteAccountConfirm')} "${account.username}"？`,
				success: (res) => {
					if (res.confirm) {
						// 从原始数组中找到并删除该账号
						const originalIndex = this.savedAccounts.findIndex(acc => acc.username === account.username);
						if (originalIndex !== -1) {
							this.savedAccounts.splice(originalIndex, 1);
							// 更新缓存
							Cache.set(SAVED_ACCOUNTS, JSON.stringify(this.savedAccounts));

							// 如果删除后没有账号了，隐藏下拉框
							if (this.filteredAccounts.length === 0) {
								this.showAccountDropdown = false;
							}
						}
					}
				}
			});
		},

		// 账号登录
		async loginFun() {
			if (!this.isAgreement) return this.$toast({ title: this.$t('login.pleaseAgreeToTerms') })
			const {
				mobile,
				password,
				telephone,
				smsCode
			} = this
			if (this.loginType == 0) {
				if (!mobile) {
					this.$toast({
						title: this.$t('login.pleaseEnterAccount')
					});
					return;
				}
				if (!password) {
					this.$toast({
						title: this.$t('login.pleaseEnterPassword')
					});
					return;
				}
				const {
					code,
					data
				} = await accountLogin({
					mobile,
					password,
					client: client
				})
				if (code == 1) {
					// Save credentials if remember me is checked
					if (this.rememberMe) {
						Cache.set(REMEMBER_LOGIN, true);
						Cache.set(REMEMBER_USERNAME, mobile);
						Cache.set(REMEMBER_PASSWORD, password);

						// 保存账号到历史记录
						this.saveAccountToHistory(mobile, password);
					} else {
						// Clear saved credentials if remember me is unchecked
						Cache.remove(REMEMBER_LOGIN);
						Cache.remove(REMEMBER_USERNAME);
						Cache.remove(REMEMBER_PASSWORD);
					}
					this.loginHandle(data)
				}
			} else {
				if (!telephone) {
					this.$toast({
						title: this.$t('login.pleaseEnterPhoneNumber')
					});
					return;
				}
				if (!smsCode) {
					this.$toast({
						title: this.$t('login.pleaseEnterVerificationCode')
					});
					return;
				}
				smsCodeLogin({
					mobile: telephone,
					code: smsCode,
					graphic_code: this.graphicCode,
					uniqid: this.uniqid
				}).then(res => {
					if (res.code == 1) {
						this.loginHandle(res.data)
					} else {
						// 验证码错误时刷新
						this.refreshCaptcha();
					}
				})
			}
		},
		// 登录结果处理
		async loginHandle(data) {
			//console.log(data);
			this.login(data)
			uni.hideLoading()
			// 绑定邀请码
			const inviteCode = Cache.get(INVITE_CODE)
			if (inviteCode) {
				bindSuperior({
					code: inviteCode
				})
				Cache.remove(INVITE_CODE)
			}

			if (getCurrentPages().length > 1) {
				uni.navigateBack({
					success() {
						const {
							onLoad,
							options
						} = currentPage()
						onLoad && onLoad(options)
					}
				})
			} else if (Cache.get(BACK_URL)) {
				this.$Router.replace(Cache.get(BACK_URL))
			} else {
				this.$Router.pushTab('/pages/index/index')
			}
			Cache.remove(BACK_URL)
		},
		changeLoginType() {
			if (this.loginType == loginType.ACCOUNT_LOGIN) {
				this.loginType = loginType.SMS_CODE_LOGIN
			} else if (this.loginType == loginType.SMS_CODE_LOGIN) {
				this.loginType = loginType.ACCOUNT_LOGIN
			}
		},
		// 刷新图形验证码
		refreshCaptcha() {
			getCaptcha().then(res => {
				if (res.code == 1) {
					this.captchaImage = res.data.image;
					this.uniqid = res.data.uniqid;
				}
			});
		},

		// 发送验证码
		$sendSms() {
			if (!this.$refs.uCode.canGetCode) return
			if (!this.telephone) {
				this.$toast({
					title: this.$t('login.pleaseEnterPhoneNumber')
				})
				return;
			}
			if (!this.graphicCode) {
				this.$toast({
					title: this.$t('login.enterGraphicCaptcha')
				})
				return;
			}

			sendSms({
				mobile: this.telephone,
				key: SMSType.LOGIN,
				graphic_code: this.graphicCode,
				uniqid: this.uniqid
			}).then(res => {
				if (res.code == 1) {
					this.$refs.uCode.start();
					this.$toast({
						title: res.msg
					});
				} else {
					// 验证码错误时刷新
					this.refreshCaptcha();
				}
			})
		},
		// app微信登录
		async appWxLogin() {
			if (!this.isAgreement) return this.$toast({ title: this.$t('login.pleaseAgreeToTerms') })
			uni.login({
				provider: 'weixin',
				success: (res) => {
					uni.showLoading({
						title: this.$t('login.loggingIn'),
						mask: true
					});
					const {
						openid,
						access_token,
					} = res.authResult
					opLogin({
						openid,
						access_token
					}).then(res => {
						uni.hideLoading()
						if (res.code == 1) {
							this.loginHandle(res.data)
						}
					}).catch(() => {
						uni.hideLoading()
					})
				}
			})


		}
	},
	computed: {
		...mapGetters(['appConfig', 'currentLanguage']),
		isOaWxAutoLogin() {
			return this.appConfig.wechat_h5
		},
		// 根据输入内容筛选账号
		filteredAccounts() {
			if (!this.mobile) {
				// 如果输入框为空，显示所有保存的账号
				return this.savedAccounts;
			}

			// 根据输入内容筛选账号
			return this.savedAccounts.filter(account =>
				account.username.toLowerCase().includes(this.mobile.toLowerCase())
			);
		}
	}
};
</script>
<style lang="scss">
page {
	background-color: #fff;
	text-align: center;
	padding: 0;

	.login {
		display: flex;
		flex-direction: column;

		// 账号下拉框样式
		.account-dropdown {
			margin-top: 10rpx;
			position: absolute;
			top: 100%;
			left: 0;
			width: 100%;
			background-color: #fff;
			border: 1px solid #ddd;
			border-radius: 8rpx;
			box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
			z-index: 999; // 提高z-index确保在最上层

			// 滚动视图样式
			::-webkit-scrollbar {
				width: 6rpx;
				background-color: #f5f5f5;
			}

			::-webkit-scrollbar-thumb {
				background-color: #ccc;
				border-radius: 6rpx;
			}

			.account-item {
				padding: 20rpx 24rpx;
				text-align: left;
				border-bottom: 1px solid #f5f5f5;
				display: flex;
				justify-content: space-between;
				align-items: center;
				position: relative;
				z-index: 101; // 确保在其他元素之上

				.account-content {
					flex: 1;
					display: flex;
					justify-content: space-between;
					align-items: center;
					cursor: pointer;
					padding-right: 20rpx;

					.password-dot {
						color: #333;
						font-size: 24rpx;
					}

					&:hover, &:active {
						background-color: #f0f0f0;
					}
				}

				.delete-icon {
					flex-shrink: 0;
					padding: 10rpx;
					cursor: pointer;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 50%;
					transition: background-color 0.2s;

					&:hover {
						background-color: #ffe6e6;
					}

					&:active {
						background-color: #ffcccc;
					}
				}

				&:last-child {
					border-bottom: none;
				}

				&:hover {
					background-color: #f9f9f9;
				}
			}
		}

		.input-content {
			position: relative;
		}

		.captcha-container {
			display: flex;
			align-items: center;
			margin-top: 10rpx;
			margin-bottom: 10rpx;

			.captcha-image {
				width: 200rpx;
				height: 80rpx;
				background-color: #f5f5f5;
				border-radius: 8rpx;
			}

			.refresh-btn {
				margin-left: 20rpx;
				color: $-color-primary;
				font-size: 24rpx;
			}
		}

		.mpwx-login {
			min-height: calc(100vh - 90px);
			flex: 1;

			.avatar {
				display: inline-block;
				width: 120rpx;
				height: 120rpx;
				border-radius: 50%;
				border: 1px solid #eee;
				overflow: hidden;
			}

			.user-name {
				height: 40rpx;
			}

			.image {
				width: 50rpx;
				height: 50rpx;
			}

			.btn {
				background-color: #09BB07;
				width: 580rpx;
				margin: 80rpx auto 0;
			}
		}


		.acount-login {
			padding-top: 100rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			box-sizing: border-box;
			min-height: 0;

			.logo {

				height: 80rpx;
				margin-bottom: 100rpx;
			}

			.input-item {
				margin-bottom: 32rpx;
				width: 702rpx;

				.input-label {
					flex: none;
					margin-bottom: 24rpx;
					color: #fff;
					font-size: 26rpx;

					.required {
						color: #F93E3E;
						margin-right: 5rpx;
						vertical-align: middle;
					}

					&:before {
						content: '';
						display: inline-block;
						height: 20rpx;
						width: 2rpx;
						margin-right: 10rpx;
						background-color: #fff;
					}
				}
			}

			.input {
				background-image: url('@/static/images/<EMAIL>');
				background-size: 100% 100%;
				background-repeat: no-repeat;
				padding: 0 24rpx;
			}

			.sms-btn {
				border: 1px solid $-color-primary;
				width: 176rpx;
				height: 60rpx;
				box-sizing: border-box;
			}

			.wx-login {
				margin-top: 60rpx;
				margin-bottom: 60rpx;

				.image {
					margin-top: 40rpx;
					width: 80rpx;
					height: 80rpx;
				}
			}
		}

		.login-wrap {
			position: fixed;
			display: flex;
			flex-direction: column;
			align-items: center;
			bottom: 0;
			width: 100%;
			background-color: #011750;
			padding-top: 18rpx;

			.login-btn {
				border-radius: 0rpx;
				width: 702rpx;
				margin: 20rpx 0 50rpx;
				color: #1D1D3B;
				font-size: 34rpx;
			}
		}
	}
}
</style>
