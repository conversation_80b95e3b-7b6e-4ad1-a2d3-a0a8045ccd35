<template>
  <view class="demo-container">
    <view class="title">矩形框组件演示</view>
    
    <!-- 基本使用 -->
    <view class="demo-section">
      <view class="section-title">基本使用</view>
      <rect-box>
        <view class="content-box">基本矩形框</view>
      </rect-box>
    </view>
    
    <!-- 自定义背景色和边框 -->
    <view class="demo-section">
      <view class="section-title">自定义背景色和边框</view>
      <rect-box 
        backgroundColor="rgba(0, 146, 245, 0.1)" 
        borderColor="#0292F5"
        padding="30rpx"
      >
        <view class="content-box">自定义背景色和边框</view>
      </rect-box>
    </view>
    
    <!-- 自定义尺寸 -->
    <view class="demo-section">
      <view class="section-title">自定义尺寸</view>
      <rect-box 
        width="600rpx"
        height="200rpx"
        backgroundColor="#f8f8f8"
        borderColor="#ddd"
      >
        <view class="content-box">自定义宽高</view>
      </rect-box>
    </view>
    
    <!-- 自定义圆角 -->
    <view class="demo-section">
      <view class="section-title">自定义圆角</view>
      <rect-box 
        borderRadius="20"
        backgroundColor="#f0f9ff"
        borderColor="#0292F5"
      >
        <view class="content-box">自定义圆角</view>
      </rect-box>
    </view>
    
    <!-- 自定义角标大小 -->
    <view class="demo-section">
      <view class="section-title">自定义角标大小</view>
      <rect-box 
        cornerSize="50rpx"
        backgroundColor="#f5f5f5"
        borderColor="#999"
      >
        <view class="content-box">自定义角标大小</view>
      </rect-box>
    </view>
    
    <!-- 复杂内容 -->
    <view class="demo-section">
      <view class="section-title">复杂内容</view>
      <rect-box 
        backgroundColor="#fff"
        borderColor="#0292F5"
        padding="30rpx"
      >
        <view class="complex-content">
          <view class="complex-title">标题文本</view>
          <view class="complex-desc">这是一段描述文本，展示了矩形框组件的灵活性。您可以在其中放置任何内容，包括文本、图片、表单等。</view>
          <view class="complex-footer">
            <view class="btn">按钮1</view>
            <view class="btn primary">按钮2</view>
          </view>
        </view>
      </rect-box>
    </view>
  </view>
</template>

<script>
import RectBox from '@/components/common/rect-box.vue';

export default {
  components: {
    RectBox
  }
};
</script>

<style lang="scss">
.demo-container {
  padding: 30rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 40rpx;
    text-align: center;
  }
  
  .demo-section {
    margin-bottom: 50rpx;
    
    .section-title {
      font-size: 30rpx;
      margin-bottom: 20rpx;
      color: #666;
    }
    
    .content-box {
      padding: 20rpx;
      text-align: center;
    }
    
    .complex-content {
      .complex-title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
      }
      
      .complex-desc {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
        margin-bottom: 30rpx;
      }
      
      .complex-footer {
        display: flex;
        justify-content: flex-end;
        
        .btn {
          padding: 10rpx 30rpx;
          background-color: #f5f5f5;
          border-radius: 8rpx;
          margin-left: 20rpx;
          font-size: 28rpx;
          
          &.primary {
            background-color: #0292F5;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
