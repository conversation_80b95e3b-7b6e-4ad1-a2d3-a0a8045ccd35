<template>
	<view class="giveaway">
		<view class="acount-login">
			<view class="input-item">
				<view class="input-label flex normal">{{ $t('subsidy.subsidyAmount') }}</view>
				<view class="input-content">
					<view class="input">
						<u-input v-model="amount" style="width: 100%;" :custom-style="{
							'height': '88rpx'
						}" :placeholder="$t('subsidy.enterSubsidyAmount')" placeholder-style="color: #90BAEB;" :input-border="false" />
					</view>
				</view>
			</view>
			<view class="input-item">
				<view class="input-label flex normal">场地照片上传</view>
				<view class="input-content">
					<view class="upload">
						<u-upload ref="uUpload" :show-progress="true" :header="{ token: $store.getters.token }"
							:max-count="8" width="219" height="267" :action="action"
							:upload-text="$t('topup.uploadImage')" @on-success="onSuccess" @on-remove="onRemove" />
					</view>
				</view>
			</view>

			<view class="login-wrap">
				<button size="lg" class="login-btn" type="primary" @click="handleApplySubsidy">{{
					$t('subsidy.applySubsidy') }}</button>
			</view>
		</view>
	</view>
</template>

<script>
import {
	mapMutations,
	mapActions,
	mapGetters
} from 'vuex'
import {
	applySubsidy
} from '@/api/user'
import {
	isWeixinClient,
	currentPage,
	client,
	trottle
} from '@/utils/tools'
import Cache from "@/utils/cache"
import pageTitleMixin from '@/mixins/page-title-mixin'

export default {
	mixins: [pageTitleMixin],
	data() {
		return {
			amount: '',
			action: baseURL + '/api/file/formimage',
			fileList: [],
			submitLoading: false
		};
	},

	onLoad(option) {
		// 页面加载时的逻辑
	},

	methods: {
		onSuccess(e) {
			this.fileList.push(e.data.base_uri)

		},
		onRemove(index) {
			this.fileList.splice(index, 1)
			console.log(index)
		},
		handleApplySubsidy() {
			if (!this.amount) {
				uni.showToast({
					title: this.$t('subsidy.pleaseEnterSubsidyAmount'),
					icon: 'none'
				})
				return
			}
			if (!this.fileList.length || this.fileList.length < 3 || this.fileList.length > 8) {
				uni.showToast({
					title: '请上传3-8张场地照片',
					icon: 'none'
				})
				return
			}

			if (this.submitLoading) {
				return
			}

			this.submitLoading = true

			// 调用申请补贴的API
			applySubsidy({
				amount: this.amount,
				images: this.fileList
			}).then(res => {
				this.submitLoading = false
				if (res.code === 1) {
					this.amount = ''
					uni.showToast({
						title: this.$t('subsidy.applySuccess'),
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			}).catch(err => {
				this.submitLoading = false
				uni.showToast({
					title: this.$t('subsidy.applyFailed'),
					icon: 'none'
				})
			})
		}
	},
	computed: {
		...mapGetters(['currentLanguage'])
	}
};
</script>
<style lang="scss">
page {
	background-color: #fff;
	text-align: center;
	padding: 0;

	.giveaway {
		display: flex;
		flex-direction: column;

		.upload {
			padding: 0;
			margin-left: -5px;
			margin-top: -5px;
			/deep/ .u-list-item {
				background: none;
				background-image: url('@/static/images/<EMAIL>');
				background-size: 100% 100%;
				background-repeat: no-repeat;
			}

			/deep/ .u-add-wrap {
				color: $-color-primary;
			}


		}

		.acount-login {
			padding-top: 100rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			box-sizing: border-box;
			min-height: 0;

			.input-item {
				margin-bottom: 32rpx;
				width: 702rpx;

				.input-label {
					flex: none;
					margin-bottom: 24rpx;
					color: #fff;
					font-size: 26rpx;

					.required {
						color: #F93E3E;
						margin-right: 5rpx;
						vertical-align: middle;
					}

					&:before {
						content: '';
						display: inline-block;
						height: 20rpx;
						width: 2rpx;
						margin-right: 10rpx;
						background-color: #fff;
					}
				}
			}

			.input {
				background-image: url('@/static/images/<EMAIL>');
				background-size: 100% 100%;
				background-repeat: no-repeat;
				padding: 0 24rpx;
			}
		}

		.login-wrap {
			position: fixed;
			display: flex;
			flex-direction: column;
			align-items: center;
			bottom: 0;
			width: 100%;
			background-color: #011750;
			padding-top: 18rpx;

			.login-btn {
				border-radius: 0rpx;
				width: 702rpx;
				margin: 20rpx 0 50rpx;
				color: #1D1D3B;
				font-size: 34rpx;
			}
		}
	}
}
</style>
