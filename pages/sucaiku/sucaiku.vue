<template>
	<view class="sucaiku">
		<u-tabs :list="categoryList" :is-scroll="true" :current="active" @change="changeActive" active-color="#FF4B59"></u-tabs>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @up="upCallback" :up="upOption" :down="{auto: false}" @down="downCallback">
			<view class="main">
				<view class="user">
					<view class="list flex flex-wrap">
						<view class="newsCard" v-for="(item,index) in newsList" :key="index">
							<view class="newsCardBox flex row-left col-top" @click="clickLook(item)">
								<view :class="['newsCardL flex-col row-center col-center', item.video?'v_icon':'']"  
									style="width: 100%;height: 250rpx;position: relative;background-color: #f1f1f1;border-radius: 6rpx;overflow: hidden;" 
									>
									<image :src="item.image" mode="heightFix" style="width: 100%;height: 100%;;display: block;"></image>
									<view class="playBtn" v-if="item.video">
										<u-icon name="play-right-fill" color="#fff" size="36" class="playIcon"></u-icon>
									</view>
									<view class="imgTitle">{{item.title}}</view>
								</view>
								<!-- <view class="newsCardM p-10 flex-col row-left col-top">
									<view class="newsCardMTitle">
										<text>{{item.title}}</text>
									</view>
									<view class="newsCardMTime">
										<text>{{item.create_time}}</text>
									</view>
								</view> -->
							</view>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
		<u-popup v-model="showBigImg" width="90%" mode="center" length="70%" border-radius="12" :closeable="true" close-icon-color="#fff" z-index="9999" close-icon-size="40" :mask-close-able="false">
			<view class="text-center" style="padding:90rpx 30rpx 30rpx 30rpx;background-color: none;">
				<!-- <image :src="bigImgUrl" mode="widthFix" style="width: 100%;display: block;"></image> -->
				<view style="width:100%;height: 500rpx;border-radius: 10rpx;overflow: hidden;">
					<video :src="bigImgUrl" controls object-fit="contain" style="width:100%;height: 100%;"></video>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import { mapActions, mapMutations, mapGetters } from 'vuex'
	import { toast } from "@/utils/tools.js" //轻提示弹框
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import MescrollMoreItemMixin from "@/components/mescroll-uni/mixins/mescroll-more-item.js";
	import {
		getCategoryList,
		getArticleList
	} from '@/api/store';
	let systemInfo = uni.getSystemInfoSync()
	
	export default {
		mixins: [MescrollMixin,MescrollMoreItemMixin], // 使用mixin
		data() {
			return {
				upOption: {
					page: {
						size: 15 // 每页数据的数量,默认10
					},
					onScroll:true,
					noMoreSize: 5, // 配置列表的总数量要大于等于5条才显示'-- END --'的提示
					empty: {
						icon: require('@/bundle_c/static/nodata.png'),
						tip: '暂无数据', // 提示
					}
				},
				active: 0,
				newsList:[],
				categoryList: [],
				type: -1,
				showBigImg: false, //是否显示大图
				bigImgUrl:'', //大图地址
			}
		},
		onLoad() {
			
		},
		created() {
			
		},
		methods: {
			changeActive(e) {
				console.log(e)
				this.active = e;
				this.newsList = [] // 先置空列表,显示加载进度
				this.mescroll.resetUpScroll() // 再刷新列表数据
			},
			async downCallback() {
				this.categoryList = []
				await this.getCategoryListFun();
				this.mescroll.resetUpScroll();
			},
			// 上拉加载
			upCallback(page) {
				const { type, active, categoryList } = this
				getArticleList({
					type: this.type,
					cid: this.active ? categoryList[this.active].id : '',
					page_size:page.size,
					page_no:page.num
				}).then(({
					data
				}) => {
					if (page.num == 1) this.newsList = [];
					let curPageData = data.list;
					let curPageLen = curPageData.length;
					let hasNext = !!data.more;
					this.newsList = this.newsList.concat(curPageData);
					this.mescroll.endSuccess(curPageLen, hasNext);
				}).catch(() => {
					this.mescroll.endErr()
				})
			},
			async getCategoryListFun() {
				const {
					code,
					data
				} = await getCategoryList({
					type: this.type
				})
				if (code == 1) {
					this.categoryList.push({name: '全部'},...data)
				}
			},
			clickLook(item){
				console.log(item)
				this.$Router.push({
					path: '/bundle_c/pages/jiaocheng/jiaocheng',
					query: {
						id: item.id,
						type:this.type,
						video: item.video || '',
						image: item.image,
					},
				})
			},
			//点击查看大图
			clickLookBigImg(item){
				console.log(item)
				this.showBigImg = true
				this.bigImgUrl = item.video
			},
			onPageScroll(res) {
				this.top = res.scrollTop;
			},
		},
	}
</script>

<style lang="scss" scoped>
	page{
	}
	.sucaiku{
		position: relative;
		
		.main{
			padding: 0;
			position: relative;
			z-index: 3;
			
			.user{
				padding: 12rpx 0 0 0;
				border-radius: 24rpx;
				
				.list{
					border-radius: 24rpx;
					padding-top: 10rpx;
					
					.newsCard{width: 50%;
						padding: 0 10rpx;
						
						&:last-child .newsCardBox{
							border-bottom: 0;
						}
						
						.newsCardBox{
							background-color: #fff;
							//padding: 40rpx 0;
							//border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);
							position: relative;
							margin-bottom: 20rpx;
							
							.newsCardL{
								margin-right: 8rpx;
								flex-shrink: 0; /* 防止压缩 */
								position: relative;
								&.v_icon:before{
									content: '';
									width: 100%;
									height: 100%;
									display: block;
									background-color: rgba(0, 0, 0, 0);
									position: absolute;
									top:0;left: 0;
									z-index: 999;
								}
								.playBtn{
									width: 70rpx;
									height: 70rpx;
									background-color: rgba(0, 0, 0, 0.5);
									position: absolute;
									top:50%;
									left: 50%;
									transform: translate(-50%,-50%);
									z-index: 999;
									border-radius: 50%;
								}
								.playIcon{
									position: absolute;
									top:50%;
									left: 50%;
									transform: translate(-50%,-50%);
									z-index: 999;
								}
								.imgTitle{
									width: 100%;
									line-height: 60rpx;
									color: #fff;
									position: absolute;
									bottom:0;
									left: 0;
									z-index: 9992;
									text-align: center;
									background-color: rgba(0, 0, 0, 0.6);
								}
							}
							.newsCardM{
								 flex-grow: 1; /* 如果需要，可以让内容区域填满剩余空间 */
								.newsCardMTitle{
									font-size: 28rpx;
									color: #383637;
									margin-bottom: 20rpx;
									height: 80rpx;
								}
								.newsCardMTime{
									font-size: 26rpx;
									line-height: 32rpx;
									color: #5b5555;
								}
								.newsCardMVip{
									margin-top: 6rpx;
									font-size: 20rpx;
									color: #919191;
									display: flex;
									justify-content: flex-start;
									align-items: center;
									
									image{
										width: 32rpx;
										height: 32rpx;
										display: block;
										margin-right: 8rpx;
									}
								}
							}
							.newsCardR{
								height: 100%;
								margin-left: auto;
								display: flex;
								flex-direction: column;
								justify-content: space-between;
								align-items: flex-end;
								
								.txt{
									color: #333;
									text-align: right;
									
									.ai{
										letter-spacing :3rpx;
									}
									
									&:nth-child(2){
										margin-top: 4rpx;
									}
								}
							}
						}
					}
				}
			}
		}
	}
</style>
