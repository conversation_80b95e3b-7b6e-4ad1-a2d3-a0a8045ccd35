<template>
	<view class="topup">
		<view class="topup-coin">
			<!-- <view class="topup-coin-item flex row-between">
				<view class="topup-coin-item-title">{{ $t('topup.rechargeChain') }}</view>
				<view class="topup-coin-item-content">
					<u-radio-group v-model="chain" shape="circle" @change="handleChainChange">
						<u-radio shape="circle" :active-color="colorConfig.primary" :name="1" activeColor="#04F9FC"
							activeIconColor="#1D1D3B">{{ $t('topup.tronChain') }}</u-radio>
						<u-radio shape="circle" :active-color="colorConfig.primary" :name="2" activeColor="#04F9FC"
							activeIconColor="#1D1D3B">{{ $t('topup.binanceChain') }}</u-radio>
					</u-radio-group>
				</view>
			</view> -->
			<!-- <view class="topup-coin-item flex row-between">
				<view class="topup-coin-item-title">{{ $t('topup.network') }}</view>
				<view class="topup-coin-item-content">
					<u-radio-group v-model="chain_type" shape="circle" @change="handleChainTypeChange">
						<u-radio shape="circle" :active-color="colorConfig.primary" :name="'TRC20'"
							activeColor="#04F9FC" activeIconColor="#1D1D3B">TRC20</u-radio>
						<u-radio shape="circle" :active-color="colorConfig.primary" :name="'ERC20'"
							activeColor="#04F9FC" activeIconColor="#1D1D3B">ERC20</u-radio>
						<u-radio shape="circle" :active-color="colorConfig.primary" :name="'BEP20'"
						activeColor="#04F9FC" activeIconColor="#1D1D3B">BEP20</u-radio>
					</u-radio-group>
				</view>
			</view> -->
			<view class="topup-coin-item address">
				<view class="topup-coin-item-title" @tap="handleAddress"><text style="margin-right: 15rpx;">{{ $t('topup.tronChain') }}TRC20{{ $t('topup.pushaddress') }}</text></view>
				<view class="topup-coin-item flex row-between">
					<view class="topup-coin-item-title" @tap="handleAddress">{{ bochang_address }}</view>
					<view class="topup-coin-item-content">
						<button class="topup-coin-item-button" @tap="copy(bochang_address)">{{ $t('topup.copy') }}</button>
					</view>
				</view>
			</view>
			<view class="topup-coin-item address">
				<view class="topup-coin-item-title" @tap="handleAddress"><text style="margin-right: 15rpx;">{{ $t('topup.binanceChain') }}BEP20{{ $t('topup.pushaddress') }}</text></view>
				<view class="topup-coin-item flex row-between">
					<view class="topup-coin-item-title" @tap="handleAddress">{{ bian_address }}</view>
					<view class="topup-coin-item-content">
						<button class="topup-coin-item-button" @tap="copy(bian_address)">{{ $t('topup.copy') }}</button>
					</view>
				</view>
			</view>
		</view>
		<div class="item-title">{{ $t('topup.rechargeInstructions') }}</div>
		<div class="item-content">
			<view class="item-content-item flex row-between">
				<view class="item-content-item-title">{{ $t('topup.minimumRecharge') }}</view>
				<view class="item-content-item-content">30 USDT</view>
			</view>
			<view class="item-content-item flex row-between">
				<view class="item-content-item-title">{{ $t('topup.arrivalTime') }}</view>
				<view class="item-content-item-content">{{ $t('topup.aboutOneMinute') }}</view>
			</view>
		</div>
		<div class="item-title">{{ $t('topup.rechargeAmount') }}</div>
		<view class="topup-coin">
			<view class="topup-coin-item">
				<!-- <view class="topup-coin-item-title">数量</view> -->
				<view class="topup-coin-item-content flex">
					<u-input v-model="rechange_amount" type="number" :placeholder="$t('topup.enterRechargeAmount')" />
				</view>
			</view>
		</view>
		<div class="item-title">{{ $t('topup.uploadVoucher') }}</div>

		<view class="upload">
			<u-upload ref="uUpload" :show-progress="true" :header="{ token: $store.getters.token }" :max-count="1"
				width="240" height="288" :action="action" :upload-text="$t('topup.uploadImage')" @on-success="onSuccess"
				@on-remove="onRemove" />
		</view>
		<view class="upload-demo" @tap="handleDemo">{{ $t('topup.viewUploadExample') }}</view>
		<view class="button-group">
			<button class="button-group-button" @click="handleSubmit">{{ $t('topup.submit') }}</button>
		</view>
		<view class="demo-wrap" v-if="showDemo">
			<!-- close btn -->
			 <u-icon class="close-btn" @tap="handleDemo" name="close-circle" size="68rpx" color="#fff"></u-icon>
			<u-image class="demo" mode="widthFix" src="/static/images/demo.jpeg" width="600"></u-image>
		</view>

	</view>
</template>

<script>
import { mapActions, mapMutations, mapGetters } from 'vuex'
import {
	rechargeTemplate,
	uploadPingzheng
} from '@/api/user';
import pageTitleMixin from '@/mixins/page-title-mixin'
import {
	baseURL
} from '@/config/app';

export default {
	mixins: [pageTitleMixin],
	data() {
		return {
			chain: 1,
			chain_type: 'TRC20',
			address: '',
			bochang_address: '',
			bian_address: '',
			rechange_amount: '',
			action: baseURL + '/api/file/formimage',
			fileList: [],
			showDemo: false
		}
	},
	computed: {
		...mapGetters(['currentLanguage']),
	},
	onLoad() {
		this.rechargeTemplate()
	},
	created() {

	},
	methods: {
		handleDemo() {
			this.showDemo = !this.showDemo
		},
		handleAddress(){
			// uni.navigateTo({
			// 	url: '/pages/bind_wallet/bind_wallet'
			// })
		},
		handleChainChange(e) {
			this.$nextTick(() => {
				this.rechargeTemplate()
			})
		},
		handleChainTypeChange(e) {
			this.$nextTick(() => {
				this.rechargeTemplate()
			})
		},
		rechargeTemplate() {
			rechargeTemplate({
				chain: this.chain,
				// chain_type: this.chain_type
			}).then(res => {
				if (res.code == 1) {
					this.chain = res.data.chain
					this.chain_type = res.data.chain_type
					this.address = res.data.wallet_address
					this.bochang_address = res.data.bochang_address
					this.bian_address = res.data.bian_address

				}
			})
		},
		onSuccess(e) {
			this.fileList.push(e.data.base_uri)

		},
		onRemove(index) {
			this.fileList.splice(index, 1)
			console.log(index)
		},
		copy(text) {
			// const copyText = `${[this.$t('topup.tronChain'),this.$t('topup.binanceChain')][this.chain - 1]}${this.$t('topup.pushaddress')}：${text}`
			uni.setClipboardData({
				data: text,
				success: () =>
					uni.showToast({
						title: this.$t('topup.copySuccess')
					})
			})
		},
		handleSubmit() {
			if (!this.rechange_amount) {
				uni.showToast({
					title: this.$t('topup.enterRechargeAmount'),
					icon: 'none'
				})
				return
			}
			if (this.rechange_amount < 30) {
				uni.showToast({
					title: this.$t('topup.limitRechargeAmount'),
					icon: 'none'
				})
				return
			}
			if (!this.fileList.length) {
				uni.showToast({
					title: this.$t('topup.pleaseUploadVoucher'),
					icon: 'none'
				})
				return
			}
			uploadPingzheng({
				rechange_amount: this.rechange_amount,
				pingzheng: this.fileList[0]
			}).then(res => {
				if (res.code == 1) {
					uni.showToast({
						title: this.$t('topup.uploadSuccess')
					})
					uni.navigateBack();
				} else {
					uni.showToast({
						title: res.msg
					})
				}
			})

		}
	},
}
</script>

<style lang="scss" scoped>
.topup {
	padding-bottom: 150rpx;
	/deep/ .u-radio__icon-wrap{
		margin-left: 20rpx;
	}
	/deep/ .u-radio__label {
		color: #fff;
		font-size: 26rpx;
		margin-right: 0;
	}

	.topup-coin {
		margin: 24rpx;
		padding: 0 24rpx;
		background-image: url('@/static/images/<EMAIL>');
		background-size: 100% 100%;
		background-repeat: no-repeat;

		.topup-coin-item {
			height: 98rpx;
			border-bottom: 1px dashed #90BAEB;

			&.address {
				height: auto;
				padding: 28rpx 0;

				.topup-coin-item-content {
					margin-left: 90rpx;
				}
			}

			&:last-child {
				border-bottom: none;
			}

			.topup-coin-item-title {
				min-width: 0;
				flex: 1;
				font-size: 26rpx;
				color: #A6CFFF;
				word-break: break-all;
			}

			.topup-coin-item-content {
				height:100%;
			display: flex;
			align-items: center;
			justify-content: center;
				.topup-coin-item-button {
					width: 128rpx;
					height: 64rpx;
					line-height: 64rpx;
					background-color: $-color-primary;
					color: #1D1D3B;
					padding: 0 10rpx;
					border-radius: 0rpx;
				}
			}
		}
	}

	.item-title {
		margin: 24rpx 0;
		padding: 0 24rpx;
		font-size: 26rpx;
		color: #fff;

		&::before {
			content: '';
			display: inline-block;
			width: 2rpx;
			height: 20rpx;
			background-color: #fff;
			margin-right: 10rpx;
		}
	}

	.item-content {
		padding: 0 24rpx;

		.item-content-item {
			padding: 0 24rpx;
			height: 88rpx;
			margin-bottom: 24rpx;
			font-size: 26rpx;
			background-image: url('@/static/images/<EMAIL>');
			background-size: 100% 100%;
			background-repeat: no-repeat;

			.item-content-item-title {
				color: #A6CFFF;
				display: flex;
				align-items: center;

				&:before {
					content: '';
					display: inline-block;
					width: 10rpx;
					height: 10rpx;
					margin-right: 10rpx;
					border-radius: 50%;
					background-color: #0292F5;
				}
			}

			.item-content-item-content {
				color: $-color-primary;
			}
		}
	}

	.upload {
		padding: 0 15rpx;

		/deep/ .u-list-item {
			background: none;
			background-image: url('@/static/images/<EMAIL>');
			background-size: 100% 100%;
			background-repeat: no-repeat;
		}

		/deep/ .u-add-wrap {
			color: $-color-primary;
		}


	}
	.upload-demo {
			margin: 15rpx;
			color: $-color-primary;
		}
	.demo-wrap{
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1000;
		.close-btn{
			position: absolute;
			top: 20rpx;
			right: 20rpx;
			color: #fff;
		}
		.demo{
			width: 750rpx;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}

	}
	.button-group {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 24rpx;
		background-color: #011750;
		.button-group-button {
			width: 100%;
			height: 88rpx;
			line-height: 88rpx;
			background: #04F9FC;
			border-radius: 0rpx;
		}
	}

}
</style>
