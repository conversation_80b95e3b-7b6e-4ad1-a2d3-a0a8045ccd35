<template>
	<view class="user"><!-- :style="[background]" -->
		<view :class="['bg', showgif ? 'showgif':'']" v-if="isLogin">
			<u-image class="bg-img" src="/static/images/wecome_home.gif" width="100%" height="100%"></u-image>
		</view>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" :up="upOption" :down="{auto: false}" @down="downCallback" :use="false">
			<view class="header" :style="{paddingTop:statusBarHeight + 30 +'px'}">
				<view class="hd-wrap">
					<view class="user-info flex row-between">
						<router-link to="/bundle/pages/user_profile/user_profile">
							<view class="info flex">
								<image class="avatar m-r-20 flex-none"
									:src="isLogin ? userInfo.avatar : '/static/images/<EMAIL>'" />
								<view v-if="isLogin">
									<view class="name flex">
										<view class="xxl bold line-2">{{userInfo.nickname}}</view>
									</view>
									<view class="flex m-t-10" v-if="userInfo.sn">
										<view class="xs m-r-20">{{ $t('user.userId') }}: {{userInfo.sn}}</view>
										<view class="xs primary row-center m-l-5" @tap.stop="onCopy">{{ $t('common.copy') }}</view>
									</view>
									<view class="flex m-t-10">
										<view class="level xs" style="margin-right: 20rpx;border-radius: 6rpx;">
											{{ $t('user.level') }}：{{ userInfo.level === 1 ? (userInfo.tiyan == 0 ? $t('user.userLevel.validUser'):$t('user.userLevel.registeredUser')) :
                                            userInfo.level === 2 ? $t('user.userLevel.v1') :
                                            userInfo.level === 3 ? $t('user.userLevel.v2') :
                                            userInfo.level === 4 ? $t('user.userLevel.v3') :
                                            userInfo.level === 5 ? $t('user.userLevel.v4') :
                                            userInfo.level === 6 ? $t('user.userLevel.v5') : '' }}
										</view>
										<!-- <view class="level xs" style="border-radius: 6rpx;">
											合伙人级别：{{ userInfo.hehuoren_level === 0 ? $t('user.partnerLevel.none') :
                                            userInfo.hehuoren_level === 4 ? $t('user.partnerLevel.junior') :
                                            userInfo.hehuoren_level === 5 ? $t('user.partnerLevel.intermediate') :
                                            userInfo.hehuoren_level === 6 ? $t('user.partnerLevel.senior') :
                                            userInfo.hehuoren_level === 7 ? $t('user.partnerLevel.founding') : $t('user.partnerLevel.none') }}
										</view> -->
									</view>
								</view>
								<view v-else>
									<view style="font-size: 42rpx">{{ $t('user.clickToLogin') }}</view>
									<view class="sm m-t-10 lighter">{{ $t('user.loginToExperience') }}</view>
								</view>
							</view>
						</router-link>
					</view>
					<view class="user-assets flex m-t-20 m-b-20">

						<!-- <router-link class="user-assests-item" to="/bundle_c/pages/user_fen/fen">
							<view class="flex-col col-center">
								<view class="lg primary">
									{{userInfo.fen || 0}}
								</view>
								<view class="sm m-t-10">
									管理奖
								</view>
							</view>
						</router-link> -->
						<router-link class="user-assests-item" to="/bundle/pages/user_order/user_order">
							<view class="flex-col col-center">
								<view class="lg primary">
									{{userInfo.total_order_amount || 0}} U
								</view>
								<view class="memo xs m-t-10">
									{{ $t('user.rentalDeposit') }}
								</view>
							</view>
						</router-link>
						<router-link class="user-assests-item" to="/bundle_c/pages/user_love/love">
							<view class="flex-col col-center">
								<view class="xl primary">
									{{userInfo.user_money || 0}} U
								</view>
								<view class="memo xs m-t-10">
									{{ $t('user.balance') }}
								</view>
							</view>
						</router-link>
						<!-- <router-link class="user-assests-item" to="/bundle_c/pages/user_love/love">
							<view class="flex-col col-center">
								<view class="lg primary">
									{{userInfo.love || 0}}
								</view>
								<view class="memo xs m-t-10">
									余额
								</view>
							</view>
						</router-link> -->

						<!-- <router-link class="user-assests-item" to="/bundle_c/pages/user_earnings/earnings">
							<view class="flex-col col-center">
								<view class="lg primary">
									{{userInfo.earnings || 0}}
								</view>
								<view class="memo xs m-t-10">
									收益
								</view>
							</view>
						</router-link> -->

						<router-link class="user-assests-item" to="/bundle_c/pages/user_integral/integral">
							<view class="flex-col col-center">
								<view class="xl primary">
									{{userInfo.user_integral || 0}} U
								</view>
								<view class="memo xs m-t-10">
									{{ $t('user.earnings') }}
								</view>
							</view>
						</router-link>
						<!-- <router-link class="user-assests-item" to="/bundle_c/pages/user_goldcoin/goldcoin">
							<view class="flex-col col-center">
								<view class="xl primary">
									{{userInfo.goldcoin || 0}}
								</view>
								<view class="sm m-t-10">
									购物券
								</view>
							</view>
						</router-link> -->
					</view>
				</view>
			</view>
			<view class="server-nav">
				<view>
					<view class="title flex row-between">
						<view class="sm">{{ $t('user.teamInfo') }}</view>
					</view>
				</view>
				<view class="nav">
					<view v-for="(item, index) in list_1" :key="index" class="item flex row-between col-center m-b-20" @tap="ClickLink(item)">
						<!-- <image class="nav-icon" :src="item.image"></image> -->
						<view class="name sm">{{ item.name.includes('.') ? $t(item.name) : item.name }}</view>
						<u-icon name="arrow-right" color="#04F9FC" size="32"></u-icon>
					</view>
				</view>
			</view>
			<view class="server-nav">
				<view>
					<view class="title flex row-between">
						<view class="sm">{{ $t('user.settings') }}</view>
					</view>
				</view>
				<view class="nav">
					<view v-for="(item, index) in list_2" :key="index" class="item flex row-between col-center m-b-20" @tap="ClickLink(item)">
						<!-- <image class="nav-icon" :src="item.image"></image> -->
						<view class="name sm">
							{{ item.name.includes('.') ? $t(item.name) : item.name }}
						</view>
						<button class="custom-button" open-type="contact" v-if="item.id=='kefu'" plain
							style="position: absolute;bottom: 0;left: 0;width: 100%;height: 100%"
						></button>
						<u-icon name="arrow-right" color="#04F9FC" size="32"></u-icon>
					</view>
				</view>
			</view>
		</mescroll-body>
		<u-popup mode="center" v-model="show" border-radius="14" :mask-close-able="false" z-index="999" :closeable="false" @open="open" @close="close">
			<view class="popContent" style="background-color: #fff;padding: 48rpx;width:666rpx;">
				<view class="popTitle m-b-30" style="font-size: 30rpx;
				color: #333333;
				font-weight: 600;
				text-align: center;">{{ $t('user.bindSuperior') }}</view>
				<view class="input m-b-30">
					<u-input :customStyle="{color:'#333333'}" v-model="invite_code" type="number" class="input" :border="true" height="76" border-color="#999999" :placeholder="$t('user.pleaseEnterSuperiorId')"/>
				</view>
				<u-button class="binding" type="error" hover-class="none" @click="ClickBinding" style="height: 80rpx;
					line-height: 80rpx;
					">{{ $t('user.confirmBinding') }}</u-button>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import MescrollCompMixin from "@/components/mescroll-uni/mixins/mescroll-comp";
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import MescrollMoreItemMixin from "@/components/mescroll-uni/mixins/mescroll-more-item.js";
	import {
		mapGetters,
		mapActions
	} from 'vuex'
	import {
		getMenu
	} from '@/api/store'
	import {
		toLogin
	} from '@/utils/login'
	import {
		menuJump,
		copy
	} from '@/utils/tools'
	import Cache from '@/utils/cache'
	import {
	    baseURL,
	    basePath
	} from '@/config/app'
	import {
		bindSuperior
	} from '@/api/user'

	import {
		signRelease
	} from '@/api/youpin'

// Import the page title mixin
import pageTitleMixin from '@/mixins/page-title-mixin';
	const app = getApp()
	let systemInfo = uni.getSystemInfoSync();
	export default {
		mixins: [MescrollCompMixin, MescrollMixin, MescrollMoreItemMixin, pageTitleMixin], // 使用mixin
		data() {
			return {
				statusBarHeight: systemInfo.statusBarHeight,
				showNav: false,
				navBg: 0,
				welcomeHomeAudio: "https://api.aichatgpt.net.cn/uploads/mp3/home.mp3",
				showgif: true,
				audioContext: null,
				menuList: [],
				upOption: {
				    use: false
				},
				handle:[
					{name:'邀请码',id:'yaoqing',iconUrl:require('@/bundle_c/static/icon_yao.png'),type:1,path:'/bundle/pages/invite_fans/invite_fans'},
					{name:'我的团队',id:'team',iconUrl:require('@/bundle_c/static/icon_fensi.png'),type:1,path:'/bundle_c/pages/team/team'},
					{name:'我的推荐人',id:'tuijian',iconUrl:require('@/bundle_c/static/icon_tuijian.png'),type:2},
					{name:'我的订单',id:'team',iconUrl:require('@/bundle_c/static/icon_order.png'),type:1,path:'/bundle_c/pages/order_list/order_list'},
					{name:'设置',id:'setting',iconUrl:require('@/bundle_c/static/icon_setup.png'),type:1,path:'/bundle/pages/user_profile/user_profile'},
				],
				list_1:[
					{name:"user.myRental",id:"order",image:require('@/bundle_c/static/icon_order.png'),path:"/bundle/pages/user_order/user_order"},
					{name:"user.myTeam",id:"team",image:require('@/bundle_c/static/icon_team.png'),path:"/bundle_c/pages/team/team"},
					{name:"user.shareWithFriends",id:"invite",image:require('@/bundle_c/static/icon_tuijian.png'),path:"/bundle/pages/invite_fans/invite_fans"},
					// {name:"资料管理",id:"ziliao",image:require('@/bundle_c/static/icon_ziliao.png'),path:"/bundle_c/pages/ziliao_guanli/ziliao_guanli"},
					// {name:"我的商品",id:"goods",image:require('@/bundle_c/static/icon_goods.png'),path:"/bundle_c/pages/shop_cart/shop_cart"},
					// {name:"我的货款",id:"daikuan",image:require('@/bundle_c/static/icon_daikuan.png'),path:"/bundle_c/pages/user_daikuan/daikuan"},
					// {name:"提现账户",id:"bank",image:require('@/bundle_c/static/icon_bank.png'),path:"/bundle_c/pages/bank_list/bank_list"},
				],
				list_2:[
					// {name:"推荐码",id:"invite",image:require('@/bundle_c/static/icon_tuijian.png'),path:"/bundle/pages/invite_fans/invite_fans"},
					// //{name:"我的橱窗",id:"chuchuang",image:require('@/bundle_c/static/icon_chuchuang.png'),path:"/bundle_c/pages/chuchuang/chuchuang"},
					// {name:"签到",id:"qiandao",image:require('@/bundle_c/static/icon_chuchuang.png'),path:""},
					// {name:"收货地址",id:"address",image:require('@/bundle_c/static/icon_dizhi.png'),path:"/bundle/pages/user_address/user_address"},
					// {name:"我的团队",id:"team",image:require('@/bundle_c/static/icon_team.png'),path:"/bundle_c/pages/team/team"},
					// {name:"联系客服",id:"kefu",image:require('@/bundle_c/static/icon_kefu.png'),path:""},
					// {name:"公告历史",id:"gongao",image:require('@/bundle_c/static/icon_gongao.png'),path:""},
					{name:"common.changePassword",id:"setup",image:require('@/bundle_c/static/icon_setup.png'),path:"/bundle_c/pages/forget_pwd/forget_pwd"},
					{name:"user.changePayPassword",id:"pay_password",image:require('@/bundle_c/static/icon_setup.png'),path:"/bundle_c/pages/pay_password/pay_password?type=update"},
					{name:"settings.languageSettings",id:"language",image:require('@/bundle_c/static/icon_setup.png'),path:"/bundle/pages/language_settings/language_settings"},
				],
				invite_code:'',
				isClick:true,//点击事件除重
				show:false,
			};
		},

		onLoad(options) {
			//this.getMenuFun();

		},

		onShow() {
			this.showgif = true;
			// 播放欢迎音频
			this.playWelcomeAudio();
			setTimeout(() => {
				this.showgif = false;
			}, 6000);
			this.getCartNum()
			this.getUser().then(res => {
				if(Cache.get('TOKEN')){
					if(this.userInfo.first_leader == null || this.userInfo.first_leader == ''){
						this.show = true
					}else{
					}
				}
			})
		},

		onPageScroll(e) {
			const top = uni.upx2px(100)
			const {
				scrollTop
			} = e
			let percent = scrollTop / top > 1 ? 1 : scrollTop / top
			this.navBg = percent
		},
		methods: {
			...mapActions(['getCartNum', 'getUser']),
			// 播放欢迎音频
			playWelcomeAudio() {
				if(!this.isLogin) return;
				try {
					// 停止之前的音频（如果存在）
					if (this.audioContext) {
						this.audioContext.stop();
						this.audioContext.destroy();
					}

					// 创建新的音频上下文
					this.audioContext = uni.createInnerAudioContext();
					this.audioContext.src = this.welcomeHomeAudio;
					this.audioContext.autoplay = true;
					this.audioContext.loop = false;

					// 音频播放完成后销毁上下文
					this.audioContext.onEnded(() => {
						if (this.audioContext) {
							this.audioContext.destroy();
							this.audioContext = null;
						}
					});

					// 音频播放错误处理
					this.audioContext.onError((error) => {
						console.log('音频播放错误:', error);
						if (this.audioContext) {
							this.audioContext.destroy();
							this.audioContext = null;
						}
					});

					// 开始播放
					this.audioContext.play();
				} catch (error) {
					console.log('播放欢迎音频失败:', error);
				}
			},
			downCallback(page) {
				this.getUser().then(() => {
					this.mescroll.endSuccess();
				}).catch(() => {
					this.mescroll.endErr()
				})
				this.getMenuFun();
			},
			goLogin() {
				let {
					isLogin
				} = this;
				if (isLogin) {
					uni.navigateTo({
						url: '/pages/user_profile/user_profile'
					});
					return;
				}
				uni.navigateTo({
					url: '/pages/login/login'
				});
			},

			goPage(url) {
				if (!this.isLogin) return toLogin()
				uni.navigateTo({
					url
				});
			},
			async getMenuFun() {
				const {
					data,
					code
				} = await getMenu({
					type: 2,
				})
				if (code == 1) {
					this.menuList = data
				}
			},
			onCopy(e) {
				copy(this.userInfo.sn, this.$t('common.copySuccess'))
			},
			menuJump(item) {
				menuJump(item)
			},
			//点击事件
			ClickLink(item){
				if(item.id==='shuju'){
					this.$toast({title:"数据更新成功",icon:"success"})
					return false
				}
				if(item.id==='qiandao'){
					signRelease().then(res=>{
						if(res.code==1){
							this.$toast({title:"签到成功",icon:"success"})
						}
					})
					return false
				}
				if(item.path){
					// Check if the name is a translation key
					if (item.name && item.name.includes('.')) {
						item.displayName = this.$t(item.name);
					} else {
						item.displayName = item.name;
					}
					uni.navigateTo({
						url: item.path
					})
				}else{
					this.$toast({title:'该功能暂未开放'})
				}
			},
			//绑定
			ClickBinding(){
				if(!this.isClick) return false
				this.isClick = false
				if(this.invite_code){
					bindSuperior({code: this.invite_code}).then((res)=>{
						if(res.code == 1){
							this.invite_code = '' //重置 //重置
							this.$toast({ title: res.msg, icon:'success'})
							this.show = false
							this.getUser()
						}else{
							this.$toast({ title: res.msg})
						}
					})
					this.isClick = true
				}else{
					this.$toast({title: this.$t('user.pleaseEnterSuperiorId')})
					this.isClick = true
				}
			},
			open(){
				uni.hideTabBar()
			},
			close(){
				uni.showTabBar()
			},
		},
		filters:{
			Setlevel(num){
				let name = ''
				switch (num){
					case 2:
						name = 'V1'
						break;
					case 3:
						name = 'V2'
						break;
					case 4:
						name = 'V3'
						break;
					case 5:
						name = 'V4'
						break;
					case 6:
						name = 'V5'
						break;
					case 1:
						name = '有效用户'
						break;
					default:
						name = ''
						break;
				}
				return name
			},
			Sethehuoren_level(num){
				let name = ''
				switch (num){
					case 0:
						name = '无'
						break;
					case 4:
						name = '初级合伙人'
						break;
					case 5:
						name = '中级合伙人'
						break;
					case 6:
						name = '高级合伙人'
						break;
					case 7:
						name = '联创合伙人'
						break;
					default:
						name = '无'
						break;
				}
				return name
			},
		},
		computed: {
			...mapGetters(["userInfo", "inviteCode", 'appConfig', 'currentLanguage']),
			background() {
				const {
					center_setting
				} = this.appConfig
				return center_setting.top_bg_image ? {
					'background-image': `url(${center_setting.top_bg_image})`
				} : {}
			},
		}
	};
</script>
<style lang="scss" scoped>
.popContent{
    .binding{
        background-color: $-color-primary;
		color: #010101;
    }
}
	.custom-button {
		border: none; /* 去除边框 */
		background-color: transparent; /* 背景透明 */
		color: $-color-normal; /* 自定义文本颜色 */
	}

	.custom-button:active {
		background-color: transparent !important; /* 点击时保持透明 */
		transform: none !important; /* 去掉点击时的缩放效果 */
	}
	.user {
		.bg {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
			transition: all .5s ease;
			opacity: 0;
		&.showgif{
			opacity: 1;
		}
		.bg-img {
			width: 100%;
			height: 100%;
		}
	}
		.header {
			.hd-wrap {
				padding-bottom: 20rpx;
				border-radius: 20rpx;
			}

			.user-info {
				padding: 30rpx;
				color: $-color-normal;
				.lighter{
					color: $-color-normal;
				}
				.avatar {
					height: 110rpx;
					width: 110rpx;
					border-radius: 50%;
					overflow: hidden;
					border: 1px solid #0292F5;
				}

				.name {
					text-align: left;
					margin-bottom: 5rpx;
				}

				.user-id {
					border: $-solid-border;
					border-radius: 100rpx;
					padding: 2rpx 15rpx;
				}

				.user-opt {
					position: relative;

					.dot {
						position: absolute;
						background-color: #ee0a24;
						border: 2rpx solid #FFFFFF;
						color: $-color-primary;
						border-radius: 100%;
						top: 6rpx;
						right: 0rpx;
						font-size: 22rpx;
						min-width: 16rpx;
						height: 16rpx;
					}
				}

				.level {
					background: #333;
					padding: 0 15rpx;
					color: #ffdea5;
					line-height: 40rpx;

					.v {
						font-style: italic;
					}
				}
			}

			.user-assets {
				padding: 0 24rpx;
				flex: 1;
				gap: 24rpx;
				.user-assests-item {
					padding: 16rpx 0;
					flex: 1;
					background-image: url('@/static/images/<EMAIL>');
					background-size: 100% 100%;
					background-repeat: no-repeat;
					.memo{
						color: $-color-lighter;
					}
				}
			}

		}

		.order-nav {
			.icon-contain {
				position: relative;
			}
		}

		.order-nav,
		.my-assets {
			margin: 20rpx 20rpx 0;
			border-radius: 8rpx;
		}

		.server-nav {
			margin: 20rpx;
			border-radius: 8rpx;
			margin-top: 80rpx;
		}

		.title {
			height: 88rpx;
			color: $-color-white;
		}

		.nav {
			.assets-item {
				flex: 1;
			}
			.name{
				color: $-color-normal;
				display: flex;
				align-items: center;
				&:before{
					content: '';
					display: inline-block;
					width: 10rpx;
					height: 10rpx;
					background-color: #0292F5;
					margin-right: 10rpx;
					border-radius: 10rpx;
				}
			}
			.item {
				padding: 0 20rpx;
				position: relative;
				width: 100%;
				background-image: url('@/static/images/<EMAIL>');
				background-size: 100% 100%;
				background-repeat: no-repeat;
				height: 88rpx;
			}

			.badge {
				padding: 0 6rpx;
				min-width: 28rpx;
				height: 28rpx;
				border-radius: 28rpx;
				box-sizing: border-box;
				border: 1rpx solid $-color-primary;
				color: $-color-primary;
				position: absolute;
				left: 33rpx;
				top: -10rpx;
				z-index: 2;
			}

			.nav-icon {
				width: 60rpx;
				height: 60rpx;
			}
		}
		.handle{
			padding: 16rpx 0;
			border-radius: 8rpx;
			background-color: #fff;

			.handleItem{
				padding: 16rpx 32rpx;

				&:last-child{
					margin-bottom: 0;
				}

				image{
					width: 80rpx;
					height: 80rpx;
					margin-right: 32rpx;
				}
				.text{
					font-size: 32rpx;
					color: #333333;
				}
				.right{
					margin-left: auto;

					.version{
						margin-left: auto;
						color: #333;
					}
					.tuijian{
						margin-left: auto;
						color: #333;
					}
					::v-deep .u-icon{
						margin-left: 20rpx;
					}
				}

			}
		}
	}
</style>
