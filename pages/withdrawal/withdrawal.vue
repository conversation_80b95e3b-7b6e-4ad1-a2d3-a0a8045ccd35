<template>

	<view class="giveaway">
		<view class="acount-login">
			<view class="input-item">
				<view class="input-label flex normal">{{ $t('withdrawal.withdrawalAddress') }}</view>
				<view class="input-content">
					<view class="input">
						<u-input v-model="address" :disabled="true" style="width: 100%;" :custom-style="{
							'height': '88rpx'
						}" :placeholder="$t('withdrawal.enterWithdrawalAddress')" placeholder-style="color: #90BAEB;"
							:input-border="false" />
					</view>
				</view>
			</view>
			<view class="input-item">
				<view class="input-label flex normal">{{ $t('withdrawal.withdrawalAmount') }}</view>
				<view class="input-content">
					<view class="input">
						<u-input v-model="amount" :disabled="disabled" style="width: 100%;" :custom-style="{
							'height': '88rpx'
						}" :placeholder="$t('withdrawal.enterWithdrawalAmount')" placeholder-style="color: #90BAEB;"
							:input-border="false" />
					</view>
				</view>
			</view>
			<view class="flex" style="width: 702rpx;" v-if="lx != 3">
				<view class="lighter">{{ $t('withdrawal.availableBalance') }}：{{ wallet.user_money || 0 }} USDT</view>
				<view class="primary m-l-24" @click="handleWithdrawAll">{{ $t('withdrawal.withdrawAll') }}</view>
			</view>
			<view class="login-wrap">
				<button size="lg" class="login-btn" type="primary" @click="handleWithdraw">{{ lx == 3 ?
					$t('userOrder.refundRent') : $t('withdrawal.withdraw')
					}}</button>
			</view>
		</view>
		<!-- 提示输入交易密码 -->
		<u-popup v-model="showMessagePop" mode='center' border-radius="30" width='670' :closeable="true" @close="close"
			@open="open">
			<view style="padding: 40rpx;background-color: #fff;">
				<view style="font-size: 32rpx;margin-bottom: 50rpx;
					font-weight: bold;text-align: center;
					color: #282828;">{{ $t('payment.prompt') }}</view>
				<view class="m-b-50">
					<u-input v-model="pay_password" :customStyle="{ color: '#333333' }" type="password" :border="true"
						:placeholder="$t('payment.enterTransactionPassword')" style="color: #282828;" />
				</view>
				<view style="width: 100%;
					border-radius: 44rpx;
					opacity: 1;
					line-height: 80rpx;
					background: #04F9FC;
					font-size: 28rpx;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #1D1D3B;
					text-align: center;" @click="confirm">{{ $t('payment.confirm') }}</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import {
	mapMutations,
	mapActions,
	mapGetters
} from 'vuex'
import {
	accountLogin,
	codeLogin,
	sendSms,
	wxpLogin,
	smsCodeLogin,
	opLogin,
} from '@/api/app';
import {
	bindSuperior, getWxMnpMobile, getMobileByMnp,
	applyWithdraw,
	getWallet,
	getUserInfo,
} from '@/api/user'
import wechath5 from '@/utils/wechath5'
import {
	isWeixinClient,
	currentPage,
	client,
	trottle
} from '@/utils/tools'
import {
	SMSType
} from "@/utils/type"
import Cache from "@/utils/cache"
import {
	BACK_URL,
	INVITE_CODE
} from '@/config/cachekey'
import {
	getWxCode,
	getUserProfile
} from '@/utils/login'
import pageTitleMixin from '@/mixins/page-title-mixin'
const loginType = {
	ACCOUNT_LOGIN: 0,
	SMS_CODE_LOGIN: 1,
}
export default {
	mixins: [pageTitleMixin],
	data() {
		return {
			isAgreement: false,
			password: '',
			address: '',
			amount: '',
			code: '',
			isWeixin: '',
			loginType: 0,
			smsCode: '',
			codeTips: '',
			telephone: "",
			text: '',
			phoneNew: '',
			wallet: {},
			lx: 1,
			sn: '',
			disabled: false,
			submitLoading: false,
			showMessagePop: false,
			pay_password: "",
		};
	},

	onLoad(option) {
		console.log(option)
		if (option.lx) {
			this.lx = option.lx;
		}

		this.sn = option.sn;
		if (this.lx == 3) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$t('userOrder.refundRent')
				});
			}, 10)
			this.disabled = true;
			this.wallet.user_money = option.amount;
			this.amount = option.amount;
		} else {
			uni.setNavigationBarTitle({
				title: this.$t('pageTitles.withdraw')
			});
			this.queryWallet()
		}

	},
	onUnload() {

	},
	onShow() {
		getUserInfo().then(res => {
			if (res.code == 1) {
				if (!res.data.wallet_address) {
					//未绑定时，弹层让用户确认是否去绑定地址
					uni.showModal({
						title: this.$t('withdrawal.prompt'),
						content: this.$t('withdrawal.pleaseBindWalletFirst'),
						success: (res) => {
							if (res.confirm) {
								//去绑定
								uni.navigateTo({
									url: '/pages/bind_wallet/bind_wallet'
								})
							} else if (res.cancel) {
							}
						}
					})

				} else {
					this.address = res.data.wallet_address
				}
			}
		})
	},
	methods: {
		open() {
			// this.showMessagePop = true
		},
		close() {
			this.showMessagePop = false
		},
		confirm() {
			if (!this.pay_password) {
				this.$toast({ title: this.$t('payment.pleaseEnterTransactionPassword') })
				return
			}
			this.showMessagePop = false
			this.handleWithdraw()
		},
		queryWallet() {
			getWallet().then(res => {
				const data = res.data || {}
				this.wallet = {
					...data,
					user_money: this.lx == 1 ? data.user_money : data.user_integral
				}
			})
		},
		handleWithdraw() {
			if (!this.address) {
				uni.showToast({
					title: this.$t('withdrawal.pleaseEnterWithdrawalAddress'),
					icon: 'none'
				})
				return
			}
			if (!this.amount) {
				uni.showToast({
					title: this.$t('withdrawal.pleaseEnterWithdrawalAmount'),
					icon: 'none'
				})
				return
			}
			if (!this.pay_password) {
				this.showMessagePop = true
				return
			}
			if (this.submitLoading) {
				return
			}
			this.submitLoading = true
			const params = {
				lx: this.lx || 1,
				wallet_address: this.address,
				money: this.amount,
				pay_password: this.pay_password,
			}
			if (this.lx == 3) {
				params.order_sn = this.sn
			}
			applyWithdraw(params).then(res => {
				this.submitLoading = false
				if (res.code === 1) {
					this.queryWallet()
					this.address = ''
					this.amount = ''
					uni.showToast({
						title: this.$t('withdrawal.withdrawalSuccess'),
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			})
		},
		handleWithdrawAll() {
			this.amount = this.wallet.user_money
		}
	},
	computed: {
		...mapGetters(['currentLanguage', 'userInfo'])
	}
};
</script>
<style lang="scss">
page {
	background-color: #fff;
	text-align: center;
	padding: 0;

	.giveaway {
		display: flex;
		flex-direction: column;



		.acount-login {
			padding-top: 100rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			box-sizing: border-box;
			min-height: 0;

			.logo {

				height: 80rpx;
				margin-bottom: 100rpx;
			}

			.input-item {
				margin-bottom: 32rpx;
				width: 702rpx;

				.input-label {
					flex: none;
					margin-bottom: 24rpx;
					color: #fff;
					font-size: 26rpx;

					.required {
						color: #F93E3E;
						margin-right: 5rpx;
						vertical-align: middle;
					}

					&:before {
						content: '';
						display: inline-block;
						height: 20rpx;
						width: 2rpx;
						margin-right: 10rpx;
						background-color: #fff;
					}
				}
			}

			.input {
				background-image: url('@/static/images/<EMAIL>');
				background-size: 100% 100%;
				background-repeat: no-repeat;
				padding: 0 24rpx;
			}

			.sms-btn {
				border: 1px solid $-color-primary;
				width: 176rpx;
				height: 60rpx;
				box-sizing: border-box;
			}

			.wx-login {
				margin-top: 60rpx;
				margin-bottom: 60rpx;

				.image {
					margin-top: 40rpx;
					width: 80rpx;
					height: 80rpx;
				}
			}
		}

		.login-wrap {
			position: fixed;
			display: flex;
			flex-direction: column;
			align-items: center;
			bottom: 0;
			width: 100%;
			background-color: #011750;
			padding-top: 18rpx;

			.login-btn {
				border-radius: 0rpx;
				width: 702rpx;
				margin: 20rpx 0 50rpx;
				color: #1D1D3B;
				font-size: 34rpx;
			}
		}
	}
}
</style>
