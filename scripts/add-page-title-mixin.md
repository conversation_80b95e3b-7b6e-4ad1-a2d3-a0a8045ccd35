# Adding Page Title Mixin to All Pages

This document provides instructions for adding the page title mixin to all pages listed in the `pageTitleMap` in `utils/page-title.js`.

## Steps for Each Page

For each page, follow these steps:

1. Add the import statement:
   ```javascript
   import pageTitleMixin from '@/mixins/page-title-mixin';
   ```

2. Add the mixin to the component's mixins array:
   ```javascript
   export default {
     mixins: [pageTitleMixin, /* existing mixins */],
     // rest of component definition
   }
   ```

3. Make sure the component has access to the `currentLanguage` getter:
   ```javascript
   computed: {
     ...mapGetters(['currentLanguage']), // Add this if not already present
     // other computed properties
   }
   ```

## List of Pages to Modify

### Main Pages

- [x] pages/index/index.vue
- [x] pages/login/login.vue
- [ ] pages/daren/daren.vue
- [ ] pages/i18n_test/i18n_test.vue
- [ ] pages/ai/ai.vue
- [ ] pages/giveaway/giveaway.vue
- [ ] pages/withdrawal/withdrawal.vue
- [ ] pages/sucaiku/sucaiku.vue
- [ ] pages/agent/agent.vue
- [ ] pages/topup/topup.vue
- [x] pages/user/user.vue (already done)

### Bundle Pages

- [ ] bundle/pages/language_settings/language_settings.vue
- [ ] bundle/pages/all_comments/all_comments.vue
- [ ] bundle/pages/activity_detail/activity_detail.vue
- [ ] bundle/pages/goods_seckill/goods_seckill.vue
- [ ] bundle/pages/user_vip/user_vip.vue
- [ ] bundle/pages/user_order/user_order.vue
- [ ] bundle/pages/order_details/order_details.vue
- [ ] bundle/pages/user_address/user_address.vue
- [ ] bundle/pages/address_edit/address_edit.vue
- [ ] bundle/pages/user_coupon/user_coupon.vue
- [ ] bundle/pages/user_collection/user_collection.vue
- [ ] bundle/pages/after_sales/after_sales.vue
- [ ] bundle/pages/apply_refund/apply_refund.vue
- [ ] bundle/pages/after_sales_detail/after_sales_detail.vue
- [ ] bundle/pages/input_express_info/input_express_info.vue
- [ ] bundle/pages/user_profile/user_profile.vue
- [ ] bundle/pages/user_wallet/user_wallet.vue
- [ ] bundle/pages/user_payment/user_payment.vue
- [ ] bundle/pages/contact_offical/contact_offical.vue
- [ ] bundle/pages/server_explan/server_explan.vue
- [ ] bundle/pages/store_settled/store_settled.vue
- [ ] bundle/pages/settled_result/settled_result.vue
- [ ] bundle/pages/settled_recode/settled_recode.vue
- [ ] bundle/pages/goods_reviews/goods_reviews.vue
- [ ] bundle/pages/user_comment/user_comment.vue
- [ ] bundle/pages/goods_logistics/goods_logistics.vue
- [ ] bundle/pages/user_spread/user_spread.vue
- [ ] bundle/pages/monthly_bill/monthly_bill.vue
- [ ] bundle/pages/monthly_bill_detail/monthly_bill_detail.vue
- [ ] bundle/pages/user_spread_order/user_spread_order.vue
- [ ] bundle/pages/user_withdraw/user_withdraw.vue
- [ ] bundle/pages/user_withdraw_code/user_withdraw_code.vue
- [ ] bundle/pages/widthdraw_result/widthdraw_result.vue
- [ ] bundle/pages/invite_fans/invite_fans.vue
- [ ] bundle/pages/download/download.vue
- [ ] bundle/pages/user_bill/user_bill.vue
- [ ] bundle/pages/user_growth/user_growth.vue
- [ ] bundle/pages/recharge_record/recharge_record.vue
- [ ] bundle/pages/user_fans/user_fans.vue
- [ ] bundle/pages/commission_details/commission_details.vue
- [ ] bundle/pages/bargain/bargain.vue

### Bundle_c Pages

- [ ] bundle_c/pages/team/team.vue
- [ ] bundle_c/pages/jiaocheng/jiaocheng.vue
- [ ] bundle_c/pages/jiaocheng/jiaocheng_chu.vue
- [ ] bundle_c/pages/fenhong/fenhong.vue
- [ ] bundle_c/pages/tixian/tixian.vue
- [ ] bundle_c/pages/webview/shop.vue
- [ ] bundle_c/pages/webview/product.vue
- [ ] bundle_c/pages/user_fen/fen.vue
- [ ] bundle_c/pages/user_money/money.vue
- [ ] bundle_c/pages/user_earnings/earnings.vue
- [ ] bundle_c/pages/user_love/love.vue
- [ ] bundle_c/pages/user_growth/growth.vue
- [ ] bundle_c/pages/user_goldcoin/goldcoin.vue
- [ ] bundle_c/pages/user_integral/integral.vue
- [ ] bundle_c/pages/user_daikuan/daikuan.vue
- [ ] bundle_c/pages/register/register.vue
- [ ] bundle_c/pages/forget_pwd/forget_pwd.vue
- [ ] bundle_c/pages/pay_password/pay_password.vue
- [ ] bundle_c/pages/goods_search/goods_search.vue
- [ ] bundle_c/pages/order_list/order_list.vue
- [ ] bundle_c/pages/ziliao_guanli/ziliao_guanli.vue
- [ ] bundle_c/pages/bank_list/bank_list.vue
- [ ] bundle_c/pages/addbank/addbank.vue
- [ ] bundle_c/pages/goods_list/goods_list.vue
- [ ] bundle_c/pages/chuchuang/chuchuang.vue
- [ ] bundle_c/pages/xiaoshou_yeji/xiaoshou_yeji.vue
- [ ] bundle_c/pages/jingying_team/jingying_team.vue
- [ ] bundle_c/pages/news_list/news_list.vue
- [ ] bundle_c/pages/news_details/news_details.vue

## Example Implementation

Here's an example of how to add the page title mixin to a page:

```javascript
// 1. Add the import statement
import pageTitleMixin from '@/mixins/page-title-mixin';

// 2. Add the mixin to the component's mixins array
export default {
  mixins: [pageTitleMixin, /* existing mixins */],
  
  // 3. Make sure the component has access to the currentLanguage getter
  computed: {
    ...mapGetters(['currentLanguage']), // Add this if not already present
    // other computed properties
  },
  
  // rest of component definition
};
```

## Verification

After adding the mixin to a page, you can verify that it works by:

1. Changing the language in the app
2. Navigating to the page
3. Checking that the page title is displayed in the correct language

## Notes

- Some pages may already have a mixins array. In that case, just add `pageTitleMixin` to the existing array.
- Some pages may already have the `currentLanguage` getter in their computed properties. In that case, you don't need to add it again.
- If a page doesn't have a computed section, you'll need to add one.
