import wechath5 from '@/utils/wechath5'
import { isWeixinClient, toast } from '@/utils/tools'
import { baseURL, basePath } from '@/config/app'
import { getGeocoder } from "@/api/store.js"

const state = {
    cityInfo: {
		// id: city_id,
		// name: result.ad_info.city,
		// gcj02_lat: result.location.lat,
		// gcj02_lng: result.location.lng
	}
};

const mutations = {
    setCityInfo(state, data) {
        state.cityInfo = data
		uni.$emit('refreshhome')
    }
};

const actions = {
	
};

export default {
    state,
    mutations,
    actions
};
