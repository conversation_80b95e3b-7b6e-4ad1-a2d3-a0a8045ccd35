import Cache from '@/utils/cache';

const LANGUAGE_KEY = 'LANGUAGE';

const state = {
  currentLanguage: Cache.get(LANGUAGE_KEY) || 'en' // Default to English
};

const mutations = {
  setLanguage(state, language) {
    state.currentLanguage = language;
    Cache.set(LANGUAGE_KEY, language);
  }
};

const actions = {
  changeLanguage({ commit }, language) {
    commit('setLanguage', language);
    // This will reload the page to apply the language change
    // We use setTimeout to ensure the language is saved before reloading
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/index/index'
      });
    }, 300);
  }
};

export default {
  state,
  mutations,
  actions
};
