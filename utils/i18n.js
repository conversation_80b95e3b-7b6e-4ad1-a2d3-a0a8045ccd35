import i18n from '@/locales';
import store from '@/store';
import Cache from '@/utils/cache';

/**
 * Change the application language
 * @param {string} language - The language code to switch to (e.g., 'en', 'zh-CN')
 * @returns {Promise} - A promise that resolves when the language has been changed
 */
export function changeLanguage(language) {
  return new Promise((resolve) => {
    // Set the i18n locale
    i18n.locale = language;

    // Update the Vuex store
    store.dispatch('changeLanguage', language);

    // Save the language preference to cache
    Cache.set('LANGUAGE', language);

    // Resolve the promise
    resolve();
  });
}

/**
 * Get the current language
 * @returns {string} - The current language code
 */
export function getCurrentLanguage() {
  return store.getters.currentLanguage || 'en';
}

/**
 * Get a translated string
 * @param {string} key - The translation key
 * @param {Object} params - Optional parameters for the translation
 * @returns {string} - The translated string
 */
export function t(key, params = {}) {
  return i18n.t(key, params);
}

export default {
  changeLanguage,
  getCurrentLanguage,
  t
};
