/**
 * Utility functions for handling page titles with i18n support
 */

// Map of page paths to i18n translation keys
const pageTitleMap = {
  // Main pages
  'pages/index/index': 'pageTitles.home',
  'pages/daren/daren': 'pageTitles.promoterTasks',
  'pages/i18n_test/i18n_test': 'pageTitles.i18nTest',
  'pages/ai/ai': 'pageTitles.ai',
  'pages/giveaway/giveaway': 'pageTitles.gift',
  'pages/withdrawal/withdrawal': 'pageTitles.withdraw',
  'pages/subsidy/subsidy': 'pageTitles.subsidy',
  'pages/bind_wallet/bind_wallet': 'pageTitles.bindWallet',
  'pages/sucaiku/sucaiku': 'pageTitles.materialLibrary',
  'pages/agent/agent': 'pageTitles.agent',
  'pages/topup/topup': 'pageTitles.recharge',
  'pages/user/user': 'pageTitles.userCenter',
  'pages/login/login': 'pageTitles.login',

  // Bundle pages
  'bundle/pages/language_settings/language_settings': 'settings.languageSettings',
  'bundle/pages/all_comments/all_comments': 'pageTitles.allComments',
  'bundle/pages/activity_detail/activity_detail': 'pageTitles.activityDetail',
  'bundle/pages/goods_seckill/goods_seckill': 'pageTitles.flashSale',
  'bundle/pages/user_vip/user_vip': 'pageTitles.memberCenter',
  'bundle/pages/user_order/user_order': 'user.myRental',
  'bundle/pages/order_details/order_details': 'pageTitles.orderDetails',
  'bundle/pages/user_address/user_address': 'pageTitles.shippingAddress',
  'bundle/pages/address_edit/address_edit': 'pageTitles.addAddress',
  'bundle/pages/user_coupon/user_coupon': 'pageTitles.myCoupons',
  'bundle/pages/user_collection/user_collection': 'pageTitles.myFavorites',
  'bundle/pages/after_sales/after_sales': 'pageTitles.afterSales',
  'bundle/pages/apply_refund/apply_refund': 'pageTitles.applyRefund',
  'bundle/pages/after_sales_detail/after_sales_detail': 'pageTitles.afterSalesDetail',
  'bundle/pages/input_express_info/input_express_info': 'pageTitles.enterTrackingNumber',
  'bundle/pages/user_profile/user_profile': 'pageTitles.userProfile',
  'bundle/pages/user_wallet/user_wallet': 'pageTitles.myWallet',
  'bundle/pages/user_payment/user_payment': 'pageTitles.userRecharge',
  'bundle/pages/contact_offical/contact_offical': 'pageTitles.contactCustomerService',
  'bundle/pages/server_explan/server_explan': 'pageTitles.servicePolicy',
  'bundle/pages/store_settled/store_settled': 'pageTitles.merchantSettlement',
  'bundle/pages/settled_result/settled_result': 'pageTitles.merchantSettlement',
  'bundle/pages/settled_recode/settled_recode': 'pageTitles.applicationRecord',
  'bundle/pages/goods_reviews/goods_reviews': 'pageTitles.productReviews',
  'bundle/pages/user_comment/user_comment': 'pageTitles.productReviewsList',
  'bundle/pages/goods_logistics/goods_logistics': 'pageTitles.logisticsDetails',
  'bundle/pages/user_spread/user_spread': 'pageTitles.distributionPromotion',
  'bundle/pages/monthly_bill/monthly_bill': 'pageTitles.monthlyBill',
  'bundle/pages/monthly_bill_detail/monthly_bill_detail': 'pageTitles.monthlyBillDetail',
  'bundle/pages/user_spread_order/user_spread_order': 'pageTitles.distributionOrders',
  'bundle/pages/user_withdraw/user_withdraw': 'pageTitles.withdraw',
  'bundle/pages/user_withdraw_code/user_withdraw_code': 'pageTitles.withdrawalRecord',
  'bundle/pages/widthdraw_result/widthdraw_result': 'pageTitles.withdrawalResult',
  'bundle/pages/invite_fans/invite_fans': 'pageTitles.invitePoster',
  'bundle/pages/download/download': 'pageTitles.downloadApp',
  'bundle/pages/user_bill/user_bill': 'pageTitles.accountDetails',
  'bundle/pages/user_growth/user_growth': 'pageTitles.growthValue',
  'bundle/pages/recharge_record/recharge_record': 'pageTitles.rechargeRecord',
  'bundle/pages/user_fans/user_fans': 'pageTitles.myFans',
  'bundle/pages/commission_details/commission_details': 'pageTitles.commissionDetails',
  'bundle/pages/bargain/bargain': 'pageTitles.bargainActivity',

  // Bundle_c pages
  'bundle_c/pages/team/team': 'user.myTeam',
  'bundle_c/pages/jiaocheng/jiaocheng': 'pageTitles.tutorial',
  'bundle_c/pages/jiaocheng/jiaocheng_chu': 'pageTitles.beginnerTutorial',
  'bundle_c/pages/fenhong/fenhong': 'pageTitles.dividend',
  'bundle_c/pages/tixian/tixian': 'pageTitles.withdraw',
  'bundle_c/pages/webview/shop': 'pageTitles.wechatStore',
  'bundle_c/pages/webview/product': 'pageTitles.wechatStore',
  'bundle_c/pages/user_fen/fen': 'pageTitles.managementBonus',
  'bundle_c/pages/user_money/money': 'pageTitles.accountBalance',
  'bundle_c/pages/user_earnings/earnings': 'pageTitles.contributionValue',
  'bundle_c/pages/user_love/love': 'userLove.myBalance',
  'bundle_c/pages/user_growth/growth': 'pageTitles.pendingSettlement',
  'bundle_c/pages/user_goldcoin/goldcoin': 'pageTitles.shoppingVouchers',
  'bundle_c/pages/user_integral/integral': 'pageTitles.myPoints',
  'bundle_c/pages/user_daikuan/daikuan': 'pageTitles.storeLoan',
  'bundle_c/pages/register/register': 'pageTitles.registerAccount',
  'bundle_c/pages/forget_pwd/forget_pwd': 'pageTitles.forgotPassword',
  'bundle_c/pages/pay_password/pay_password': 'pageTitles.setTransactionPassword',
  'bundle_c/pages/goods_search/goods_search': 'pageTitles.productSearch',
  'bundle_c/pages/order_list/order_list': 'pageTitles.myOrders',
  'bundle_c/pages/ziliao_guanli/ziliao_guanli': 'pageTitles.dataManagement',
  'bundle_c/pages/bank_list/bank_list': 'pageTitles.bankCardList',
  'bundle_c/pages/addbank/addbank': 'pageTitles.addBankCard',
  'bundle_c/pages/goods_list/goods_list': 'pageTitles.myProducts',
  'bundle_c/pages/chuchuang/chuchuang': 'pageTitles.myShowcase',
  'bundle_c/pages/xiaoshou_yeji/xiaoshou_yeji': 'pageTitles.promoterSalesRanking',
  'bundle_c/pages/jingying_team/jingying_team': 'pageTitles.eliteTeam',
  'bundle_c/pages/news_list/news_list': 'pageTitles.mallNews',
  'bundle_c/pages/news_details/news_details': 'pageTitles.newsDetails',
  'bundle_c/pages/goods_details/goods_details': 'pageTitles.productDetails',
  'bundle_c/pages/confirm_order/confirm_order': 'pageTitles.confirmOrder',
  'bundle_c/pages/payment/payment': 'pageTitles.orderPayment',
  'bundle_c/pages/pay_result/pay_result': 'pageTitles.payResult',
};

/**
 * Get the i18n translation key for a page title based on the current route
 * @param {string} pagePath - The current page path
 * @returns {string|null} - The i18n translation key or null if not found
 */
export function getPageTitleKey(pagePath) {
  // Remove any query parameters
  const path = pagePath.split('?')[0];

  // Return the mapped translation key or null if not found
  return pageTitleMap[path] || null;
}

/**
 * Set the page title based on the current route and i18n
 * @param {object} vm - The Vue component instance (this)
 * @param {string} [customTitleKey] - Optional custom title key to use instead of the mapped one
 */
export function setPageTitle(vm, customTitleKey) {
  // Get the current page route
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const pagePath = currentPage.route;

    // Get the title key from the map or use the custom one if provided
    const titleKey = customTitleKey || getPageTitleKey(pagePath);

    if (titleKey) {
      // Set the page title using i18n
      const title = vm.$t(titleKey);
      uni.setNavigationBarTitle({ title });
      console.log(`[i18n] Set page title to: ${title} (${titleKey})`);
    } else {
      console.log(`[i18n] No title mapping found for page: ${pagePath}`);
    }
  } catch (error) {
    console.error('[i18n] Error setting page title:', error);

    // If we have a custom title key, try to use it directly
    if (customTitleKey) {
      try {
        const title = vm.$t(customTitleKey);
        uni.setNavigationBarTitle({ title });
        console.log(`[i18n] Set page title to: ${title} (${customTitleKey}) using custom key`);
      } catch (innerError) {
        console.error('[i18n] Error setting page title with custom key:', innerError);
      }
    }
  }
}

export default {
  getPageTitleKey,
  setPageTitle
};
